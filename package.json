{"name": "evaluation_platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@heroicons/react": "^2.2.0", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "@types/jwt-decode": "^2.2.1", "ahooks": "^3.9.5", "antd": "^5.24.8", "axios": "^1.8.4", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "idb-keyval": "^6.2.2", "jwt-decode": "^2.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.30.0", "three": "^0.175.0", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/vite": "^4.1.13", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}