# utils/csv_parser.py

import csv
import os
import json
import re


def parse_csv_to_evaluation_case_pool(file_path):
    """
    解析CSV文件为评测集数据格式
    CSV格式必须包含以下列：
    - pkl_name: PKL文件名
    - pkl_dir: PKL目录
    
    以下列为可选：
    - key_obs_id: 关键观测点ID (默认为0)
    - path_range_start: 路径范围开始 (默认为0)
    - path_range_end: 路径范围结束 (默认为10)
    """
    vin_pattern = r"[A-Z0-9]{17}"
    # vehicle type 一位大写字母+两位数字 一定出现在字符串的开头
    vehicle_type_pattern = r"^ [A-Z]\d{2}"
    time_ns_pattern = r"\d{19}"
    if not os.path.exists(file_path):
        return {"success": False, "error": f"文件不存在: {file_path}"}

    evaluation_sets = []
    try:
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            # 验证CSV格式 - 只检查必要字段
            required_fields = ['pkl_name', 'pkl_dir']
            if not all(field in reader.fieldnames for field in required_fields):
                missing = [
                    f for f in required_fields if f not in reader.fieldnames]
                return {"success": False, "error": f"CSV缺少必要字段: {', '.join(missing)}"}

            # 解析每一行
            for row in reader:
                try:
                    # 处理路径范围 (若有)
                    path_range = [0, 10]  # 默认值
                    if 'path_range_start' in row and row['path_range_start'].strip():
                        path_range[0] = int(row['path_range_start'])
                    if 'path_range_end' in row and row['path_range_end'].strip():
                        path_range[1] = int(row['path_range_end'])
                    time_ns = re.search(
                        time_ns_pattern, row['pkl_name'])
                    vehicle_type = re.search(
                        vehicle_type_pattern, row['pkl_name'])
                    vin_type = re.search(
                        vin_pattern, row['pkl_name'])
                    # 创建评测集数据
                    evaluation_case_pool = {
                        'vin': vin_type.group(0),
                        'vehicle_type': vehicle_type.group(0) if vehicle_type else 'C10',
                        'time_ns': time_ns.group(0) if time_ns else None,
                        'pkl_dir': row['pkl_dir'],
                        'key_obs_id': int(row.get('key_obs_id', 0)) if row.get('key_obs_id', '').strip() else 0,
                        'path_range': path_range,
                        'pkl_name': row['pkl_name'],
                    }
                    evaluation_sets.append(evaluation_case_pool)
                except (ValueError, KeyError) as e:
                    return {"success": False, "error": f"解析行 {row} 时出错: {str(e)}"}
        return {"success": True, "data": evaluation_sets}
    except Exception as e:
        return {"success": False, "error": f"解析CSV文件时出错: {str(e)}"}
