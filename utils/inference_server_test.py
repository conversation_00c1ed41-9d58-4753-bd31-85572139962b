# inference_client.py
import grpc
import os
import sys
import time
import pickle
import numpy as np  # 用于创建示例 pickle 数据

# --- 设置项目路径 (根据你的项目结构调整) ---
# 假设此脚本位于与 inference_server.py 相同的目录或其子目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
IMPORT_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, '..'))
if IMPORT_DIR not in sys.path:
    sys.path.append(IMPORT_DIR)
print(f"Adding {IMPORT_DIR} to sys.path")

# 确保可以导入 protobuf 生成的文件
# fmt: off
import proto.inference_server_pb2 as inference_server_pb2
import proto.inference_server_pb2_grpc as inference_server_pb2_grpc
import proto.inference_result_pb2 as inference_result_pb2
# fmt: on

# --- Helper function to create a dummy pickle file ---


def create_dummy_pickle(filepath="dummy_input.pkl"):
    """
    创建一个包含示例数据的 pickle 文件。
    *** 你必须修改此函数以创建符合你的模型 preprocessor 输入要求的真实数据结构 ***
    """
    # 这是一个非常基础的示例，很可能与你的模型不兼容
    # 你需要根据 preprocessor 的期望输入来构建这个字典
    dummy_data = {
        # 示例：(batch, num_obj, features)
        'feature_obj': np.random.rand(1, 10, 5).astype(np.float32),
        # 示例：(batch, channels, H, W)
        'feature_map': np.random.rand(1, 3, 200, 200).astype(np.float32),
        # 示例：(batch, time, xy)
        'agent_traj_hist': np.random.rand(1, 11, 2).astype(np.float32),
        # 添加你的 preprocessor 需要的其他所有键值对...
        # 例如: 'agent_mask', 'lane_graph', etc.
        'request_id': 'dummy_request_from_pickle'  # 可以包含元数据
    }
    try:
        with open(filepath, 'wb') as f:
            pickle.dump(dummy_data, f)
        print(f"Created dummy pickle file: {filepath}")
        return True
    except Exception as e:
        print(f"Error creating dummy pickle file: {e}")
        return False

# --- Test LoadModel ---


def test_load_model(stub: inference_server_pb2_grpc.InferenceServiceStub):
    print("\n--- Testing LoadModel ---")
    # *** 修改这里的路径为你实际的配置文件和模型文件 ***
    config = inference_server_pb2.InferenceConfig(
        json_config_file="/extra-data/Prediction/46462/code-space/leapprediction/json/0415_del_test_bag_baseline.json",  # <--- 修改这里
        # <--- 修改这里
        pth_file="/extra-data/Prediction/42060/repo/debug/pdp_weight/0415_del_test_bag_baseline-99.pth",
        nearest_negative_anchor_num=1,              # 示例值
        other_negative_anchor_num=0                 # 示例值
    )
    request = inference_server_pb2.LoadModelRequest(config=config)
    try:
        response = stub.LoadModel(request, timeout=120)  # 增加超时时间
        print(
            f"LoadModel Response: success={response.success}, message='{response.message}'")
        return response.success
    except grpc.RpcError as e:
        print(f"LoadModel RPC failed: {e.code()} - {e.details()}")
        return False
    except Exception as e:
        print(f"An unexpected error occurred during LoadModel: {e}")
        return False


# --- Test Infer ---
def test_infer(stub: inference_server_pb2_grpc.InferenceServiceStub, pickle_path="dummy_input.pkl"):
    print("\n--- Testing Infer ---")
    if not os.path.exists(pickle_path):
        print(f"Pickle file '{pickle_path}' not found. Cannot run Infer test.")
        return

    request_id = f"client_test_{int(time.time())}"
    request = inference_server_pb2.InferenceRequest(
        pickle_file_path=os.path.abspath(pickle_path),  # 使用绝对路径
        request_id=request_id
    )
    try:
        start_time = time.time()
        response = stub.Infer(request, timeout=60)  # 设置超时
        end_time = time.time()
        print(f"Infer RPC call took {end_time - start_time:.4f} seconds.")

        # 打印一些响应信息以确认
        print(f"Infer Response Version: {response.version}")
        if response.version.startswith("error"):
            print(f"Inference returned an error state: {response.version}")
            return

        print(
            f"Number of agent predictions: {len(response.agent_predictions)}")
        if len(response.agent_predictions) > 0:
            print(
                f"  Agent 0 has {len(response.agent_predictions[0].trajectories)} trajectory proposals.")
            if len(response.agent_predictions[0].trajectories) > 0:
                print(
                    f"    Proposal 0 probability: {response.agent_predictions[0].trajectories[0].probability:.4f}")
                print(
                    f"    Proposal 0 has {len(response.agent_predictions[0].trajectories[0].trajectory.points)} points.")

        print(
            f"Anchor-Free Paths: {len(response.anchor_free_pathformer.paths)}")
        print(
            f"Anchor-Based Paths: {len(response.anchor_based_pathformer.paths)}")
        print(f"Decision DLPs: {len(response.decision)}")

    except grpc.RpcError as e:
        print(f"Infer RPC failed: {e.code()} - {e.details()}")
    except Exception as e:
        print(f"An unexpected error occurred during Infer: {e}")


# --- Main Execution ---
if __name__ == '__main__':
    server_address = 'localhost:50051'
    pickle_file = "/extra-data/Prediction/46462/code-space/leapprediction/test.pkl"

    # 2. 连接到服务器
    print(f"Connecting to gRPC server at {server_address}...")
    try:
        # 使用非安全连接，与服务器的 add_insecure_port 对应
        channel = grpc.insecure_channel(server_address)
        # 快速检查连接状态 (可选，但有助于调试)
        try:
            grpc.channel_ready_future(channel).result(timeout=5)
            print("Channel is ready.")
        except grpc.FutureTimeoutError:
            print(
                f"Error: Could not connect to server at {server_address} within timeout.")
            sys.exit(1)

        stub = inference_server_pb2_grpc.InferenceServiceStub(channel)

        # 3. 测试 LoadModel
        model_loaded = test_load_model(stub)

        # 4. 如果模型加载成功，测试 Infer
        if model_loaded:
            test_infer(stub, pickle_file)
        else:
            print("Skipping Infer test because model loading failed.")

        # 5. 关闭连接
        print("\nClosing gRPC channel.")
        channel.close()

    except Exception as e:
        print(f"An error occurred in the client: {e}")

    # 6. 清理虚拟 pickle 文件
    # if os.path.exists(pickle_file):
    #     try:
    #         os.remove(pickle_file)
    #         print(f"Removed dummy pickle file: {pickle_file}")
    #     except OSError as e:
    #         print(f"Error removing dummy pickle file: {e}")

    print("\nClient finished.")
