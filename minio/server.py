import os
import subprocess
import time


def setup_minio():
    """安装并设置MinIO服务器"""

    # 创建MinIO数据目录
    minio_data_dir = "/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/minio_data"
    os.makedirs(minio_data_dir, exist_ok=True)

    # 检查MinIO是否已安装
    # try:
    #     subprocess.check_output(["which", "minio"])
    #     print("MinIO已安装")
    # except subprocess.CalledProcessError:
    #     print("安装MinIO...")
    #     subprocess.run(
    #         ["wget", "https://dl.min.io/server/minio/release/linux-amd64/archive/minio_20230601052349.0.0_amd64.deb", "-O", "minio.deb"], check=True)
    #     subprocess.run(["sudo", "dpkg", "-i", "minio.deb"], check=True)
    #     subprocess.run(["rm", "minio.deb"], check=True)

    # 检查是否已经有MinIO运行
    try:
        result = subprocess.check_output(["pgrep", "minio"]).decode().strip()
        print(f"MinIO已在运行，进程ID: {result}")
    except subprocess.CalledProcessError:
        # 启动MinIO服务器
        print("启动MinIO服务器...")
        minio_process = subprocess.Popen([
            "minio", "server", minio_data_dir,
            "--console-address", ":9001",
            "--address", ":9000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

        # 等待MinIO启动
        time.sleep(3)

        if minio_process.poll() is None:
            print("MinIO服务器已成功启动")
        else:
            stdout, stderr = minio_process.communicate()
            print(f"MinIO启动失败: {stderr.decode()}")
            raise Exception("MinIO启动失败")

    # 安装Python客户端
    try:
        subprocess.check_call(["pip", "install", "minio"])
        print("MinIO Python客户端已安装")
    except subprocess.CalledProcessError:
        print("安装MinIO Python客户端失败")
        raise


if __name__ == "__main__":
    setup_minio()

    print("\nMinIO访问信息:")
    print("API 端点: http://localhost:9000")
    print("控制台: http://localhost:9001")
    print("默认访问密钥: minioadmin")
    print("默认密钥: minioadmin")
    print("\n使用以下命令访问控制台进行进一步配置")
