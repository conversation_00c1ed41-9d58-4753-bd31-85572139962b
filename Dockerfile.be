FROM lpdocker.leapmotor.com/open-repo/docker/library/python:3.10.18-bookworm


COPY . /app/

RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources \
    && apt update \
    && apt install -y libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/* \
    && pip install --no-cache-dir -r /app/requirements.txt -i http://mirrors.leapmotor.com/repository/pypi/simple --trusted-host mirrors.leapmotor.com

WORKDIR /app

CMD ["python", "main.py"]

