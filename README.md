# 评估平台 (Evaluation Platform)

## 项目概述

评估平台是一个用于自动驾驶算法评估的Web应用系统，支持评估案例管理、推理配置、评估指标计算和可视化等功能。系统采用前后端分离架构，前端使用React + TypeScript，后端使用FastAPI + Python。

## 技术栈

### 前端
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI库**: Ant Design
- **3D可视化**: Three.js
- **状态管理**: 原生React State + Context

### 后端
- **框架**: FastAPI
- **数据库**: MySQL
- **ORM**: 原生SQL + PyMySQL
- **通信协议**: gRPC (Protocol Buffers)
- **异步处理**: asyncio

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                        前端 (React)                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  数据集管理  │  │  推理配置    │  │  可视化组件  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  评估任务    │  │  指标展示    │  │  标注工具    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                            │ HTTP/JSON API
┌─────────────────────────────────────────────────────────────┐
│                      后端 (FastAPI)                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  数据处理API │  │  评估流程API │  │  可视化API   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  标注API     │  │  有效性检查  │  │  认证API     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                            │ SQL
┌─────────────────────────────────────────────────────────────┐
│                       数据库 (MySQL)                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  评估案例池  │  │  推理配置    │  │  评估结果    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  数据集      │  │  标注数据    │  │  Bag/Pickle │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 核心功能

### 1. 数据管理
- **评估案例池**: 管理pkl格式的评估数据文件
- **数据集管理**: 创建和管理评估数据集
- **Bag/Pickle信息**: 存储原始数据文件的元信息

### 2. 推理配置
- **配置管理**: 管理推理模型的参数配置
- **版本控制**: 支持配置的版本管理和切换

### 3. 评估流程
- **任务创建**: 批量创建评估任务
- **流程监控**: 监控推理和评估进度
- **结果存储**: 存储推理和评估结果

### 4. 可视化分析
- **3D可视化**: 使用Three.js展示点云和轨迹数据
- **指标仪表板**: 展示ADE、FDE等评估指标
- **数据探索**: 支持交互式数据查看

### 5. 标注工具
- **PDP路径标注**: 支持路径决策点质量标注（好/差/未知）
- **路径对比标注**: 支持路径配对比较标注
- **数据验证**: 标注数据的有效性检查
- **标注员管理**: 支持多标注员协作和权限管理

## 数据库设计

### 核心表结构

#### 评估案例池 (evaluation_case_pool)
```sql
CREATE TABLE evaluation_case_pool (
    id INT PRIMARY KEY AUTO_INCREMENT,
    pkl_dir VARCHAR(255) NOT NULL,
    pkl_name VARCHAR(255) NOT NULL,
    vehicle_type VARCHAR(50),
    vin VARCHAR(50),
    time_ns BIGINT,
    key_obs_id INT DEFAULT 0,
    path_range JSON,
    dirty_data BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 推理配置 (inference_config)
```sql
CREATE TABLE inference_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_name VARCHAR(100) NOT NULL,
    json_config_file VARCHAR(255),
    pth_file VARCHAR(255),
    nearest_negative_anchor_num INT DEFAULT 25,
    other_negative_anchor_num INT DEFAULT 6,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 评估数据集 (evaluation_sets)
```sql
CREATE TABLE evaluation_sets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    set_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Bag信息表 (bag_infos)
```sql
CREATE TABLE bag_infos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bag_name VARCHAR(255) NOT NULL UNIQUE,
    date DATE NOT NULL,
    version ENUM('3.0', '3.5') NOT NULL,
    mode ENUM('CNAP', 'URP') NOT NULL,
    vin VARCHAR(50),
    origin_dir VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```


### 关系说明
- `evaluation_case_pool` ↔ `evaluation_sets`: 多对多关系 (通过中间表)
- `inference_result` 关联 `evaluation_case_pool` 和 `inference_config`
- `evaluation_result` 基于 `inference_result` 生成
- `pickle_infos` 关联 `bag_infos` (多对一)

## PDP标注系统

### 系统概述
PDP（Path Decision Point）标注系统是评估平台的核心功能之一，用于对自动驾驶算法生成的路径进行质量标注。系统支持多标注员协作，提供直观的可视化界面和完整的标注数据管理功能。

### 核心组件

#### 1. 前端组件

##### PdpPathAnnotation 页面组件
- **文件位置**: `src/pages/PdpPathAnnotation.tsx`
- **主要功能**:
  - PKL文件列表管理和筛选
  - 3D路径可视化展示
  - 路径质量标注界面
  - 标注统计和进度跟踪
  - 脏数据标记功能

##### PickleVisualizer 可视化组件
- **文件位置**: `src/components/PickleVisualizer.tsx`
- **主要功能**:
  - 3D点云和路径渲染
  - 交互式路径高亮
  - 相机控制和视角切换
  - 路径索引显示


#### 2. 后端API

##### PDP标注API
- **文件位置**: `api/annotation/pdp_annotation.py`
- **核心接口**:
  ```python
  # 获取PKL文件的PDP路径数据
  GET /api/annotation/pdp-paths/{pkl_id}

  # 提交路径标注
  POST /api/annotation/pdp-paths

  # 导出标注数据
  GET /api/annotation/export-annotations/{evaluation_set_id}

  # 标记脏数据
  POST /api/annotation/mark-dirty
  ```



## 部署指南

### 环境要求
- **Python**: 3.10
- **Node.js**: 16+
- **MySQL**: 8.0+
- **操作系统**: Linux/MacOS

### 1. 克隆项目
```bash
git clone <repository-url>
cd evaluation_platform
```

### 2. 后端部署

#### 安装依赖
```bash

# 安装依赖
pip install -r requirements.txt
```

#### 数据库配置
```bash
# 数据库服务器放在************** mysql
```


#### 启动后端服务
```bash
# 开发环境
python main.py
```

### 3. 前端部署

#### 安装依赖
```bash
npm install
```

#### 构建和启动
```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview
```
## 开发指南

### 项目结构
```
evaluation_platform/
├── api/                    # 后端API
│   ├── dataset/           # 数据集管理
│   ├── evaluation_pipeline/ # 评估流程
│   ├── data_process/      # 数据处理
│   ├── annotation/        # 标注工具
│   └── visualize/         # 可视化
├── database/              # 数据库相关
│   ├── db_operations.py   # 数据库操作
│   └── *.sql             # 初始化脚本
├── proto/                 # gRPC协议文件
├── src/                   # 前端源码
│   ├── components/        # React组件
│   ├── pages/            # 页面组件
│   ├── hooks/            # 自定义Hooks
│   └── utils/            # 工具函数
├── static/               # 静态资源
├── logs/                 # 日志文件
└── main.py              # 应用入口
```

### 关键文件说明
- [`main.py`](main.py): FastAPI应用入口和路由配置
- [`database/db_operations.py`](database/db_operations.py): 数据库操作封装



### 数据备份
```bash
# 数据库备份
mysqldump -u username -p my_eval_db > backup_$(date +%Y%m%d).sql

# 定期备份脚本
#!/bin/bash
BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u username -p my_eval_db | gzip > $BACKUP_DIR/eval_db_$DATE.sql.gz
```

### 性能监控
```bash
# 系统资源监控
htop
iostat -x 1

# 数据库性能
mysql -e "SHOW PROCESSLIST;"
mysql -e "SHOW ENGINE INNODB STATUS\G"
```

## 联系信息

- **项目维护者**: [杨若虚]
- **技术支持**: [<EMAIL>]
- **文档更新**: 2025年7月9日

---

**注意**: 本项目正在持续开发中，建议定期查看更新和安全补丁。部署前请确保所有依赖版本兼容。