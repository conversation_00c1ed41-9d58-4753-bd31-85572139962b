#FROM lpdocker.leapmotor.com/open-repo/docker/library/node:20 as builder
FROM itharbor.leapmotor.com/tools/node:20.10.0 as builder

WORKDIR /app

COPY . /app

RUN npm config set registry https://lprepo.leapmotor.com/npm/ps/npm/ \
    && npm install typescript -g \
    && npm install \
    && npm run build

FROM lpdocker.leapmotor.com/open-repo/docker/library/nginx:1.29.1-alpine

COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /app/dist /usr/share/nginx/html
