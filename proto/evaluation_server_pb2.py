# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: proto/evaluation_server.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'proto/evaluation_server.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from proto import evaluation_result_pb2 as proto_dot_evaluation__result__pb2
from proto import inference_result_pb2 as proto_dot_inference__result__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dproto/evaluation_server.proto\x12\x12\x65valuation_service\x1a\x1dproto/evaluation_result.proto\x1a\x1cproto/inference_result.proto\"~\n\x11\x45valuationRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x18\n\x10pickle_file_path\x18\x02 \x01(\t\x12;\n\x10inference_result\x18\x03 \x01(\x0b\x32!.inference_result.InferenceOutput\"\x0f\n\rHealthRequest\"4\n\x0eHealthResponse\x12\x11\n\tavailable\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t2\xbc\x01\n\x11\x45valuationService\x12V\n\x08\x45valuate\x12%.evaluation_service.EvaluationRequest\x1a#.evaluation_result.EvaluationResult\x12O\n\x06Health\x12!.evaluation_service.HealthRequest\x1a\".evaluation_service.HealthResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto.evaluation_server_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_EVALUATIONREQUEST']._serialized_start=114
  _globals['_EVALUATIONREQUEST']._serialized_end=240
  _globals['_HEALTHREQUEST']._serialized_start=242
  _globals['_HEALTHREQUEST']._serialized_end=257
  _globals['_HEALTHRESPONSE']._serialized_start=259
  _globals['_HEALTHRESPONSE']._serialized_end=311
  _globals['_EVALUATIONSERVICE']._serialized_start=314
  _globals['_EVALUATIONSERVICE']._serialized_end=502
# @@protoc_insertion_point(module_scope)
