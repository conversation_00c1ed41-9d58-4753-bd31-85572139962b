# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from proto import inference_result_pb2 as proto_dot_inference__result__pb2
from proto import inference_server_pb2 as proto_dot_inference__server__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in proto/inference_server_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class InferenceServiceStub(object):
    """修改服务定义
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.LoadModel = channel.unary_unary(
                '/inference_service.InferenceService/LoadModel',
                request_serializer=proto_dot_inference__server__pb2.LoadModelRequest.SerializeToString,
                response_deserializer=proto_dot_inference__server__pb2.LoadModelResponse.FromString,
                _registered_method=True)
        self.Infer = channel.unary_unary(
                '/inference_service.InferenceService/Infer',
                request_serializer=proto_dot_inference__server__pb2.InferenceRequest.SerializeToString,
                response_deserializer=proto_dot_inference__server__pb2.InferenceResponse.FromString,
                _registered_method=True)
        self.StreamResults = channel.unary_stream(
                '/inference_service.InferenceService/StreamResults',
                request_serializer=proto_dot_inference__server__pb2.StreamResultsRequest.SerializeToString,
                response_deserializer=proto_dot_inference__result__pb2.InferenceOutput.FromString,
                _registered_method=True)


class InferenceServiceServicer(object):
    """修改服务定义
    """

    def LoadModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Infer(self, request, context):
        """修改返回值为简单响应
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamResults(self, request, context):
        """新增流式结果接收接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_InferenceServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'LoadModel': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadModel,
                    request_deserializer=proto_dot_inference__server__pb2.LoadModelRequest.FromString,
                    response_serializer=proto_dot_inference__server__pb2.LoadModelResponse.SerializeToString,
            ),
            'Infer': grpc.unary_unary_rpc_method_handler(
                    servicer.Infer,
                    request_deserializer=proto_dot_inference__server__pb2.InferenceRequest.FromString,
                    response_serializer=proto_dot_inference__server__pb2.InferenceResponse.SerializeToString,
            ),
            'StreamResults': grpc.unary_stream_rpc_method_handler(
                    servicer.StreamResults,
                    request_deserializer=proto_dot_inference__server__pb2.StreamResultsRequest.FromString,
                    response_serializer=proto_dot_inference__result__pb2.InferenceOutput.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'inference_service.InferenceService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('inference_service.InferenceService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class InferenceService(object):
    """修改服务定义
    """

    @staticmethod
    def LoadModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference_service.InferenceService/LoadModel',
            proto_dot_inference__server__pb2.LoadModelRequest.SerializeToString,
            proto_dot_inference__server__pb2.LoadModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Infer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/inference_service.InferenceService/Infer',
            proto_dot_inference__server__pb2.InferenceRequest.SerializeToString,
            proto_dot_inference__server__pb2.InferenceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StreamResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/inference_service.InferenceService/StreamResults',
            proto_dot_inference__server__pb2.StreamResultsRequest.SerializeToString,
            proto_dot_inference__result__pb2.InferenceOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
