syntax = "proto3";

import "proto/inference_result.proto"; // 导入现有的结果定义

package inference_service;

// 推理配置参数
message InferenceConfig {
  string json_config_file = 1; // 模型和处理器配置的 JSON 文件路径
  string pth_file = 2;         // 模型权重文件路径
  int32 nearest_negative_anchor_num = 3;
  int32 other_negative_anchor_num = 4;
  // 可以根据需要添加其他来自 argparse 的配置，例如设备 (cpu/cuda)
  // string device = 5;
}

// 加载模型的请求
message LoadModelRequest {
  InferenceConfig config = 1;
}

// 加载模型的响应 (可以为空或包含状态)
message LoadModelResponse {
  bool success = 1;
  string message = 2;
}

// 单次推理请求 - 传入包含预处理输入的 pickle 文件路径
message InferenceRequest {
  string pickle_file_path = 1; // 指向包含预处理输入的 pickle 文件的路径
  string request_id = 100;     // 用于追踪请求
}
// 添加新的请求和响应消息
message InferenceResponse {
    string request_id = 1;
    bool accepted = 2;
  }
  
message StreamResultsRequest {
repeated string request_ids = 1;
int32 timeout_ms = 2;
}

// 修改服务定义
service InferenceService {
rpc LoadModel(LoadModelRequest) returns (LoadModelResponse);
// 修改返回值为简单响应
rpc Infer(InferenceRequest) returns (InferenceResponse);
// 新增流式结果接收接口
rpc StreamResults(StreamResultsRequest) returns (stream inference_result.InferenceOutput);
}