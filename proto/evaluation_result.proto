syntax = "proto3";

package evaluation_result;

// 定义 N-th Class 枚举
enum PathClass {
  PATH_CLASS_UNKNOWN = 0;
  PATH_CLASS_1 = 1;
  PATH_CLASS_2 = 2;
  PATH_CLASS_3 = 3;
  PATH_CLASS_4 = 4;
  PATH_CLASS_5 = 5;
  PATH_CLASS_6 = 6;
}

enum DlpClass {
  DLP_CLASS_UNKNOWN = 0;
  DLP_CLASS_1 = 1;
  DLP_CLASS_2 = 2;
  DLP_CLASS_3 = 3;
  DLP_CLASS_4 = 4;
  DLP_CLASS_5 = 5;
  DLP_CLASS_6 = 6;
}

// 每个 Top K 的 float型指标 (ADE/FDE)
message TopKMetrics {
  float top_1 = 1;
  float top_3 = 2;
  float top_6 = 3;
  int32 count = 4;
}

// 每个 Top K 的 bool型指标 (Collision)
message TopKBoolMetrics {
  bool top_1 = 1;
  bool top_3 = 2;
  bool top_6 = 3;
  int32 count = 4;
}

// 每个路径类别的 float指标 (ADE/FDE)
message PathClassFloatMetrics {
  PathClass path_class = 1;
  float ade = 2;
  float fde = 3;
  int32 count = 4;
}

// 每个路径类别的 bool指标 (Collision)
message PathClassBoolMetrics {
  PathClass path_class = 1;
  bool collision = 2;
  int32 count = 3;
}

// 每个dlp类别的 float指标 (ADE/FDE)
message DlpClassFloatMetrics {
  DlpClass dlp_class = 1;
  float ade = 2;
  float fde = 3;
  int32 count = 4;
}

// 每个dlp类别的 bool指标 (Collision)
message DlpClassBoolMetrics {
  DlpClass dlp_class = 1;
  bool collision = 2;
  int32 count = 3;
}

// 评测结果主消息体
message EvaluationResult {
  TopKMetrics prediction_accuracy_ade = 1;
  TopKMetrics prediction_accuracy_fde = 2;
  
  TopKMetrics pathformer_accuracy_40_ade = 3;
  TopKMetrics pathformer_accuracy_40_fde = 4;
  TopKMetrics pathformer_accuracy_200_ade = 5;
  TopKMetrics pathformer_accuracy_200_fde = 6;
  
  TopKMetrics decision_accuracy_ade = 7;
  TopKMetrics decision_accuracy_fde = 8;
  
  TopKBoolMetrics static_4s_collision = 9;
  
  TopKMetrics pathformer_accuracy4s_ade = 10;
  TopKMetrics pathformer_accuracy4s_fde = 11;

  repeated PathClassFloatMetrics nth_cls_path_res_adefde = 12;
  repeated PathClassBoolMetrics nth_cls_path_res_static_collision = 13;

  repeated DlpClassFloatMetrics nth_cls_dlp_res_adefde = 14;
  repeated DlpClassBoolMetrics nth_cls_dlp_res_dynamic_collision = 15;
}
