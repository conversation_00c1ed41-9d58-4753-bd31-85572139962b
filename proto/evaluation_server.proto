syntax = "proto3";

package evaluation_service;

import "proto/evaluation_result.proto";
import "proto/inference_result.proto";
// 评估服务定义
service EvaluationService {
  // 评估单个推理结果
  rpc Evaluate(EvaluationRequest) returns (evaluation_result.EvaluationResult);
  
  // 检查服务是否可用
  rpc Health(HealthRequest) returns (HealthResponse);
}

// 评估请求
message EvaluationRequest {
  string request_id = 1;         // 请求ID
  string pickle_file_path = 2;   // 原始pkl文件路径
  inference_result.InferenceOutput inference_result = 3; // 推理结果文件路径
}

// 健康检查请求
message HealthRequest {
}

// 健康检查响应
message HealthResponse {
  bool available = 1;            // 服务是否可用
  string message = 2;            // 附加信息
}