# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from proto import evaluation_result_pb2 as proto_dot_evaluation__result__pb2
from proto import evaluation_server_pb2 as proto_dot_evaluation__server__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in proto/evaluation_server_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class EvaluationServiceStub(object):
    """评估服务定义
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Evaluate = channel.unary_unary(
                '/evaluation_service.EvaluationService/Evaluate',
                request_serializer=proto_dot_evaluation__server__pb2.EvaluationRequest.SerializeToString,
                response_deserializer=proto_dot_evaluation__result__pb2.EvaluationResult.FromString,
                _registered_method=True)
        self.Health = channel.unary_unary(
                '/evaluation_service.EvaluationService/Health',
                request_serializer=proto_dot_evaluation__server__pb2.HealthRequest.SerializeToString,
                response_deserializer=proto_dot_evaluation__server__pb2.HealthResponse.FromString,
                _registered_method=True)


class EvaluationServiceServicer(object):
    """评估服务定义
    """

    def Evaluate(self, request, context):
        """评估单个推理结果
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Health(self, request, context):
        """检查服务是否可用
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EvaluationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Evaluate': grpc.unary_unary_rpc_method_handler(
                    servicer.Evaluate,
                    request_deserializer=proto_dot_evaluation__server__pb2.EvaluationRequest.FromString,
                    response_serializer=proto_dot_evaluation__result__pb2.EvaluationResult.SerializeToString,
            ),
            'Health': grpc.unary_unary_rpc_method_handler(
                    servicer.Health,
                    request_deserializer=proto_dot_evaluation__server__pb2.HealthRequest.FromString,
                    response_serializer=proto_dot_evaluation__server__pb2.HealthResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'evaluation_service.EvaluationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('evaluation_service.EvaluationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class EvaluationService(object):
    """评估服务定义
    """

    @staticmethod
    def Evaluate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/evaluation_service.EvaluationService/Evaluate',
            proto_dot_evaluation__server__pb2.EvaluationRequest.SerializeToString,
            proto_dot_evaluation__result__pb2.EvaluationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Health(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/evaluation_service.EvaluationService/Health',
            proto_dot_evaluation__server__pb2.HealthRequest.SerializeToString,
            proto_dot_evaluation__server__pb2.HealthResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
