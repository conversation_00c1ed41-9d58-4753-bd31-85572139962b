# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: proto/inference_result.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'proto/inference_result.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cproto/inference_result.proto\x12\x10inference_result\"J\n\x0fTrajectoryPoint\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0f\n\x07heading\x18\x03 \x01(\x02\x12\x10\n\x08velocity\x18\x04 \x01(\x02\"?\n\nTrajectory\x12\x31\n\x06points\x18\x01 \x03(\x0b\x32!.inference_result.TrajectoryPoint\"\x8b\x01\n\x12TrajectoryWithProb\x12\x30\n\ntrajectory\x18\x01 \x01(\x0b\x32\x1c.inference_result.Trajectory\x12\x13\n\x0bprobability\x18\x02 \x01(\x02\x12\x19\n\x11\x63utin_probability\x18\x03 \x01(\x02\x12\x13\n\x0bobstacle_id\x18\x04 \x01(\x05\"M\n\x0f\x41gentPrediction\x12:\n\x0ctrajectories\x18\x01 \x03(\x0b\x32$.inference_result.TrajectoryWithProb\"Q\n\x1a\x41nchorFreePathformerOutput\x12\x33\n\x05paths\x18\x01 \x03(\x0b\x32$.inference_result.TrajectoryWithProb\"\x8b\x01\n\x1b\x41nchorBasedPathformerOutput\x12\x33\n\x05paths\x18\x01 \x03(\x0b\x32$.inference_result.TrajectoryWithProb\x12\x37\n\x0c\x66inal_points\x18\x02 \x03(\x0b\x32!.inference_result.TrajectoryPoint\"F\n\x19\x44\x65\x63isionLongitudinalPoint\x12\t\n\x01s\x18\x01 \x03(\x02\x12\t\n\x01v\x18\x02 \x03(\x02\x12\x13\n\x0bprobability\x18\x03 \x01(\x02\"^\n\x0e\x44\x65\x63isionOutput\x12\x38\n\x03\x64lp\x18\x01 \x03(\x0b\x32+.inference_result.DecisionLongitudinalPoint\x12\x12\n\npath_index\x18\x02 \x01(\x05\"\x85\x03\n\x0fInferenceOutput\x12\x0f\n\x07version\x18\x01 \x01(\t\x12<\n\x11\x61gent_predictions\x18\x02 \x03(\x0b\x32!.inference_result.AgentPrediction\x12L\n\x16\x61nchor_free_pathformer\x18\x03 \x01(\x0b\x32,.inference_result.AnchorFreePathformerOutput\x12N\n\x17\x61nchor_based_pathformer\x18\x04 \x01(\x0b\x32-.inference_result.AnchorBasedPathformerOutput\x12\x32\n\x08\x64\x65\x63ision\x18\x05 \x03(\x0b\x32 .inference_result.DecisionOutput\x12=\n\x11trajectory_output\x18\x06 \x01(\x0b\x32\".inference_result.TrajectoryOutput\x12\x12\n\nrequest_id\x18\x07 \x01(\t\"9\n\x0bTensorProto\x12\r\n\x05shape\x18\x01 \x03(\x05\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\x12\r\n\x05\x64type\x18\x03 \x01(\t\"\xb5\x05\n\x10TrajectoryOutput\x12<\n\x15prediction_trajectory\x18\x01 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12<\n\x15prediction_classifier\x18\x02 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12\x37\n\x10\x63utin_classifier\x18\x03 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12H\n!anchor_free_pathformer_trajectory\x18\x04 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12H\n!anchor_free_pathformer_classifier\x18\x05 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12I\n\"anchor_based_pathformer_trajectory\x18\x06 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12J\n#anchor_based_pathformer_final_point\x18\x07 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12I\n\"anchor_based_pathformer_classifier\x18\x08 \x01(\x0b\x32\x1d.inference_result.TensorProto\x12:\n\x13\x64\x65\x63ision_trajectory\x18\t \x01(\x0b\x32\x1d.inference_result.TensorProto\x12:\n\x13\x64\x65\x63ision_classifier\x18\n \x01(\x0b\x32\x1d.inference_result.TensorProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto.inference_result_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_TRAJECTORYPOINT']._serialized_start=50
  _globals['_TRAJECTORYPOINT']._serialized_end=124
  _globals['_TRAJECTORY']._serialized_start=126
  _globals['_TRAJECTORY']._serialized_end=189
  _globals['_TRAJECTORYWITHPROB']._serialized_start=192
  _globals['_TRAJECTORYWITHPROB']._serialized_end=331
  _globals['_AGENTPREDICTION']._serialized_start=333
  _globals['_AGENTPREDICTION']._serialized_end=410
  _globals['_ANCHORFREEPATHFORMEROUTPUT']._serialized_start=412
  _globals['_ANCHORFREEPATHFORMEROUTPUT']._serialized_end=493
  _globals['_ANCHORBASEDPATHFORMEROUTPUT']._serialized_start=496
  _globals['_ANCHORBASEDPATHFORMEROUTPUT']._serialized_end=635
  _globals['_DECISIONLONGITUDINALPOINT']._serialized_start=637
  _globals['_DECISIONLONGITUDINALPOINT']._serialized_end=707
  _globals['_DECISIONOUTPUT']._serialized_start=709
  _globals['_DECISIONOUTPUT']._serialized_end=803
  _globals['_INFERENCEOUTPUT']._serialized_start=806
  _globals['_INFERENCEOUTPUT']._serialized_end=1195
  _globals['_TENSORPROTO']._serialized_start=1197
  _globals['_TENSORPROTO']._serialized_end=1254
  _globals['_TRAJECTORYOUTPUT']._serialized_start=1257
  _globals['_TRAJECTORYOUTPUT']._serialized_end=1950
# @@protoc_insertion_point(module_scope)
