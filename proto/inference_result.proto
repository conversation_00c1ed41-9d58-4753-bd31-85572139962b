syntax = "proto3";

package inference_result;

// 轨迹点：一个位置 + 速度信息（可选）
message TrajectoryPoint {
  float x = 1;
  float y = 2;
  float heading = 3;     // optional, 可选角度信息
  float velocity = 4;    // optional, 可选速度信息
}

// 轨迹：一系列轨迹点
message Trajectory {
  repeated TrajectoryPoint points = 1;
}

// 带置信度的候选轨迹
message TrajectoryWithProb {
  Trajectory trajectory = 1;
  float probability = 2;
  float cutin_probability = 3; // optional, 代表切入概率
  int32 obstacle_id = 4; // optional, 轨迹对应的障碍物ID
}

// agent 的预测信息
message AgentPrediction {
  repeated TrajectoryWithProb trajectories = 1;

}

// Anchor-Free Pathformer 输出
message AnchorFreePathformerOutput {
  repeated TrajectoryWithProb paths = 1;
}

// Anchor-Based Pathformer 输出
message AnchorBasedPathformerOutput {
  repeated TrajectoryWithProb paths = 1;
  repeated TrajectoryPoint final_points = 2;
}

message DecisionLongitudinalPoint {
    repeated float s = 1; // frenet s
    repeated float v = 2; // frenet v optinal
    float probability = 3; // 置信度
}
// 决策模块的输出
message DecisionOutput {
  repeated DecisionLongitudinalPoint dlp = 1;
  int32 path_index = 2; // 选中的路径索引
}

// 顶层模型输出结构
message InferenceOutput {
  string version = 1;

  // 多agent预测结果
  repeated AgentPrediction agent_predictions = 2;

  AnchorFreePathformerOutput anchor_free_pathformer = 3;
  AnchorBasedPathformerOutput anchor_based_pathformer = 4;

  repeated DecisionOutput decision = 5;

  TrajectoryOutput trajectory_output = 6;

  string request_id = 7; // 请求ID
}
message TensorProto {
    repeated int32 shape = 1;
    bytes data = 2;
    string dtype = 3;
}
  
message TrajectoryOutput {
TensorProto prediction_trajectory = 1;
TensorProto prediction_classifier = 2;
TensorProto cutin_classifier = 3;

TensorProto anchor_free_pathformer_trajectory = 4;
TensorProto anchor_free_pathformer_classifier = 5;

TensorProto anchor_based_pathformer_trajectory = 6;
TensorProto anchor_based_pathformer_final_point = 7;
TensorProto anchor_based_pathformer_classifier = 8;

TensorProto decision_trajectory = 9;
TensorProto decision_classifier = 10;
}