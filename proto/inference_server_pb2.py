# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: proto/inference_server.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'proto/inference_server.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from proto import inference_result_pb2 as proto_dot_inference__result__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cproto/inference_server.proto\x12\x11inference_service\x1a\x1cproto/inference_result.proto\"\x85\x01\n\x0fInferenceConfig\x12\x18\n\x10json_config_file\x18\x01 \x01(\t\x12\x10\n\x08pth_file\x18\x02 \x01(\t\x12#\n\x1bnearest_negative_anchor_num\x18\x03 \x01(\x05\x12!\n\x19other_negative_anchor_num\x18\x04 \x01(\x05\"F\n\x10LoadModelRequest\x12\x32\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\".inference_service.InferenceConfig\"5\n\x11LoadModelResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"@\n\x10InferenceRequest\x12\x18\n\x10pickle_file_path\x18\x01 \x01(\t\x12\x12\n\nrequest_id\x18\x64 \x01(\t\"9\n\x11InferenceResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x10\n\x08\x61\x63\x63\x65pted\x18\x02 \x01(\x08\"?\n\x14StreamResultsRequest\x12\x13\n\x0brequest_ids\x18\x01 \x03(\t\x12\x12\n\ntimeout_ms\x18\x02 \x01(\x05\x32\x9d\x02\n\x10InferenceService\x12V\n\tLoadModel\x12#.inference_service.LoadModelRequest\x1a$.inference_service.LoadModelResponse\x12R\n\x05Infer\x12#.inference_service.InferenceRequest\x1a$.inference_service.InferenceResponse\x12]\n\rStreamResults\x12\'.inference_service.StreamResultsRequest\x1a!.inference_result.InferenceOutput0\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto.inference_server_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_INFERENCECONFIG']._serialized_start=82
  _globals['_INFERENCECONFIG']._serialized_end=215
  _globals['_LOADMODELREQUEST']._serialized_start=217
  _globals['_LOADMODELREQUEST']._serialized_end=287
  _globals['_LOADMODELRESPONSE']._serialized_start=289
  _globals['_LOADMODELRESPONSE']._serialized_end=342
  _globals['_INFERENCEREQUEST']._serialized_start=344
  _globals['_INFERENCEREQUEST']._serialized_end=408
  _globals['_INFERENCERESPONSE']._serialized_start=410
  _globals['_INFERENCERESPONSE']._serialized_end=467
  _globals['_STREAMRESULTSREQUEST']._serialized_start=469
  _globals['_STREAMRESULTSREQUEST']._serialized_end=532
  _globals['_INFERENCESERVICE']._serialized_start=535
  _globals['_INFERENCESERVICE']._serialized_end=820
# @@protoc_insertion_point(module_scope)
