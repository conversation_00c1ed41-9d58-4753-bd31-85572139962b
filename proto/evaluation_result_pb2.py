# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: proto/evaluation_result.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'proto/evaluation_result.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dproto/evaluation_result.proto\x12\x11\x65valuation_result\"I\n\x0bTopKMetrics\x12\r\n\x05top_1\x18\x01 \x01(\x02\x12\r\n\x05top_3\x18\x02 \x01(\x02\x12\r\n\x05top_6\x18\x03 \x01(\x02\x12\r\n\x05\x63ount\x18\x04 \x01(\x05\"M\n\x0fTopKBoolMetrics\x12\r\n\x05top_1\x18\x01 \x01(\x08\x12\r\n\x05top_3\x18\x02 \x01(\x08\x12\r\n\x05top_6\x18\x03 \x01(\x08\x12\r\n\x05\x63ount\x18\x04 \x01(\x05\"r\n\x15PathClassFloatMetrics\x12\x30\n\npath_class\x18\x01 \x01(\x0e\x32\x1c.evaluation_result.PathClass\x12\x0b\n\x03\x61\x64\x65\x18\x02 \x01(\x02\x12\x0b\n\x03\x66\x64\x65\x18\x03 \x01(\x02\x12\r\n\x05\x63ount\x18\x04 \x01(\x05\"j\n\x14PathClassBoolMetrics\x12\x30\n\npath_class\x18\x01 \x01(\x0e\x32\x1c.evaluation_result.PathClass\x12\x11\n\tcollision\x18\x02 \x01(\x08\x12\r\n\x05\x63ount\x18\x03 \x01(\x05\"o\n\x14\x44lpClassFloatMetrics\x12.\n\tdlp_class\x18\x01 \x01(\x0e\x32\x1b.evaluation_result.DlpClass\x12\x0b\n\x03\x61\x64\x65\x18\x02 \x01(\x02\x12\x0b\n\x03\x66\x64\x65\x18\x03 \x01(\x02\x12\r\n\x05\x63ount\x18\x04 \x01(\x05\"g\n\x13\x44lpClassBoolMetrics\x12.\n\tdlp_class\x18\x01 \x01(\x0e\x32\x1b.evaluation_result.DlpClass\x12\x11\n\tcollision\x18\x02 \x01(\x08\x12\r\n\x05\x63ount\x18\x03 \x01(\x05\"\xa6\x08\n\x10\x45valuationResult\x12?\n\x17prediction_accuracy_ade\x18\x01 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12?\n\x17prediction_accuracy_fde\x18\x02 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12\x42\n\x1apathformer_accuracy_40_ade\x18\x03 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12\x42\n\x1apathformer_accuracy_40_fde\x18\x04 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12\x43\n\x1bpathformer_accuracy_200_ade\x18\x05 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12\x43\n\x1bpathformer_accuracy_200_fde\x18\x06 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12=\n\x15\x64\x65\x63ision_accuracy_ade\x18\x07 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12=\n\x15\x64\x65\x63ision_accuracy_fde\x18\x08 \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12?\n\x13static_4s_collision\x18\t \x01(\x0b\x32\".evaluation_result.TopKBoolMetrics\x12\x41\n\x19pathformer_accuracy4s_ade\x18\n \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12\x41\n\x19pathformer_accuracy4s_fde\x18\x0b \x01(\x0b\x32\x1e.evaluation_result.TopKMetrics\x12I\n\x17nth_cls_path_res_adefde\x18\x0c \x03(\x0b\x32(.evaluation_result.PathClassFloatMetrics\x12R\n!nth_cls_path_res_static_collision\x18\r \x03(\x0b\x32\'.evaluation_result.PathClassBoolMetrics\x12G\n\x16nth_cls_dlp_res_adefde\x18\x0e \x03(\x0b\x32\'.evaluation_result.DlpClassFloatMetrics\x12Q\n!nth_cls_dlp_res_dynamic_collision\x18\x0f \x03(\x0b\x32&.evaluation_result.DlpClassBoolMetrics*\x8f\x01\n\tPathClass\x12\x16\n\x12PATH_CLASS_UNKNOWN\x10\x00\x12\x10\n\x0cPATH_CLASS_1\x10\x01\x12\x10\n\x0cPATH_CLASS_2\x10\x02\x12\x10\n\x0cPATH_CLASS_3\x10\x03\x12\x10\n\x0cPATH_CLASS_4\x10\x04\x12\x10\n\x0cPATH_CLASS_5\x10\x05\x12\x10\n\x0cPATH_CLASS_6\x10\x06*\x87\x01\n\x08\x44lpClass\x12\x15\n\x11\x44LP_CLASS_UNKNOWN\x10\x00\x12\x0f\n\x0b\x44LP_CLASS_1\x10\x01\x12\x0f\n\x0b\x44LP_CLASS_2\x10\x02\x12\x0f\n\x0b\x44LP_CLASS_3\x10\x03\x12\x0f\n\x0b\x44LP_CLASS_4\x10\x04\x12\x0f\n\x0b\x44LP_CLASS_5\x10\x05\x12\x0f\n\x0b\x44LP_CLASS_6\x10\x06\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'proto.evaluation_result_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PATHCLASS']._serialized_start=1714
  _globals['_PATHCLASS']._serialized_end=1857
  _globals['_DLPCLASS']._serialized_start=1860
  _globals['_DLPCLASS']._serialized_end=1995
  _globals['_TOPKMETRICS']._serialized_start=52
  _globals['_TOPKMETRICS']._serialized_end=125
  _globals['_TOPKBOOLMETRICS']._serialized_start=127
  _globals['_TOPKBOOLMETRICS']._serialized_end=204
  _globals['_PATHCLASSFLOATMETRICS']._serialized_start=206
  _globals['_PATHCLASSFLOATMETRICS']._serialized_end=320
  _globals['_PATHCLASSBOOLMETRICS']._serialized_start=322
  _globals['_PATHCLASSBOOLMETRICS']._serialized_end=428
  _globals['_DLPCLASSFLOATMETRICS']._serialized_start=430
  _globals['_DLPCLASSFLOATMETRICS']._serialized_end=541
  _globals['_DLPCLASSBOOLMETRICS']._serialized_start=543
  _globals['_DLPCLASSBOOLMETRICS']._serialized_end=646
  _globals['_EVALUATIONRESULT']._serialized_start=649
  _globals['_EVALUATIONRESULT']._serialized_end=1711
# @@protoc_insertion_point(module_scope)
