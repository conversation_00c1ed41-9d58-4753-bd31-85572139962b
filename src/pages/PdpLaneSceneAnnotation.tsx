import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Layout,
  List,
  Card,
  Button,
  Input,
  Pagination,
  Tag,
  Space,
  message,
  Spin,
  Radio,
  Typography,
  Row,
  Col,
  Slider,
} from "antd";
import {
  CopyOutlined,
  SearchOutlined,
  ExportOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
  WarningOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";
import axios from "axios";
import { useParams } from "react-router-dom";
// import PickleVisualizer from "../components/PickleVisualizer";
import RotatePickleVisualizer from "../components/RotatePickleVisualizer";
import ResizableSider from "../components/ResizableSider";
import ImageWithPaths from "../components/ImageWithPaths";
import "./PdpPathAnnotation.css"; // 复用相同的样式
import { EvaluationCase, EvaluationSet, PdpPathInfo } from "../types";
import { useAuth } from "../contexts/AuthContext";

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

interface LaneSceneAnnotation {
  vrulane_annotation?: string;
  frontlane_annotation?: string;
  employee_id?: string;
  created_at?: string;
  updated_at?: string;
}

interface AnnotationStats {
  total: number;
  annotated: number;
  vrulane_good: number;
  vrulane_bad: number;
  vrulane_ignore: number;
  frontlane_good: number;
  frontlane_bad: number;
  frontlane_ignore: number;
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success("已复制到剪贴板");
  } catch (err) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand("copy");
    document.body.removeChild(textArea);
    message.success("已复制到剪贴板");
  }
};

const PdpLaneSceneAnnotation: React.FC = () => {
  const { id, bagSetId } = useParams<{ id?: string; bagSetId?: string }>();
  const { user, isAuthenticated } = useAuth();

  // 判断是否为bag集模式
  const isBagSetMode = !!bagSetId;

  // 状态管理
  const [leftSiderWidth, setLeftSiderWidth] = useState(300);
  const [rightSiderWidth, setRightSiderWidth] = useState(650);

  const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(
    null
  );
  const [pdpPaths, setPdpPaths] = useState<Record<number, PdpPathInfo>>({});
  const [highlightPathIndex, setHighlightPathIndex] = useState<number | null>(
    null
  );
  const [selectedCenterlineIds, setSelectedCenterlineIds] = useState<string[]>(
    []
  );

  const [pklList, setPklList] = useState<EvaluationCase[]>([]);
  const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
  const selectedPklRef = useRef<EvaluationCase | null>(null);
  const [isHoveringEgoImg, setIsHoveringEgoImg] = useState(false);
  const [currentAnnotation, setCurrentAnnotation] =
    useState<LaneSceneAnnotation | null>(null);
  const [signImage, setsignImage] = useState<string | null>(null);
  const [recordedPklIndex, setRecordedPklIndex] = useState<number | null>(null);
  const [egoImgData, setEgoImgData] = useState<string | null>(null);
  const [ego2img, setEgo2img] = useState<number[][] | null>(null);
  const [egoYaw, setEgoYaw] = useState<number | null>(null);
  const [egoPathData, setEgoPathData] = useState<number[][]>([]); // 用于存储ego路径点数据
  const [loading, setLoading] = useState({
    pklList: false,
    annotation: false,
    data: false,
    markDirty: false,
    checkPkl: false,
  });

  const [pklPagination, setPklPagination] = useState({
    current: 1,
    pageSize: 8,
    total: 0,
  });
  // 添加分页状态
  const [bagPagination, setBagPagination] = useState({
    current: 1,
    pageSize: 8, // 每页显示10个bag
    total: 0, // bag总数
  });
  const [searchKeyword, setSearchKeyword] = useState("");
  const [isDirtyData, setIsDirtyData] = useState(false);
  const [currentPklIndex, setCurrentPklIndex] = useState(0); // 当前PKL在列表中的索引
  const [showFullyAnnotatedOnly, setShowFullyAnnotatedOnly] = useState(false);

  // bag集模式相关状态
  const [bagSetDetail, setBagSetDetail] = useState<any>(null);
  const [bagList, setBagList] = useState<any[]>([]);
  const [selectedBag, setSelectedBag] = useState<any>(null);
  const [bagPklList, setBagPklList] = useState<any[]>([]);
  const [checkStatusFilter, setCheckStatusFilter] = useState<
    "all" | "checked" | "unchecked"
  >("all");

  const [bagName, setBagName] = useState("");
  const [timeNs, setTimeNs] = useState("");
  const [timeRange, setTimeRange] = useState("");
  const [year, setYear] = useState("");
  const [month, setMonth] = useState("");
  const [day, setDay] = useState("");
  const [annotationStats, setAnnotationStats] = useState<AnnotationStats>({
    total: 0,
    annotated: 0,
    vrulane_good: 0,
    vrulane_bad: 0,
    vrulane_ignore: 0,
    frontlane_good: 0,
    frontlane_bad: 0,
    frontlane_ignore: 0,
  });
  // 在现有的 useState hooks 中添加新状态
  const [sceneTag, setSceneTag] = useState<string[]>([]); // 改为数组

  // 复制模式相关状态
  const [copyMode, setCopyMode] = useState(false); // 是否开启复制模式
  const [copiedData, setCopiedData] = useState<{
    sceneTag: string[];
    selectedCenterlineIds: string[];
  } | null>(null); // 复制的数据

  // 定义场景标签选项
  const sceneTagOptions = [
    { value: "进路口-直行", label: "进路口-直行" },
    { value: "进路口-左转", label: "进路口-左转" },
    { value: "进路口-右转", label: "进路口-右转" },
    { value: "进路口-左掉头", label: "进路口-左掉头" },
    { value: "出路口-直行", label: "出路口-直行" },
    { value: "出路口-左转", label: "出路口-左转" },
    { value: "出路口-右转", label: "出路口-右转" },
    { value: "出路口-左掉头", label: "出路口-左掉头" },
  ];

  // 处理复制模式切换
  const handleCopyModeToggle = () => {
    if (!copyMode) {
      // 开启复制模式，记录当前数据
      if (!selectedPkl) {
        message.error("请先选择PKL文件");
        return;
      }
      setCopiedData({
        sceneTag: [...sceneTag],
        selectedCenterlineIds: [...selectedCenterlineIds],
      });
      setCopyMode(true);
      setRecordedPklIndex(currentPklIndex); // 记录当前的 PKL 索引

      message.success(
        "复制模式已开启，已记录当前PKL的标注数据 (索引: ${currentPklIndex + 1})"
      );
    } else {
      // 关闭复制模式
      setCopyMode(false);
      setCopiedData(null);
      message.success("复制模式已关闭");
    }
  };

  // 应用复制的数据到当前PKL
  const applyCopiedData = async () => {
    if (!copiedData || !selectedPkl) return;

    try {
      // 应用场景标签
      // setSceneTag([...copiedData.sceneTag]);

      let response;

      // bag集模式
      response = await axios.post("/api/annotation/lane-scene/scene-tags", {
        pkl_id: selectedPkl.id,
        tags: copiedData.sceneTag,
        bag_id: selectedBag.id,
        delete_annotation: false,
      });

      if (response && !response.data.success) {
        console.error("保存场景标签失败");
      }
      // 应用选中的centerline IDs
      // setSelectedCenterlineIds([...copiedData.selectedCenterlineIds]);

      try {
        const requestData: any = {
          pkl_id: selectedPkl.id,
          selected_centerline_ids: copiedData.selectedCenterlineIds,
          employee_id: user?.employee_id || null,
        };

        requestData.bag_id = selectedBag.id;

        await axios.post(
          "/api/annotation/lane-scene/selected-centerlines",
          requestData
        );

        message.success("复制的数据已应用到当前PKL");
      } catch (error) {
        console.error("保存选中centerlines失败:", error);
      }
    } catch (error) {
      console.error("应用复制数据失败:", error);
      message.error("应用复制数据失败");
    }
  };

  // 添加处理场景标签选择的函数
  const handleSceneTagSelect = async (tag: string) => {
    if (!selectedPkl) {
      message.error("请先选择PKL文件");
      return;
    }

    // 切换标签选择状态（多选模式）
    const currentTags = [...sceneTag];
    const tagIndex = currentTags.indexOf(tag);

    let newTags: string[];
    if (tagIndex > -1) {
      // 如果标签已选中，则取消选择
      newTags = currentTags.filter((t) => t !== tag);
    } else {
      // 如果标签未选中，则添加选择
      newTags = [...currentTags, tag];
    }
    setSceneTag(newTags);
    try {
      console.log("提交场景标签：", newTags);
      let response;
      response = await axios.post("/api/annotation/lane-scene/scene-tags", {
        pkl_id: selectedPkl.id,
        tags: newTags,
        bag_id: selectedBag.id,
        delete_annotation: newTags.length === 0,
      });
      if (response.data.success) {
        message.success(
          newTags.length > 0 ? "场景标签保存成功" : "场景标签已清除"
        );
      } else {
        message.error("保存场景标签失败");
        // 恢复之前的状态
        setSceneTag(sceneTag);
      }
    } catch (error) {
      console.error("Failed to save scene tag:", error);
      message.error("保存场景标签出错");
      // 恢复之前的状态
      setSceneTag(sceneTag);
    }
  };
  useEffect(() => {
    if (!isAuthenticated) {
      message.error("请先登录后再进行标注操作");
      return;
    }

    // 根据模式加载不同的数据
    loadBagSetDetail();
  }, [isAuthenticated, bagSetId]);

  // 加载bag集详情
  const loadBagSetDetail = async (
    page = 1,
    pageSize = bagPagination.pageSize,
    year_filter=year,
    month_filter=month,
    day_filter=day,
  ) => {
    if (!bagSetId) return;

    try {
      const response = await axios.get(`/api/bag-sets/${bagSetId}`, {
        params: { page: page, per_page: pageSize,year:year_filter, month: month_filter, day: day_filter },
      });
      if (response.data.success) {
        setBagSetDetail(response.data.bag_set);
        setBagList(response.data.bags);
        // 更新分页信息
        setBagPagination({
          current: page,
          pageSize,
          total: response.data.total_bags,
        });
        // 如果有bag，默认选择第一个
        if (response.data.bags.length > 0) {
          handleBagSelect(response.data.bags[0]);
        }
      } else {
        message.error("加载bag集详情失败");
      }
    } catch (error) {
      console.error("Failed to load bag set detail:", error);
      message.error("加载bag集详情出错");
    }
  };

  // 选择bag
  const handleBagSelect = async (bag: any) => {
    // 清除与当前 bag 相关的状态
    setSelectedPkl(null);
    setCurrentPklIndex(0);
    setSceneTag([]);
    setSelectedCenterlineIds([]);
    setEgoImgData(null);
    setsignImage(null);
    setCurrentAnnotation(null);
    setCopiedData(null);
    setRecordedPklIndex(null);
    setSelectedBag(bag);

    try {
      const response = await axios.get(
        `/api/bag-sets/${bagSetId}/bags/${bag.id}/pkls`
      );
      if (response.data.success) {
        // 现在API返回的格式与evaluation_set_with_cases对齐，直接使用cases
        const pklCases = response.data.cases || [];
        console.log(
          "Loaded pkl cases for bag:",
          bag.bag_name,
          "count:",
          pklCases.length
        );

        setBagPklList(pklCases);
        setPklList(pklCases);

        // 如果有pkl，默认选择第一个
        if (pklCases.length > 0) {
          setCurrentPklIndex(0);
          // 这里可以选择是否自动加载第一个pkl的数据
          handlePklSelect(pklCases[0], bag);
        }
      } else {
        message.error("加载bag的pkl列表失败");
      }
    } catch (error) {
      console.error("Failed to load bag pkls:", error);
      message.error("加载bag的pkl列表出错");
    }
  };

  // 加载PKL文件列表
  const loadPklList = async (
    page = pklPagination.current,
    pageSize = pklPagination.pageSize,
    search = searchKeyword,
    fullyAnnotatedOnly = showFullyAnnotatedOnly,
    checkStatus = checkStatusFilter,
    bag_name = bagName,
    time_ns = timeNs,
    time_range = timeRange
  ) => {
    if (!id) return;

    setLoading((prev) => ({ ...prev, pklList: true }));
    try {
      const params: any = {
        page,
        per_page: pageSize,
        search,
        annotated_filter: fullyAnnotatedOnly, // 使用车道场景标注过滤
        check_status: checkStatus,
        pdp: true,
      };

      if (bag_name && time_ns && time_range) {
        params.bag_name = bag_name;
        params.time_ns = time_ns;
        params.time_range = time_range;
      }

      const response = await axios.get(`/api/evaluation_sets/${id}`, {
        params,
      });

      if (response.data.success) {
        const evaluationSetData: EvaluationSet = {
          id: response.data.evaluation_set.id,
          set_name: response.data.evaluation_set.name,
          creator_name: response.data.evaluation_set.creator_name || "",
          description: response.data.evaluation_set.description,
          cases: response.data.cases || [],
          case_count: response.data.case_count || 0,
          created_at: response.data.evaluation_set.created_at,
        };
        setEvaluationSet(evaluationSetData);
        const newPklList = response.data.cases || [];
        setPklList(newPklList);
        setPklPagination({
          current: response.data.page || page,
          pageSize: response.data.per_page || pageSize,
          total: response.data.case_count || 0,
        });

        // 如果有选中的PKL，更新其在新列表中的索引
        if (selectedPkl) {
          const index = newPklList.findIndex(
            (item: EvaluationCase) => item.id === selectedPkl.id
          );
          if (index !== -1) {
            setCurrentPklIndex(index);
          } else {
            // 如果当前选中的PKL不在新列表中，重置索引
            setCurrentPklIndex(0);
          }
        } else {
          // 如果没有选中的PKL，重置索引
          setCurrentPklIndex(0);
        }
      } else {
        message.error(response.data.error || "加载PKL列表失败");
      }
    } catch (error) {
      console.error("Failed to load PKL list:", error);
      message.error("加载PKL列表出错");
    } finally {
      setLoading((prev) => ({ ...prev, pklList: false }));
    }
  };
  // 处理bag分页变化
  const handleBagPageChange = (page: number, pageSize?: number) => {
    loadBagSetDetail(page, pageSize || bagPagination.pageSize);
  };
  // 选择PKL文件时加载数据
  const handlePklSelect = async (pkl: EvaluationCase, bag?: any) => {
    setSelectedPkl(pkl);
    setCurrentAnnotation(null);
    setsignImage(null);
    setEgoImgData(null);
    setSelectedCenterlineIds([]);
    setSceneTag([]); // 重置场景标签

    setLoading((prev) => ({ ...prev, data: true }));

    // 更新当前PKL在列表中的索引
    const index = pklList.findIndex((item) => item.id === pkl.id);
    if (index !== -1) {
      setCurrentPklIndex(index);
    }
    // 如果是复制模式，立即设置复制的数据
    if (copyMode && copiedData) {
      setSelectedCenterlineIds([...copiedData.selectedCenterlineIds]);
      setSceneTag([...copiedData.sceneTag]);
    } else {
      // 非复制模式才重置数据
      setSelectedCenterlineIds([]);
      setSceneTag([]);
    }

    try {
      let response;
      // bag集模式：调用bag集相关API
      const selectedBagToUse = bag || selectedBag;
      response = await axios.get(
        `/api/bag-sets/${bagSetId}/bags/${selectedBagToUse.id}/pkl/${pkl.id}`
      );

      if (response.data.success) {
        console.log("API Response:", response.data); // 调试信息
        setCurrentAnnotation(response.data.current_annotation);
        setIsDirtyData(response.data.pkl_info.is_dirty || false);

        if (response.data.sign_image) {
          // console.log(
          //   "Setting image data, length:",
          //   response.data.image_data.length
          // ); // 调试信息
          setsignImage(response.data.sign_image);
        } else {
          console.log("No image data received"); // 调试信息
          setsignImage(null);
        }
        if (response.data.ego_img_data) {
          setEgoImgData(response.data.ego_img_data);
        } else {
          setEgoImgData(null);
        }
        if (response.data.ego2img) {
          setEgo2img(response.data.ego2img);
        } else {
          setEgo2img(null);
        }
        if (!copyMode) {
          // 设置选中的centerline ID
          if (response.data.selected_centerline_ids) {
            setSelectedCenterlineIds(response.data.selected_centerline_ids);
            console.log(
              "Selected Centerline IDs:",
              response.data.selected_centerline_ids
            );
          } else {
            setSelectedCenterlineIds([]);
          }
          if (response.data.scene_tags) {
            setSceneTag(response.data.scene_tags);
            console.log("Scene Tag:", response.data.scene_tags);
          } else {
            setSceneTag([]);
          }
        }

        if (response.data.ego_yaw !== null) {
          setEgoYaw(response.data.ego_yaw);
        } else {
          setEgoYaw(null);
        }
        if (response.data.ego_path_data) {
          setEgoPathData(response.data.ego_path_data);
        } else {
          setEgoPathData([]);
        }
      } else {
        message.error("加载车道场景数据失败");
      }
      // 如果开启了复制模式，自动应用复制的数据
      if (copyMode && copiedData) {
        applyCopiedData();
      }
    } catch (error) {
      console.error("Failed to load lane scene data:", error);
      message.error("加载车道场景数据出错");
    } finally {
      setLoading((prev) => ({ ...prev, data: false }));
    }
  };

  useEffect(() => {
    selectedPklRef.current = selectedPkl;
  }, [selectedPkl]);

  const changeCenterlineIds = useCallback(
    async (ids: string[]) => {
      //   setSelectedCenterlineIds(ids);
      setSelectedCenterlineIds([...ids]);

      // 如果有选中的 PKL 文件，保存到后端
      const currentSelectedPkl = selectedPklRef.current;
      if (currentSelectedPkl) {
        try {
          console.log("Sending selected centerline IDs to backend:", ids);
          await axios.post("/api/annotation/lane-scene/selected-centerlines", {
            pkl_id: currentSelectedPkl.id,
            selected_centerline_ids: ids,
            bag_id: selectedBag.id,
            employee_id: user?.employee_id || null,
          });
        } catch (error) {
          console.error("保存选中centerlines失败:", error);
          message.error("保存选中centerlines失败");
        }
      }
    },
    [selectedPkl, id, user?.employee_id]
  ); // 添加所有依赖项

  // 处理滑动条变化
  const handleSliderChange = (value: number) => {
    if (pklList.length > 0 && value >= 0 && value < pklList.length) {
      const pkl = pklList[value];
      handlePklSelect(pkl);
    }
  };

  // 处理标注
  const handleAnnotate = async (
    annotationType: "vrulane" | "frontlane",
    annotation: string
  ) => {
    if (!selectedPkl) {
      message.error("请先选择PKL文件");
      return;
    }

    // 检查是否需要删除标注
    const currentValue =
      annotationType === "vrulane"
        ? currentAnnotation?.vrulane_annotation
        : currentAnnotation?.frontlane_annotation;
    const isDeleteAction = currentValue === annotation;

    setLoading((prev) => ({ ...prev, annotation: true }));
    try {
      const requestData: any = {
        pkl_id: selectedPkl.id,
        evaluation_set_id: parseInt(id || "0"),
        delete_annotation: isDeleteAction,
      };

      if (isDeleteAction) {
        requestData.annotation_type = annotationType;
      } else {
        if (annotationType === "vrulane") {
          requestData.vrulane_annotation = annotation;
        } else {
          requestData.frontlane_annotation = annotation;
        }
      }

      const response = await axios.post(
        "/api/annotation/lane-scene/annotate",
        requestData
      );

      if (response.data.success) {
        // 更新当前标注状态
        const updatedAnnotation = { ...currentAnnotation };
        if (isDeleteAction) {
          if (annotationType === "vrulane") {
            updatedAnnotation.vrulane_annotation = undefined;
          } else {
            updatedAnnotation.frontlane_annotation = undefined;
          }
          message.success("标注已删除");
        } else {
          if (annotationType === "vrulane") {
            updatedAnnotation.vrulane_annotation = annotation;
          } else {
            updatedAnnotation.frontlane_annotation = annotation;
          }
          updatedAnnotation.employee_id = user?.employee_id;
          updatedAnnotation.updated_at = new Date().toISOString();
          message.success("标注保存成功");
        }

        setCurrentAnnotation(updatedAnnotation);
      } else {
        message.error("操作失败");
      }
    } catch (error) {
      console.error("Failed to save annotation:", error);
      message.error("操作出错");
    } finally {
      setLoading((prev) => ({ ...prev, annotation: false }));
    }
  };

  // 处理标记为脏数据
  const handleMarkAsDirty = async () => {
    if (!selectedPkl) {
      message.error("请先选择PKL文件");
      return;
    }

    setLoading((prev) => ({ ...prev, markDirty: true }));
    try {
      const response = await axios.post("/api/annotation/mark-dirty", {
        pkl_id: selectedPkl.id,
        is_dirty: !isDirtyData,
      });

      if (response.data.success) {
        setIsDirtyData(response.data.is_dirty);
        message.success(
          response.data.is_dirty ? "已标记为脏数据" : "已取消脏数据标记"
        );

        const updatedPklList = pklList.map((pkl) => {
          if (pkl.id === selectedPkl.id) {
            return { ...pkl, dirty_data: response.data.is_dirty };
          }
          return pkl;
        });
        setPklList(updatedPklList);
      } else {
        message.error("操作失败");
      }
    } catch (error) {
      console.error("Failed to mark as dirty data:", error);
      message.error("操作出错");
    } finally {
      setLoading((prev) => ({ ...prev, markDirty: false }));
    }
  };

  // 处理PKL检查状态切换
  const handleTogglePklCheck = async (
    pkl: EvaluationCase,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();

    if (!id) {
      message.error("评测集ID无效");
      return;
    }

    setLoading((prev) => ({ ...prev, checkPkl: true }));

    try {
      const isCurrentlyChecked = pkl.is_checked;
      const endpoint = isCurrentlyChecked
        ? `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`
        : `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`;

      const method = isCurrentlyChecked ? "delete" : "post";
      const response = await axios[method](endpoint);

      if (response.data.success) {
        const updatedPklList = pklList.map((item) => {
          if (item.id === pkl.id) {
            return {
              ...item,
              is_checked: !isCurrentlyChecked,
              checked_at: isCurrentlyChecked
                ? undefined
                : new Date().toISOString(),
              checked_by: isCurrentlyChecked ? undefined : user?.employee_id,
            };
          }
          return item;
        });

        setPklList(updatedPklList);

        if (selectedPkl?.id === pkl.id) {
          setSelectedPkl({
            ...selectedPkl,
            is_checked: !isCurrentlyChecked,
            checked_at: isCurrentlyChecked
              ? undefined
              : new Date().toISOString(),
            checked_by: isCurrentlyChecked ? undefined : user?.employee_id,
          });
        }

        message.success(
          isCurrentlyChecked ? "PKL检查标记已取消" : "PKL已标记为已检查"
        );
      } else {
        message.error("操作失败：" + (response.data.error || "未知错误"));
      }
    } catch (error) {
      console.error("Failed to toggle PKL check status:", error);
      message.error("操作出错，请稍后再试");
    } finally {
      setLoading((prev) => ({ ...prev, checkPkl: false }));
    }
  };

  // 导出标注数据
  const handleExportAnnotations = async () => {
    if (!id) {
      message.error("评测集ID无效");
      return;
    }

    try {
      const exportUrl = `/api/annotation/lane-scene/export/${id}`;
      window.open(exportUrl, "_blank");
    } catch (error) {
      console.error("Failed to export annotations:", error);
      message.error("导出出错，请稍后再试");
    }
  };
  // 添加导出处理函数
  const handleExportLaneSceneAnnotations = async () => {
    if (!bagSetId) {
      message.error("缺少bag集ID");
      return;
    }
    try {
      const exportUrl = `/api/bag-sets/export-bag-set/${bagSetId}`;
      window.open(exportUrl, "_blank");
    } catch (error) {
      console.error("Export failed:", error);
      message.error("导出失败，请稍后再试");
    }
  };
  // 处理分页变化
  const handlePklPageChange = (page: number, pageSize?: number) => {
    loadPklList(
      page,
      pageSize,
      searchKeyword,
      showFullyAnnotatedOnly,
      checkStatusFilter,
      bagName,
      timeNs,
      timeRange
    );
  };

  // 处理搜索
  const handleSearch = () => {
    loadPklList(
      1,
      pklPagination.pageSize,
      searchKeyword,
      showFullyAnnotatedOnly,
      checkStatusFilter,
      bagName,
      timeNs,
      timeRange
    );
  };
  const handleDateFilter = () => {

  
    // 调用加载函数，传递年月日参数
    loadBagSetDetail(1, bagPagination.pageSize, year, month, day);
  };
  const handleFilterToggle = () => {
    const newFilterState = !showFullyAnnotatedOnly;
    setShowFullyAnnotatedOnly(newFilterState);
    loadPklList(
      1,
      pklPagination.pageSize,
      searchKeyword,
      newFilterState,
      checkStatusFilter,
      bagName,
      timeNs,
      timeRange
    );
  };

  const handleCheckStatusFilter = (status: "all" | "checked" | "unchecked") => {
    setCheckStatusFilter(status);
    loadPklList(
      1,
      pklPagination.pageSize,
      searchKeyword,
      showFullyAnnotatedOnly,
      status,
      bagName,
      timeNs,
      timeRange
    );
  };

  const handleClearBagTimeFilter = () => {
    setBagName("");
    setTimeNs("");
    setTimeRange("");
    loadPklList(
      1,
      pklPagination.pageSize,
      searchKeyword,
      showFullyAnnotatedOnly,
      checkStatusFilter,
      "",
      "",
      ""
    );
  };
  // 在其他处理函数附近添加 handlePklNavigation 函数
  const handlePklNavigation = async (step: number) => {
    if (pklList.length === 0) return;

    const newIndex = currentPklIndex + step;

    // 确保索引在有效范围内
    if (newIndex >= 0 && newIndex < pklList.length) {
      const pkl = pklList[newIndex];
      // 如果是复制模式，复制标注数据到目标 PKL
      if (copyMode && copiedData) {
        const targetIndices = Array.from(
          { length: Math.abs(step) },
          (_, i) => currentPklIndex + (step > 0 ? i + 1 : -(i + 1))
        )
          .filter((index) => index >= 0 && index < pklList.length)
          .filter((index) => index !== newIndex); // 排除目标 PKL 的索引;

        for (const index of targetIndices) {
          const targetPkl = pklList[index];
          try {
            // 复制场景标签
            await axios.post("/api/annotation/lane-scene/scene-tags", {
              pkl_id: targetPkl.id,
              tags: copiedData.sceneTag,
              bag_id: selectedBag.id,
              delete_annotation: false,
            });

            // 复制选中的中心线 ID
            await axios.post(
              "/api/annotation/lane-scene/selected-centerlines",
              {
                pkl_id: targetPkl.id,
                selected_centerline_ids: copiedData.selectedCenterlineIds,
                bag_id: selectedBag.id,
                employee_id: user?.employee_id || null,
              }
            );

            console.log(`标注数据已复制到 PKL: ${targetPkl.pkl_name}`);
          } catch (error) {
            console.error(
              `复制标注数据到 PKL ${targetPkl.pkl_name} 失败:`,
              error
            );
          }
        }

        message.success(`标注数据已复制到 ${targetIndices.length} 个 PKL 文件`);
      }
      handlePklSelect(pkl);
    }
  };
  // 初始加载
  useEffect(() => {
    if (id) {
      loadPklList();
    }
  }, [id]);

  return (
    <div
      className="lane-scene-annotation-layout"
      style={{ display: "flex", height: "100vh" }}
    >
      {/* 左侧列表 */}
      <ResizableSider
        width={leftSiderWidth}
        minWidth={200}
        maxWidth={600}
        onResize={setLeftSiderWidth}
        position="left"
        className="pkl-list-sider"
      >
        <div className="pkl-list-header">
          <Space direction="vertical" style={{ width: "100%" }} size="small">
            {bagSetDetail && (
              <div
                style={{
                  marginBottom: "4px",
                  padding: "4px 6px",
                  background: "#f0f8ff",
                  borderRadius: "3px",
                  border: "1px solid #d9d9d9",
                }}
              >
                <Text
                  strong
                  style={{
                    fontSize: "12px",
                    color: "#1890ff",
                    lineHeight: "1.2",
                  }}
                >
                  bag集: {bagSetDetail.set_name}
                </Text>
              </div>
            )}
            <div
              style={{
                border: "1px solid #d9d9d9",
                padding: "6px",
                borderRadius: "4px",
                marginBottom: "8px",
              }}
            >
              <Text strong style={{ fontSize: "11px", color: "#666" }}>
                按日期过滤:
              </Text>
              <div style={{ display: "flex", gap: "8px", marginTop: "4px" }}>
                <Input
                  placeholder="年 (YYYY)"
                  value={year}
                  onChange={(e) => setYear(e.target.value)}
                  size="small"
                  style={{ width: "60px" }}
                />
                <Input
                  placeholder="月 (MM)"
                  value={month}
                  onChange={(e) => setMonth(e.target.value)}
                  size="small"
                  style={{ width: "40px" }}
                />
                <Input
                  placeholder="日 (DD)"
                  value={day}
                  onChange={(e) => setDay(e.target.value)}
                  size="small"
                  style={{ width: "40px" }}
                />
              </div>
              <Button
                type="primary"
                size="small"
                onClick={handleDateFilter}
                style={{ marginTop: "8px", width: "100%" }}
              >
                应用日期过滤
              </Button>
            </div>
            {!isBagSetMode && (
              <Input
                placeholder="搜索PKL文件"
                prefix={<SearchOutlined />}
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onPressEnter={handleSearch}
              />
            )}

            {!isBagSetMode && (
              <div
                style={{
                  border: "1px solid #d9d9d9",
                  padding: "6px",
                  borderRadius: "4px",
                }}
              >
                <Text strong style={{ fontSize: "11px", color: "#666" }}>
                  bag_name时间戳过滤:
                </Text>
                <Input
                  placeholder="bag_name"
                  value={bagName}
                  onChange={(e) => setBagName(e.target.value)}
                  size="small"
                  style={{ marginTop: "3px" }}
                />
                <Input
                  placeholder="时间戳(纳秒)"
                  value={timeNs}
                  onChange={(e) => setTimeNs(e.target.value)}
                  size="small"
                  style={{ marginTop: "3px" }}
                />
                <Input
                  placeholder="时间范围(秒)"
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                  size="small"
                  style={{ marginTop: "3px" }}
                />
                <Space size="small" style={{ marginTop: "3px", width: "100%" }}>
                  <Button
                    type="primary"
                    size="small"
                    onClick={handleSearch}
                    style={{ flex: 1 }}
                  >
                    应用过滤
                  </Button>
                  <Button
                    size="small"
                    onClick={handleClearBagTimeFilter}
                    style={{ flex: 1 }}
                  >
                    清空
                  </Button>
                </Space>
              </div>
            )}

            {!isBagSetMode && (
              <Space size="small" style={{ marginTop: "4px" }}>
                <Button
                  type={showFullyAnnotatedOnly ? "primary" : "default"}
                  icon={<CheckCircleOutlined />}
                  onClick={handleFilterToggle}
                  size="small"
                >
                  {showFullyAnnotatedOnly ? "显示全部" : "仅显示已标注"}
                </Button>
                <Radio.Group
                  value={checkStatusFilter}
                  onChange={(e) => handleCheckStatusFilter(e.target.value)}
                  size="small"
                  buttonStyle="solid"
                >
                  <Radio.Button value="all">全部</Radio.Button>
                  <Radio.Button value="checked">已检查</Radio.Button>
                  <Radio.Button value="unchecked">未检查</Radio.Button>
                </Radio.Group>
                <Button
                  type="default"
                  icon={<ExportOutlined />}
                  onClick={handleExportAnnotations}
                  size="small"
                  style={{ width: "100%", marginTop: "4px" }}
                >
                  导出车道场景标注数据
                </Button>
              </Space>
            )}
            <Button
              type="default"
              icon={<ExportOutlined />}
              onClick={handleExportLaneSceneAnnotations}
              size="small"
              style={{ width: "100%", marginTop: "8px" }}
            >
              导出车道场景标注数据
            </Button>
          </Space>
        </div>
        <div className="bag-list-container">
        <div className="bag-list-main">
          {
            // bag集模式：显示bag列表
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
              }}
            >
              {/* // bag集模式：显示bag列表 */}
              <div style={{ flex: 1, overflowY: "auto" }}>
                <List
                  loading={loading.pklList}
                  dataSource={bagList}
                  renderItem={(bag) => (
                    <List.Item
                      className={
                        selectedBag?.id === bag.id
                          ? "pkl-item selected"
                          : "pkl-item"
                      }
                      onClick={() => handleBagSelect(bag)}
                      style={{ display: "flex", alignItems: "flex-start" }}
                    >
                      <div
                        className="pkl-item-content"
                        style={{ flex: 1, minWidth: 0 }}
                      >
                        <div
                          className="pkl-name"
                          style={{
                            fontSize: "2px",
                            lineHeight: "1.3",
                            wordBreak: "break-all",
                            whiteSpace: "normal",
                            maxWidth: "100%",
                          }}
                        >
                          <Text strong>{bag.bag_name}</Text>
                        </div>
                        <div
                          style={{
                            fontSize: "10px",
                            color: "#666",
                            marginTop: "2px",
                          }}
                        >
                          {bag.pkl_count} pkl文件
                        </div>
                      </div>
                    </List.Item>
                  )}
                  style={{
                    overflowY: "auto", // 启用垂直滚动条
                    maxHeight: "calc(100vh - 250px)", // 限制最大高度，避免内容超出
                  }}
                />
              </div>
              <div style={{ padding: "8px 0", textAlign: "center" }}>
                <Pagination
                  current={bagPagination.current}
                  pageSize={bagPagination.pageSize}
                  total={bagPagination.total}
                  onChange={handleBagPageChange}
                  showSizeChanger={false}
                  size="small"
                  style={{ marginTop: "8px", textAlign: "center" }}
                />
              </div>
            </div>
            
          }
        </div>
        </div>
        {!isBagSetMode && (
          <div className="pkl-pagination">
            <Pagination
              current={pklPagination.current}
              pageSize={pklPagination.pageSize}
              total={pklPagination.total}
              onChange={handlePklPageChange}
              showSizeChanger={false}
              size="small"
              simple
            />
          </div>
        )}
      </ResizableSider>

      {/* 中间可视化区域 */}
      <div
        className="lane-visualization-content"
        style={{
          flex: 1,
          background: "#fff",
          borderRight: "1px solid #f0f0f0",
          overflow: "hidden",
          position: "relative", // 添加相对定位以便滑动条绝对定位
        }}
      >
        {/* 显示 ego_img_data 图片 */}
        {/* 显示 ego_img_data 和 signImage 图片 */}
        {/* <div
          style={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            padding: "0px 0",
            backgroundColor: "#f9f9f9",
            borderBottom: "1px solid #f0f0f0",
            gap: "16px", // 图片之间的间距
          }}
        >
          {egoImgData && (
            <img
              src={`data:image/jpeg;base64,${egoImgData}`}
              alt="Ego Image"
              style={{
                maxWidth: "45%", // 限制宽度为容器的45%
                maxHeight: "200px",
                objectFit: "contain",
                border: "1px solid #d9d9d9",
                borderRadius: "4px",
              }}
            />
          )}
          {signImage && (
            <img
              src={`data:image/png;base64,${signImage}`}
              alt="Sign Image"
              style={{
                maxWidth: "45%", // 限制宽度为容器的45%
                maxHeight: "200px",
                objectFit: "contain",
                border: "1px solid #d9d9d9",
                borderRadius: "4px",
              }}
            />
          )}
        </div> */}
        {selectedPkl ? (
          <div
            className="visualization-container"
            style={{ width: "100%", height: "100%" }}
          >
            <div
              className="visualization-area"
              style={{ width: "100%", height: "100%" }}
            >
              {loading.data ? (
                <div
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                  }}
                >
                  <Spin tip="加载可视化..." />
                </div>
              ) : (
                <div
                  style={{
                    width: "100%",
                    height: "100%",
                    position: "relative", // 设置为相对定位，作为图片绝对定位的参考
                  }}
                >
                  {/* 3D可视化区域 - 占满整个空间 */}
                  <div
                    style={{
                      width: "100%",
                      height: "calc(100vh - 80px)",
                      position: "relative",
                    }}
                  >
                    <RotatePickleVisualizer
                      evaluationCase={selectedPkl}
                      highlightPathIndex={null}
                      pdpPaths={pdpPaths}
                      changeCenterlineIds={changeCenterlineIds}
                      selectedCenterlineIds={selectedCenterlineIds}
                    />
                  </div>

                  {/* 图片区域 - 绝对定位在右下角*/}
                  {signImage && (
                    <div
                      style={{
                        position: "absolute", // 绝对定位
                        right: "16px", // 距离右边16px
                        bottom: "150px", // 距离底部16px

                        width: "200px", // 固定宽度
                        maxWidth: "40%", // 最大宽度不超过父容器的40%
                        height: "auto",
                        maxHeight: "250px", // 最大高度
                        backgroundColor: "rgba(255, 255, 255, 0.95)", // 半透明白色背景
                        borderRadius: "8px",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)", // 更明显的阴影
                        border: "1px solid rgba(0, 0, 0, 0.1)",
                        overflow: "hidden",
                        zIndex: 10, // 确保在3D场景之上
                      }}
                    >
                      <img
                        src={`data:image/png;base64,${signImage}`}
                        alt="车道标志图"
                        style={{
                          width: "100%",
                          height: "auto",
                          maxHeight: "250px",
                          objectFit: "contain", // 保持图片比例
                          display: "block",
                        }}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
              color: "#999",
            }}
          >
            <Text>请从左侧选择一个PKL文件</Text>
          </div>
        )}

        {/* 导航滑动条 */}
        {pklList.length > 0 && (isBagSetMode ? selectedBag : true) && (
          <div
            style={{
              position: "absolute", // 固定位置
              bottom: 0, // 固定在页面底部
              width: "100%", // 宽度调整为页面的80%
              backgroundColor: "rgba(255, 255, 255, 0.8)",
              padding: "12px 16px",
              borderRadius: "8px",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              border: "1px solid rgba(0, 0, 0, 0.1)",
            }}
          >
            {/* 添加导航按钮 */}
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                gap: "8px",
                marginBottom: "8px",
              }}
            >
              <Button
                size="small"
                onClick={() => handlePklNavigation(-20)}
                disabled={currentPklIndex <= 0}
              >
                -20
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(-15)}
                disabled={currentPklIndex <= 0}
              >
                -15
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(-10)}
                disabled={currentPklIndex <= 0}
              >
                -10
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(-5)}
                disabled={currentPklIndex <= 0}
              >
                -5
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(-1)}
                disabled={currentPklIndex <= 0}
              >
                &lt;
              </Button>
              {/* 输入框 */}
              <Input
                type="number"
                size="small"
                value={currentPklIndex + 1} // 显示当前索引（+1 是为了显示从 1 开始的索引）
                onChange={(e) => {
                  const value = parseInt(e.target.value, 10);
                  if (!isNaN(value) && value >= 1 && value <= pklList.length) {
                    setCurrentPklIndex(value - 1); // 更新索引（-1 是为了匹配数组索引）
                  }
                }}
                onPressEnter={() => {
                  const targetPkl = pklList[currentPklIndex];
                  if (targetPkl) {
                    handlePklSelect(targetPkl); // 切换到指定的 PKL
                  } else {
                    message.error("无效的 PKL 索引");
                  }
                }}
                style={{
                  width: "60px",
                  textAlign: "center",
                }}
              />
              <span>/ {pklList.length}</span>
              <Button
                size="small"
                onClick={() => handlePklNavigation(1)}
                disabled={currentPklIndex >= pklList.length - 1}
              >
                &gt;
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(5)}
                disabled={currentPklIndex >= pklList.length - 1}
              >
                +5
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(10)}
                disabled={currentPklIndex >= pklList.length - 1}
              >
                +10
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(15)}
                disabled={currentPklIndex >= pklList.length - 1}
              >
                +15
              </Button>
              <Button
                size="small"
                onClick={() => handlePklNavigation(20)}
                disabled={currentPklIndex >= pklList.length - 1}
              >
                +20
              </Button>
            </div>

            <Slider
              min={0}
              max={pklList.length - 1}
              value={currentPklIndex}
              keyboard={true}
              onChange={handleSliderChange}
              tooltip={{
                formatter: (value) => {
                  if (value !== undefined && pklList[value]) {
                    return `${value + 1}/${pklList.length}: ${
                      pklList[value].pkl_name
                    }`;
                  }
                  return "";
                },
              }}
              style={{ margin: 0 }}
            />
            <div
              style={{ marginBottom: "8px", fontSize: "12px", color: "#666" }}
            >
              <div>
                {isBagSetMode ? (
                  <>
                    {selectedBag && `bag: ${selectedBag.bag_name} - `}
                    PKL: {currentPklIndex + 1} / {pklList.length}
                  </>
                ) : (
                  `PKL: ${currentPklIndex + 1} / {pklList.length}`
                )}
              </div>
              {selectedPkl && (
                <span style={{ marginLeft: "8px", fontFamily: "monospace" }}>
                  {selectedPkl.pkl_name}
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 右侧车道场景标注面板 */}
      <ResizableSider
        width={rightSiderWidth}
        minWidth={300}
        maxWidth={1000}
        onResize={setRightSiderWidth}
        position="right"
        className="annotation-sider"
      >
        <div className="annotation-panel">
          <div className="annotation-header">
            <Title level={5} style={{ margin: "16px 0 8px 0" }}>
              车道场景标注
            </Title>

            {/* 复制模式按钮 */}
            <div style={{ marginBottom: "16px" }}>
              <Button
                type={copyMode ? "primary" : "default"}
                icon={<CopyOutlined />}
                onClick={handleCopyModeToggle}
                style={{
                  width: "100%",
                  backgroundColor: copyMode ? "#52c41a" : undefined,
                  borderColor: copyMode ? "#52c41a" : undefined,
                }}
              >
                {copyMode ? "复制模式已开启" : "开启复制模式"}
              </Button>
            </div>
          </div>
          {recordedPklIndex !== null && (
            <Button
              type="primary"
              style={{ marginTop: "8px" }}
              onClick={() => {
                const targetPkl = pklList[recordedPklIndex];
                if (targetPkl) {
                  handlePklSelect(targetPkl); // 直接跳转到对应的 PKL
                } else {
                  message.error("无法找到记录的 PKL 文件");
                }
              }}
            >
              跳转到第{recordedPklIndex + 1}个PKL
            </Button>
          )}
          {!selectedPkl ? (
            <div className="empty-annotation">
              <Text>请先选择一个PKL文件</Text>
            </div>
          ) : loading.data ? (
            <div className="loading-paths">
              <Spin tip="加载数据..." />
            </div>
          ) : (
            <div className="lane-annotation-container">
              <Card
                title="中心线id"
                size="small"
                style={{ marginBottom: "16px" }}
              >
                {selectedCenterlineIds && selectedCenterlineIds.length > 0 && (
                  <div>
                    <div
                      style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}
                    >
                      {selectedCenterlineIds.map((id) => (
                        <div
                          key={id}
                          style={{
                            padding: "4px 8px",
                            backgroundColor: "#f5f5f5",
                            borderRadius: "4px",
                            fontSize: "12px",
                            fontFamily: "monospace",
                          }}
                        >
                          {id}
                        </div>
                      ))}
                    </div>
                    <div
                      style={{
                        marginTop: "8px",
                        fontSize: "12px",
                        color: "#666",
                      }}
                    >
                      总计: {selectedCenterlineIds.length} 条中心线
                    </div>
                  </div>
                )}
              </Card>
              <Card
                title="场景标签"
                size="small"
                style={{ marginBottom: "16px" }}
              >
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr",
                    gap: "8px",
                  }}
                >
                  {sceneTagOptions.map((option) => (
                    <Button
                      key={option.value}
                      size="small"
                      type={
                        sceneTag.includes(option.value) ? "primary" : "default"
                      }
                      onClick={() => handleSceneTagSelect(option.value)}
                      style={{
                        fontSize: "12px",
                        height: "auto",
                        padding: "4px 8px",
                      }}
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
                {sceneTag.length > 0 && (
                  <div style={{ marginTop: "8px", fontSize: "12px" }}>
                    <Text>当前选择 {sceneTag.length} 个标签 </Text>
                  </div>
                )}
              </Card>
              {/* egoImgData - 放置在右侧面板下方 */}
              {egoImgData && (
                <div
                  style={{
                    marginTop: "16px",
                    padding: "8px",
                    backgroundColor: "#f9f9f9",
                    border: "1px solid #d9d9d9",
                    borderRadius: "8px",
                    textAlign: "center",
                    position: "relative", // 为放大图片提供定位参考
                  }}
                  onMouseEnter={() => setIsHoveringEgoImg(true)} // 鼠标进入时显示放大图片
                  onMouseLeave={() => setIsHoveringEgoImg(false)} // 鼠标离开时隐藏放大图片
                >
                  <img
                    src={`data:image/jpeg;base64,${egoImgData}`}
                    alt="Ego Image"
                    style={{
                      maxWidth: "100%",
                      maxHeight: "400px",
                      objectFit: "contain",
                    }}
                  />
                  {/* 放大图片 */}
                  {/* {isHoveringEgoImg && (
      <div
        style={{
          position: "absolute",
          top: "-400px", // 放大图片显示在上方
          left: "50%",
          transform: "translateX(-50%)",
          zIndex: 100,
          backgroundColor: "#fff",
          border: "1px solid #d9d9d9",
          borderRadius: "8px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
          padding: "8px",
        }}
      >
        <img
          src={`data:image/jpeg;base64,${egoImgData}`}
          alt="Ego Image Enlarged"
          style={{
            width: "600px", // 放大图片的宽度
            height: "auto",
            objectFit: "contain",
            borderRadius: "4px",
          }}
        />
      </div>
    )} */}
                </div>
              )}
              {/* 标注信息 */}
              {currentAnnotation &&
                (currentAnnotation.vrulane_annotation ||
                  currentAnnotation.frontlane_annotation) && (
                  <Card
                    title="标注信息"
                    size="small"
                    style={{ marginTop: "16px" }}
                  >
                    {currentAnnotation.employee_id && (
                      <Text style={{ fontSize: "12px", color: "#666" }}>
                        标注人: {currentAnnotation.employee_id}
                      </Text>
                    )}
                    {currentAnnotation.updated_at && (
                      <div>
                        <Text style={{ fontSize: "12px", color: "#666" }}>
                          更新时间:{" "}
                          {new Date(
                            currentAnnotation.updated_at
                          ).toLocaleString()}
                        </Text>
                      </div>
                    )}
                  </Card>
                )}
            </div>
          )}
        </div>
      </ResizableSider>
    </div>
  );
};

export default PdpLaneSceneAnnotation;
