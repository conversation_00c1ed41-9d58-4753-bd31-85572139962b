import React, { useState, useEffect } from 'react';
import { Card, Table, Button, message, Tag, Progress, Row, Col } from 'antd';
import { PlayCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
interface Assignment {
    id: number;
    task_id: number;
    task_name: string;
    assigned_at: string;
    started_at: string | null;
    completed_at: string | null;
    total_assigned: number;  // 分配给当前标注员的PKL总数
    completed: number;       // 已完成的PKL数量
    remaining: number;       // 剩余的PKL数量
    progress_percentage: number; // 进度百分比
}


const ValidityCheckTaskAssignmentPage: React.FC = () => {
    const [assignments, setAssignments] = useState<Assignment[]>([]);
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    const { user, isAuthenticated } = useAuth();
    const annotatorId = user?.id; // 实际应从用户context获取

    const loadAssignments = async () => {
        setLoading(true);
        try {
            const response = await axios.get(`/api/validity-check/assignments/annotator/${annotatorId}`);

            // 为每个assignment获取详细进度
            const assignments = response.data.assignments;
            const assignmentPromises = assignments.map(async (assignment: any) => {
                const progressRes = await axios.get(
                    `/api/validity-check/annotations/progress/${assignment.task_id}/${annotatorId}`
                );
                return {
                    ...assignment,
                    ...progressRes.data.data
                };
            });

            const detailedAssignments = await Promise.all(assignmentPromises);
            setAssignments(detailedAssignments);
        } catch (error) {
            message.error('加载任务失败');
        } finally {
            setLoading(false);
        }
    };


    useEffect(() => {
        loadAssignments();
    }, []);

    const handleStartAnnotation = async (assignmentId: number) => {
        try {
            await axios.post(`/api/validity-check/assignments/${assignmentId}/start`);
            message.success('开始标注');
            loadAssignments();
        } catch (error) {
            message.error('开始标注失败');
        }
    };

    const handleGoToAnnotation = (taskId: number, assignmentId: number) => {
        navigate(`/validity-check/annotation/${taskId}/${assignmentId}`);
    };

    const getStatusTag = (assignment: Assignment) => {
        if (assignment.completed_at) {
            return <Tag color="success">已完成</Tag>;
        } else if (assignment.started_at) {
            return <Tag color="processing">进行中</Tag>;
        } else {
            return <Tag color="default">未开始</Tag>;
        }
    };

    const getProgress = (assignment: Assignment) => {
        return assignment.progress_percentage || 0;
    };

    const columns = [
        {
            title: '任务名称',
            dataIndex: 'task_name',
            key: 'task_name',
        },
        {
            title: '状态',
            key: 'status',
            render: (_, record: Assignment) => getStatusTag(record)
        },
        {
            title: '进度',
            key: 'progress',
            render: (_, record: Assignment) => (
                <div>
                    <Progress
                        percent={record.progress_percentage || 0}
                        size="small"
                        format={() => `${record.completed || 0}/${record.total_assigned || 0}`}
                    />
                </div>
            )
        },
        {
            title: '分配时间',
            dataIndex: 'assigned_at',
            key: 'assigned_at',
            render: (date: string) => new Date(date).toLocaleString()
        },
        {
            title: '开始时间',
            dataIndex: 'started_at',
            key: 'started_at',
            render: (date: string | null) => date ? new Date(date).toLocaleString() : '-'
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record: Assignment) => {
                if (record.completed_at) {
                    return <Button disabled icon={<CheckCircleOutlined />}>已完成</Button>;
                } else if (record.started_at) {
                    return (
                        <Button
                            type="primary"
                            onClick={() => handleGoToAnnotation(record.task_id, record.id)}
                        >
                            继续标注
                        </Button>
                    );
                } else {
                    return (
                        <Button
                            type="primary"
                            icon={<PlayCircleOutlined />}
                            onClick={() => {
                                handleStartAnnotation(record.id);
                                setTimeout(() => {
                                    handleGoToAnnotation(record.task_id, record.id);
                                }, 1000);
                            }}
                        >
                            开始标注
                        </Button>
                    );
                }
            }
        }
    ];

    const statistics = {
        total: assignments.length,
        notStarted: assignments.filter(a => !a.started_at).length,
        inProgress: assignments.filter(a => a.started_at && !a.completed_at).length,
        completed: assignments.filter(a => a.completed_at).length
    };

    return (
        <div style={{ padding: 24 }}>
            <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={6}>
                    <Card>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                                {statistics.total}
                            </div>
                            <div>总任务数</div>
                        </div>
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                                {statistics.notStarted}
                            </div>
                            <div>未开始</div>
                        </div>
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                                {statistics.inProgress}
                            </div>
                            <div>进行中</div>
                        </div>
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <div style={{ textAlign: 'center' }}>
                            <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                                {statistics.completed}
                            </div>
                            <div>已完成</div>
                        </div>
                    </Card>
                </Col>
            </Row>

            <Card title="我的标注任务">
                <Table
                    columns={columns}
                    dataSource={assignments}
                    rowKey="id"
                    loading={loading}
                    pagination={{ pageSize: 10 }}
                />
            </Card>
        </div>
    );
};

export default ValidityCheckTaskAssignmentPage;