import React, { useState, useEffect, useMemo } from "react";
import axios from "axios";
import { Card, Button, Radio, message, Progress, Tag, List, Input } from "antd";
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CheckOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { useParams, useNavigate } from "react-router-dom";

import PickleVisualizer from "../../components/PickleVisualizer";
import { useAuth } from "../../contexts/AuthContext";

// * -------------------------------------------------------------------------- interface

interface PKL {
  id: number;
  pkl_name: string;
  pkl_dir: string;
  bag_name: string;
  vehicle_type: string;
  date: string;
  mode: string;
  version: string;
  is_completed: boolean;
  annotation_result?: string; // 标注结果
  annotation_time?: string; // 标注时间
}

interface BagGroup {
  bag_name: string;
  pkls: PKL[];
  completed_count: number;
  total_count: number;
}

// * -------------------------------------------------------------------------- const

const validityOptions = [
  { label: "有效", value: "valid" },
  { label: "GT标注错误", value: "bad_gt" },
  { label: "车辆未居中", value: "not_centered" },
  { label: "在对向车道", value: "on_opposite_road" },
  { label: "跨越边界", value: "cross_boundary" },
  { label: "偏离导航路径", value: "deviate_from_navi" },
  { label: "未知问题", value: "unknown" },
];

// * -------------------------------------------------------------------------- component

const AnnotationPage: React.FC = () => {
  const { taskId, assignmentId } = useParams<{
    taskId: string;
    assignmentId: string;
  }>();
  const navigate = useNavigate();

  const [bagGroups, setBagGroups] = useState<BagGroup[]>([]);
  const [currentBagIndex, setCurrentBagIndex] = useState(0);
  const [currentPklIndex, setCurrentPklIndex] = useState(0);
  const [validity, setValidity] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [showBagList, setShowBagList] = useState(false);
  const [bagListPage, setBagListPage] = useState(1);
  const [showIncompleteOnly, setShowIncompleteOnly] = useState(false);

  const filteredBagGroups = useMemo(() => {
    if (showIncompleteOnly) {
      return bagGroups.filter((bag) => bag.completed_count < bag.total_count);
    }
    return bagGroups;
  }, [bagGroups, showIncompleteOnly]);

  const { user } = useAuth() || {};
  const annotatorId = user?.id || 0;

  const currentBag = bagGroups[currentBagIndex];
  const currentPkl = currentBag?.pkls[currentPklIndex];
  const totalPkls = bagGroups.reduce((sum, bag) => sum + bag.total_count, 0);
  const completedPkls = bagGroups.reduce(
    (sum, bag) => sum + bag.completed_count,
    0,
  );
  const overallProgress =
    totalPkls > 0 ? Math.round((completedPkls / totalPkls) * 100) : 0;

  const evaluationCase = useMemo(
    () => ({
      id: currentPkl?.id || 0,
      pkl_name: currentPkl?.pkl_name || "",
      pkl_dir: currentPkl?.pkl_dir || "",
      key_obs_id: 0,
      path_range: [0, 0] as [number, number],
      dirty_data: false,
    }),
    [currentPkl?.id, currentPkl?.pkl_name, currentPkl?.pkl_dir],
  );

  // 在组件顶部或使用 useMemo 预计算前缀和数组
  const bagPrefixSums = useMemo(() => {
    const prefixSums: number[] = [0];
    bagGroups.forEach((bag, index) => {
      prefixSums.push(prefixSums[index] + bag.total_count);
    });
    return prefixSums;
  }, [bagGroups]);

  // 二分查找函数
  const findBagAndPklIndex = (absolutePosition: number) => {
    // 使用二分查找找到对应的 bagIndex
    let left = 0;
    let right = bagPrefixSums.length - 1;
    let bagIndex = 0;

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      if (bagPrefixSums[mid] < absolutePosition) {
        bagIndex = mid;
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }

    const pklIndex = absolutePosition - bagPrefixSums[bagIndex] - 1;
    return { bagIndex, pklIndex };
  };

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // 检查是否按下空格键，且没有在输入框或其他表单元素中
      if (
        event.code === "Space" &&
        event.target instanceof HTMLElement &&
        !["INPUT", "TEXTAREA", "SELECT"].includes(event.target.tagName)
      ) {
        event.preventDefault(); // 防止页面滚动
        handleSubmitAnnotation();
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [validity, submitting, currentPkl?.id, annotatorId, assignmentId, taskId]);

  useEffect(() => {
    if (currentPkl && !currentPkl.is_completed) {
      // 如果当前PKL未完成标注，默认选择"有效"
      setValidity("valid");
    } else if (currentPkl && currentPkl.is_completed) {
      // 如果已完成，显示之前的标注结果
      setValidity(currentPkl.annotation_result || "");
    }
  }, [currentPkl?.id, currentPkl?.is_completed, currentPkl?.annotation_result]);

  // 加载所有PKL数据
  const loadAllPkls = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        `/api/validity-check/annotations/pkls/grouped`,
        {
          params: {
            task_id: taskId,
            annotator_id: annotatorId,
          },
        },
      );

      const bagGroupsData = response.data.bag_groups;
      console.log("🚀 ~ loadAllPkls ~ bagGroupsData:", bagGroupsData)

      // 转换为BagGroup数组
      const groups: BagGroup[] = Object.entries(bagGroupsData).map(
        ([bagName, pkls]) => {
          const pklArray = pkls as PKL[];
          const completedCount = pklArray.filter(
            (pkl) => pkl.is_completed,
          ).length;
          return {
            bag_name: bagName,
            pkls: pklArray,
            completed_count: completedCount,
            total_count: pklArray.length,
          };
        },
      ).sort((a, b) => a.bag_name.localeCompare(b.bag_name)); 

      setBagGroups(groups);
      console.log("加载的Bag Groups:", groups);

      // 找到第一个bag
      if (groups.length > 0) {
        setCurrentBagIndex(0);
        setCurrentPklIndex(0);
      }
    } catch (error) {
      message.error("加载数据失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (taskId && assignmentId) {
      loadAllPkls();
    }
  }, [taskId, assignmentId]);

  // 提交标注结果
  const handleSubmitAnnotation = async () => {
    if (!validity) {
      message.warning("请选择标注结果");
      return;
    }

    setSubmitting(true);
    try {
      await axios.post("/api/validity-check/annotations", {
        assignment_id: parseInt(assignmentId!),
        task_id: parseInt(taskId!),
        pkl_id: currentPkl.id,
        annotator_id: annotatorId,
        validity: validity,
      });

      const isUpdate = currentPkl?.is_completed;
      message.success(isUpdate ? "标注更新成功" : "标注提交成功");

      // 更新PKL完成状态
      const newBagGroups = [...bagGroups];
      const bagIndex = currentBagIndex;
      const pklIndex = currentPklIndex;

      const wasCompleted = newBagGroups[bagIndex].pkls[pklIndex].is_completed;

      newBagGroups[bagIndex].pkls[pklIndex] = {
        ...newBagGroups[bagIndex].pkls[pklIndex],
        is_completed: true,
        annotation_result: validity,
        annotation_time: new Date().toISOString(),
      };

      // 如果之前未完成，现在完成了，增加完成计数
      if (!wasCompleted) {
        newBagGroups[bagIndex].completed_count += 1;
      }

      setBagGroups(newBagGroups);

      // 如果是新标注（非更新），移动到下一个PKL
      if (!isUpdate) {
        moveToNext();
        setValidity("");
      }
    } catch (error) {
      message.error(currentPkl?.is_completed ? "标注更新失败" : "标注提交失败");
    } finally {
      setSubmitting(false);
    }
  };

  // 移动到下一个PKL
  const moveToNext = () => {
    if (currentPklIndex < (currentBag?.pkls.length || 0) - 1) {
      setCurrentPklIndex(currentPklIndex + 1);
    } else if (currentBagIndex < bagGroups.length - 1) {
      setCurrentBagIndex(currentBagIndex + 1);
      setCurrentPklIndex(0);
    }
  };

  // 移动到上一个PKL
  const moveToPrevious = () => {
    if (currentPklIndex > 0) {
      setCurrentPklIndex(currentPklIndex - 1);
    } else if (currentBagIndex > 0) {
      setCurrentBagIndex(currentBagIndex - 1);
      setCurrentPklIndex(
        (bagGroups[currentBagIndex - 1]?.pkls.length || 1) - 1,
      );
    }
  };

  // 跳转到指定bag的PKL
  const jumpToBag = (
    bagName: string,
    completedCount: number,
    totalCount: number,
  ) => {
    const targetBagIndex = bagGroups.findIndex(
      (bag) => bag.bag_name === bagName,
    );
    if (targetBagIndex !== -1) {
      setCurrentBagIndex(targetBagIndex);
      if (completedCount < totalCount) {
        setCurrentPklIndex(completedCount);
      } else {
        setCurrentPklIndex(0);
      }
      setShowBagList(false);
    }
  };

  if (loading) {
    return <div style={{ padding: 24, textAlign: "center" }}>加载中...</div>;
  }

  if (!currentBag || !currentPkl) {
    return (
      <div style={{ padding: 24, textAlign: "center" }}>
        <div>没有可标注的数据</div>
        <Button
          type="primary"
          onClick={() => navigate("/validity-check/assignment")}
          style={{ marginTop: 16 }}
        >
          返回任务列表
        </Button>
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-50px)] flex flex-col p-4 gap-4">
      {/* 顶部信息栏 */}
      <div className="flex rounded-md border border-[#eee] bg-white p-4">
        <div className="flex flex-1 items-center justify-start">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate("/validity-check/assignment")}
          >
            返回任务列表
          </Button>
        </div>
        <div className="flex flex-1">
          <div className="flex flex-col justify-start w-full px-4 items-center gap-2">
            <h3>Bag: {currentBag.bag_name}</h3>
            <Progress
              percent={Math.round(
                (currentBag.completed_count / currentBag.total_count) * 100,
              )}
              format={() =>
                `${currentBag.completed_count}/${currentBag.total_count}`
              }
            />
          </div>
        </div>
        <div className="flex flex-1">
          <div className="flex flex-col w-full items-end justify-start gap-2">
            <div className="flex gap-4 items-center">
              <Button
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => setShowBagList(!showBagList)}
              >
                Bag列表
              </Button>
              <div>
                总进度: {completedPkls}/{totalPkls}
              </div>
            </div>
            <Progress
              percent={overallProgress}
              size="small"
              className="w-[300px]!"
            />
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex flex-1 relative gap-4">
        {/* 左侧可视化 */}
        <div className="flex-1 rounded-md border border-[#eee] bg-white p-4">
          <PickleVisualizer evaluationCase={evaluationCase} />
        </div>

        {/* 右侧标注面板 */}
        <Card className="w-[400px] ml-8" title="标注信息">
          <div className="mb-2 flex">
            <div className="mx-auto flex flex-col gap-1 items-start">
              <h4>当前PKL信息</h4>
              <p>
                <strong className="mr-1">车辆类型:</strong>
                <Tag>{currentPkl.vehicle_type}</Tag>
              </p>
              <p>
                <strong className="mr-1">日期:</strong>
                {currentPkl.date}
              </p>
              <p>
                <strong className="mr-1">模式:</strong>
                <Tag>{currentPkl.mode}</Tag>
              </p>
              <p>
                <strong className="mr-1">版本:</strong>
                <Tag>{currentPkl.version}</Tag>
              </p>
              <p>
                <strong className="mr-1">状态:</strong>
                <Tag color={currentPkl.is_completed ? "green" : "blue"}>
                  {currentPkl.is_completed ? "已完成" : "待标注"}
                </Tag>
              </p>
            </div>
          </div>

          {/* 标注面板 */}
          <div className="mb-4">
            <h4>质量评分</h4>
            <Radio.Group
              value={
                validity ||
                (currentPkl.is_completed ? currentPkl.annotation_result : "")
              }
              onChange={(e) => setValidity(e.target.value)}
              className="w-full"
            >
              <div className="flex flex-col gap-2">
                {validityOptions.map((option) => (
                  <Radio key={option.value} value={option.value}>
                    {option.label}
                  </Radio>
                ))}
              </div>
            </Radio.Group>
          </div>

          {/* 提交/更新按钮 */}
          <div className="mb-4">
            <Button
              block
              type="primary"
              size="large"
              icon={<CheckOutlined />}
              onClick={handleSubmitAnnotation}
              loading={submitting}
              disabled={
                !validity &&
                !(currentPkl.is_completed && currentPkl.annotation_result)
              }
            >
              {currentPkl.is_completed ? "更新标注" : "提交标注"}
            </Button>
          </div>

          {/* 导航按钮 */}
          <div className="flex justify-between gap-4">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={moveToPrevious}
              disabled={currentBagIndex === 0 && currentPklIndex === 0}
            >
              上一个
            </Button>

            <div className="flex flex-col items-center gap-2">
              <div className="text-sm text-[#666]">
                {/* 计算当前PKL在整个任务中的绝对位置 */}
                {(() => {
                  let absolutePosition = 0;
                  for (let i = 0; i < currentBagIndex; i++) {
                    absolutePosition += bagGroups[i].total_count;
                  }
                  absolutePosition += currentPklIndex + 1;
                  return `当前: ${absolutePosition}/${totalPkls}`;
                })()}
              </div>
              <div className="flex items-center gap-1">
                <Input
                  min="1"
                  max={totalPkls}
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value) && value >= 1 && value <= totalPkls) {
                      const { bagIndex, pklIndex } = findBagAndPklIndex(value);
                      setCurrentBagIndex(bagIndex);
                      setCurrentPklIndex(pklIndex);
                    }
                  }}
                  onPressEnter={(e) => {
                    const value = parseInt(
                      (e.target as HTMLInputElement).value,
                    );
                    if (!isNaN(value) && value >= 1 && value <= totalPkls) {
                      const { bagIndex, pklIndex } = findBagAndPklIndex(value);
                      setCurrentBagIndex(bagIndex);
                      setCurrentPklIndex(pklIndex);
                    }
                  }}
                  className="w-[80px] text-center"
                  size="small"
                />
                <span className="text-sm text-[#666]">/</span>
                <span className="text-sm text-[#666]">{totalPkls}</span>
              </div>
            </div>

            <Button
              icon={<ArrowRightOutlined />}
              onClick={moveToNext}
              disabled={
                currentBagIndex === bagGroups.length - 1 &&
                currentPklIndex === (currentBag?.pkls.length || 0) - 1
              }
            >
              下一个
            </Button>
          </div>

          <div className="mt-4 text-xs text-[#666] flex flex-col gap-1">
            <p>{`当前: 第${currentPklIndex + 1}个/共${currentBag.total_count}个`}</p>
            <p>{`(Bag: ${currentBag.bag_name})`}</p>
            <p>{`已完成: ${currentBag.completed_count}个 | 待标注: ${currentBag.total_count - currentBag.completed_count}个`}</p>
            <p className="text-[#1890ff]">{`提示: 按空格键快速跳转到下一个`}</p>
          </div>
        </Card>

        {/* Bag列表浮窗 */}
        {showBagList && (
          <>
            {/* 背景遮罩 */}
            <div
              onClick={() => setShowBagList(false)}
              className="fixed top-0 left-0 right-0 bottom-0 bg-black/30 z-[1000]"
            />

            {/* 浮窗内容 */}
            <Card
              style={{
                position: "fixed",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: 650,
                maxHeight: "80vh",
                zIndex: 1001,
                boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
                overflow: "hidden",
              }}
              title="所有Bag Groups"
              extra={
                <div style={{ display: "flex", gap: 2, alignItems: "center" }}>
                  <Button
                    size="small"
                    type={showIncompleteOnly ? "primary" : "default"}
                    onClick={() => {
                      setShowIncompleteOnly(!showIncompleteOnly);
                      setBagListPage(1); // 重置页码
                    }}
                  >
                    {showIncompleteOnly ? "显示全部" : "仅未完成"}
                  </Button>
                  <Button size="small" onClick={() => setShowBagList(false)}>
                    关闭
                  </Button>
                </div>
              }
            >
              <div
                style={{ maxHeight: "calc(80vh - 120px)", overflowY: "auto" }}
              >
                {filteredBagGroups.length === 0 ? (
                  <div className="text-center p-4 text-[#999]">
                    {showIncompleteOnly ? "所有Bag都已完成标注" : "没有数据"}
                  </div>
                ) : (
                  <List
                    size="small"
                    pagination={{
                      current: bagListPage,
                      pageSize: 8,
                      total: filteredBagGroups.length,
                      onChange: setBagListPage,
                      showSizeChanger: false,
                      size: "small",
                    }}
                    dataSource={filteredBagGroups.slice(
                      (bagListPage - 1) * 8,
                      bagListPage * 8,
                    )}
                    renderItem={(bag) => (
                      <List.Item
                        className="mb-2 last:mb-0"
                        style={{
                          cursor: "pointer",
                          backgroundColor:
                            bag.bag_name === currentBag?.bag_name
                              ? "#f0f8ff"
                              : "transparent",
                          padding: "6px",
                          borderRadius: "6px",
                          border:
                            bag.bag_name === currentBag?.bag_name
                              ? "1px solid #1890ff"
                              : "1px solid #f0f0f0",
                          transition: "all 0.2s ease",
                        }}
                        onClick={() =>
                          jumpToBag(
                            bag.bag_name,
                            bag.completed_count,
                            bag.total_count,
                          )
                        }
                        onMouseEnter={(e) => {
                          if (bag.bag_name !== currentBag?.bag_name) {
                            e.currentTarget.style.backgroundColor = "#fafafa";
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (bag.bag_name !== currentBag?.bag_name) {
                            e.currentTarget.style.backgroundColor =
                              "transparent";
                          }
                        }}
                      >
                        <div className="w-full">
                          <div className="flex justify-between items-between">
                            <span
                              style={{
                                fontWeight:
                                  bag.bag_name === currentBag?.bag_name
                                    ? "bold"
                                    : "normal",
                                fontSize: "14px",
                                color:
                                  bag.bag_name === currentBag?.bag_name
                                    ? "#1890ff"
                                    : "#333",
                              }}
                            >
                              {bag.bag_name}
                            </span>
                            <Tag
                              color={
                                bag.completed_count === bag.total_count
                                  ? "green"
                                  : "blue"
                              }
                            >
                              {bag.completed_count}/{bag.total_count}
                            </Tag>
                          </div>
                          <Progress
                            percent={Math.round(
                              (bag.completed_count / bag.total_count) * 100,
                            )}
                            className="mb-[-2px]"
                            size="small"
                            showInfo={false}
                            status={
                              bag.completed_count === bag.total_count
                                ? "success"
                                : "active"
                            }
                          />
                        </div>
                      </List.Item>
                    )}
                  />
                )}
              </div>
            </Card>
          </>
        )}
      </div>
    </div>
  );
};

const BagListModal: React.FC = () => {
  return (
    <div>
      <h1>Bag List Modal</h1>
    </div>
  );
};

export default AnnotationPage;
