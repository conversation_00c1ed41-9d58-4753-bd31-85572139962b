import React, { useState, useEffect } from 'react';
import { Layout, Typography, Spin, Button, Input, Pagination, message, Empty } from 'antd';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { ArrowLeftOutlined, SearchOutlined } from '@ant-design/icons';
import PickleVisualizer from '../components/PickleVisualizer';
import './ImageCompare.css';
import { EvaluationCase, InferenceConfig, PdpPathInfo } from '../types';
const { Title, Text } = Typography;
const { Header, Content } = Layout;
const { Search } = Input;

const ImageCompare: React.FC = () => {
    const { id: evaluationSetId } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const location = useLocation();
    const { configIds = [] } = location.state as { configIds: number[] } || {};

    const [loading, setLoading] = useState(true);
    const [configs, setConfigs] = useState<InferenceConfig[]>([]);
    const [cases, setCases] = useState<EvaluationCase[]>([]);
    const [evaluationSetName, setEvaluationSetName] = useState<string>('');
    const [searchText, setSearchText] = useState<string>('');
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(3);
    const [totalCases, setTotalCases] = useState<number>(0);
    const [searchLoading, setSearchLoading] = useState<boolean>(false);
    const [pathsByConfigAndCase, setPathsByConfigAndCase] = useState<Record<string, PdpPathInfo[]>>({});
    const [loadingPaths, setLoadingPaths] = useState<boolean>(false);

    // 获取评测集信息和评测案例
    const fetchEvaluationSet = async (page = 1, perPage = 10, searchQuery = '') => {
        setLoading(true);
        try {
            // 使用分页参数请求API
            const params: any = {
                page: page,
                per_page: perPage
            };

            // 如果有搜索关键词，添加到请求参数
            if (searchQuery) {
                params.search = searchQuery;
            }

            const evaluationSetResponse = await axios.get(`/api/evaluation_sets/${evaluationSetId}`, {
                params: params
            });

            if (evaluationSetResponse.data.success) {
                console.log('获取评测集信息成功:', evaluationSetResponse.data);
                const evaluationSet = evaluationSetResponse.data.evaluation_set;
                const fetchedCases = evaluationSetResponse.data.cases || [];

                setEvaluationSetName(evaluationSet.set_name);
                setCases(fetchedCases);
                // 更新总数
                if (evaluationSetResponse.data.case_count !== undefined) {
                    setTotalCases(evaluationSetResponse.data.case_count);
                }

                // 预加载所有案例的所有配置的路径数据
                await fetchAllPathsData(fetchedCases, configIds);
            } else {
                message.error(evaluationSetResponse.data.error || '获取评测集详情失败');
            }
        } catch (error) {
            console.error('Failed to fetch evaluation set details:', error);
            message.error('获取评测集详情失败，请稍后再试');
        } finally {
            setLoading(false);
        }
    };

    // 获取所有案例的所有配置的路径数据
    const fetchAllPathsData = async (cases: EvaluationCase[], configIds: number[]) => {
        setLoadingPaths(true);
        try {
            const allPaths: Record<string, PdpPathInfo[]> = {};

            // 创建所有请求的Promise
            const requests: Promise<void>[] = [];
            for (const caseItem of cases) {
                for (const configId of configIds) {
                    const key = `${caseItem.id}-${configId}`;
                    requests.push(
                        axios.get('/api/inference-result', {
                            params: {
                                config_id: configId,
                                pkl_id: caseItem.id
                            }
                        })
                            .then(response => {
                                if (response.data.success) {
                                    // 转换为PdpPathInfo格式
                                    const paths: PdpPathInfo[] = response.data.trajectories.map((traj: any, index: number) => ({
                                        index: index,
                                        probability: traj.probability || 0,
                                        points_count: traj.points?.length || 0,
                                        visualization_points: traj.points || [],
                                        annotation: null
                                    }));
                                    allPaths[key] = paths;
                                } else {
                                    console.warn(`Failed to fetch paths for case ${caseItem.id}, config ${configId}:`, response.data.error);
                                    allPaths[key] = [];
                                }
                            })
                            .catch(error => {
                                console.error(`Error fetching paths for case ${caseItem.id}, config ${configId}:`, error);
                                allPaths[key] = [];
                            })
                    );
                }
            }

            // 等待所有请求完成
            await Promise.all(requests);
            setPathsByConfigAndCase(allPaths);
        } catch (error) {
            console.error('Failed to fetch all paths data:', error);
        } finally {
            setLoadingPaths(false);
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            if (!configIds.length || !evaluationSetId) {
                message.error('参数错误，无法加载对比数据');
                navigate(-1);
                return;
            }

            if (configIds.length > 4) {
                message.warning('图片对比最多只能比较4个推理配置');
            }

            // 获取评测集第一页数据
            await fetchEvaluationSet(currentPage, pageSize, searchText);

            // 获取配置详情
            setLoading(true);
            try {
                const configPromises = configIds.map(configId =>
                    axios.get(`/api/inference_config/${configId}`)
                );

                const configResults = await Promise.all(configPromises);
                const configData = configResults.map((result, index) => {
                    if (result.data.success) {
                        return result.data;
                    } else {
                        return {
                            id: configIds[index],
                            json_name: `配置 ${configIds[index]}`,
                            pth_name: '未知模型',
                            pth_upload_time: ''
                        };
                    }
                });

                setConfigs(configData);
            } catch (error) {
                console.error('Failed to fetch inference configs:', error);
                message.error('获取推理配置失败，请稍后再试');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [evaluationSetId, configIds, navigate]); // 不包含 currentPage 和 searchText，初始加载一次

    // 处理页码变化
    const handlePageChange = (page: number, pageSize?: number) => {
        const newPageSize = pageSize || 10;
        setCurrentPage(page);
        setPageSize(newPageSize);
        fetchEvaluationSet(page, newPageSize, searchText);
    };

    // 处理搜索
    const handleSearch = (value: string) => {
        setSearchText(value);
        setCurrentPage(1); // 重置到第一页
        fetchEvaluationSet(1, pageSize, value);
    };

    const handleGoBack = () => {
        navigate(-1);
    };

    // 获取指定案例和配置的路径数据
    const getPathsForCaseAndConfig = (caseId: number, configId: number): PdpPathInfo[] => {
        const key = `${caseId}-${configId}`;
        // console.log(`Fetching paths for case ${caseId}, config ${configId}:`, pathsByConfigAndCase[key]);
        return pathsByConfigAndCase[key] || [];
    };

    return (
        <Layout className="image-compare">
            <Header className="compare-header">
                <Button
                    type="link"
                    icon={<ArrowLeftOutlined />}
                    onClick={handleGoBack}
                >
                    返回
                </Button>
                <Title level={3}>图片对比 - {evaluationSetName}</Title>
            </Header>
            <Content className="compare-content">
                {loading ? (
                    <div className="loading-container">
                        <Spin size="large" tip="加载对比数据..." />
                    </div>
                ) : (
                    <>
                        <div className="search-container">
                            <Search
                                placeholder="搜索Pickle名称..."
                                allowClear
                                enterButton={<SearchOutlined />}
                                onSearch={handleSearch}
                                loading={searchLoading}
                                style={{ width: 300, marginBottom: 16 }}
                            />
                            <div className="pagination-info">
                                <Text>共 {totalCases} 条记录</Text>
                            </div>
                        </div>

                        {cases.length > 0 ? (
                            <>
                                <div className="cases-list">
                                    {cases.map(caseItem => (
                                        <div key={caseItem.id} className="case-row">
                                            <div className="case-header">
                                                <Text strong>{caseItem.pkl_name}</Text>
                                            </div>
                                            <div className="visualizers-grid">
                                                {configIds.slice(0, 4).map(configId => (
                                                    <div key={configId} className="visualizer-container">
                                                        <div className="visualizer-header">
                                                            <div>
                                                                <Text strong ellipsis>
                                                                    {configs.find(c => c.id === configId)?.json_name || `配置 ${configId}`}
                                                                </Text>
                                                            </div>
                                                            <div>
                                                                <Text ellipsis>
                                                                    {configs.find(c => c.id === configId)?.pth_name || ``}
                                                                </Text>
                                                            </div>
                                                        </div>
                                                        <div style={{ position: 'relative', height: '90vh' }}>
                                                            <PickleVisualizer
                                                                evaluationCase={caseItem}
                                                                pdpPaths={getPathsForCaseAndConfig(caseItem.id, configId)}
                                                                height='90vh'
                                                            />
                                                            {loadingPaths && (
                                                                <div style={{
                                                                    position: 'absolute',
                                                                    top: 0,
                                                                    left: 0,
                                                                    right: 0,
                                                                    bottom: 0,
                                                                    display: 'flex',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    background: 'rgba(0, 0, 0, 0.3)'
                                                                }}>
                                                                    <Spin tip="加载轨迹数据..." />
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <div className="pagination-container">
                                    <Pagination
                                        current={currentPage}
                                        total={totalCases}
                                        pageSize={pageSize}
                                        onChange={handlePageChange}
                                        showSizeChanger={false}
                                        showQuickJumper
                                    />
                                </div>
                            </>
                        ) : (
                            <Empty description="没有找到符合条件的案例" />
                        )}
                    </>
                )}
            </Content>
        </Layout>
    );
};

export default ImageCompare;