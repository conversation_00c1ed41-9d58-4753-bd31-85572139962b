import React, { useState, useEffect } from 'react';
import { Layout, Typography, Table, Spin, Button, message } from 'antd';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { ArrowLeftOutlined } from '@ant-design/icons';
import './QuantitativeCompare.css';

const { Title, Text } = Typography;
const { Header, Content } = Layout;

interface EvaluationMetrics {
    ade_40: number;
    ade_200: number;
    fde_40: number;
    fde_200: number;
    static_collision: number;
    case_count: number;
}

interface ConfigMetric {
    configId: number;
    configName: string;
    pthName: string; // 添加 pthName 字段
    metrics: EvaluationMetrics;
}

const QuantitativeCompare: React.FC = () => {
    const { id: evaluationSetId } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const location = useLocation();
    const { configIds = [] } = location.state as { configIds: number[] } || {};

    const [loading, setLoading] = useState(true);
    const [compareData, setCompareData] = useState<ConfigMetric[]>([]);
    const [evaluationSetName, setEvaluationSetName] = useState<string>('');

    useEffect(() => {
        const fetchComparisonData = async () => {
            if (!configIds.length || !evaluationSetId) {
                message.error('参数错误，无法加载对比数据');
                navigate(-1);
                return;
            }

            setLoading(true);
            try {
                // 获取评测集信息
                const evaluationSetResponse = await axios.get(`/api/evaluation_sets/${evaluationSetId}`);
                if (evaluationSetResponse.data.success) {
                    setEvaluationSetName(evaluationSetResponse.data.evaluation_set.set_name);
                }

                // 获取每个配置的评测指标
                const metricsPromises = configIds.map(configId =>
                    axios.get('/api/evaluation_metrics', {
                        params: {
                            evaluation_set_id: evaluationSetId,
                            inference_config_id: configId
                        }
                    })
                );

                // 获取配置详情
                const configPromises = configIds.map(configId =>
                    axios.get(`/api/inference_config/${configId}`)
                );

                const metricsResults = await Promise.all(metricsPromises);
                const configResults = await Promise.all(configPromises);

                // 合并数据
                const data = configIds.map((configId, index) => {
                    const metricsData = metricsResults[index].data.success ? metricsResults[index].data.metrics : null;
                    const configData = configResults[index].data.success ? configResults[index].data : null;

                    return {
                        configId,
                        configName: configData ? configData.json_name : `配置 ${configId}`,
                        pthName: configData ? configData.pth_name : '-', // 添加 pthName
                        metrics: metricsData || {}
                    };
                });

                setCompareData(data);
            } catch (error) {
                console.error('Failed to fetch comparison data:', error);
                message.error('获取对比数据失败，请稍后再试');
            } finally {
                setLoading(false);
            }
        };

        fetchComparisonData();
    }, [evaluationSetId, configIds, navigate]);

    const columns = [
        {
            title: '推理配置',
            dataIndex: 'configName',
            key: 'configName',
        },
        {
            title: '模型文件名',
            dataIndex: 'pthName',
            key: 'pthName',
        },
        {
            title: 'ADE@40',
            dataIndex: ['metrics', 'ade_40'],
            key: 'ade_40',
            render: (value: number) => value ? value.toFixed(2) : '-',
            sorter: (a: ConfigMetric, b: ConfigMetric) =>
                (a.metrics?.ade_40 || 0) - (b.metrics?.ade_40 || 0),
        },
        {
            title: 'ADE@200',
            dataIndex: ['metrics', 'ade_200'],
            key: 'ade_200',
            render: (value: number) => value ? value.toFixed(2) : '-',
            sorter: (a: ConfigMetric, b: ConfigMetric) =>
                (a.metrics?.ade_200 || 0) - (b.metrics?.ade_200 || 0),
        },
        {
            title: 'FDE@40',
            dataIndex: ['metrics', 'fde_40'],
            key: 'fde_40',
            render: (value: number) => value ? value.toFixed(2) : '-',
            sorter: (a: ConfigMetric, b: ConfigMetric) =>
                (a.metrics?.fde_40 || 0) - (b.metrics?.fde_40 || 0),
        },
        {
            title: 'FDE@200',
            dataIndex: ['metrics', 'fde_200'],
            key: 'fde_200',
            render: (value: number) => value ? value.toFixed(2) : '-',
            sorter: (a: ConfigMetric, b: ConfigMetric) =>
                (a.metrics?.fde_200 || 0) - (b.metrics?.fde_200 || 0),
        },
        {
            title: '静态碰撞',
            dataIndex: ['metrics', 'static_collision'],
            key: 'static_collision',
            render: (value: number) => value !== undefined ? `${value}%` : '-',
            sorter: (a: ConfigMetric, b: ConfigMetric) =>
                (a.metrics?.static_collision || 0) - (b.metrics?.static_collision || 0),
        }
    ];

    const handleGoBack = () => {
        navigate(-1);
    };

    return (
        <Layout className="qualitative-compare">
            <Header className="compare-header">
                <Button
                    type="link"
                    icon={<ArrowLeftOutlined />}
                    onClick={handleGoBack}
                >
                    返回
                </Button>
                <Title level={3}>定性对比 - {evaluationSetName}</Title>
            </Header>
            <Content className="compare-content">
                {loading ? (
                    <div className="loading-container">
                        <Spin size="large" tip="加载对比数据..." />
                    </div>
                ) : (
                    <>
                        <div className="description">
                            <Text>
                                下表展示了不同推理配置在评测集 <strong>{evaluationSetName}</strong> 上的评测指标对比。
                                您可以点击表头对各指标进行排序。
                            </Text>
                        </div>
                        <Table
                            dataSource={compareData}
                            columns={columns}
                            rowKey="configId"
                            pagination={false}
                        />
                    </>
                )}
            </Content>
        </Layout>
    );
};

export default QuantitativeCompare;