import React, { useState, useCallback, useEffect, useRef } from "react";
import {
  List,
  Card,
  Button,
  Input,
  Pagination,
  Space,
  message,
  Spin,
  Tag,
  Typography,
  Slider,
  Radio,
} from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CopyOutlined,
  ExportOutlined,
} from "@ant-design/icons";
import axios from "axios";
import { useParams } from "react-router-dom";
import { useRequest } from "ahooks";

import RotatePickleVisualizer from "./RotatePickleVisualizer";
import ResizableSider from "../../components/ResizableSider";
import type { EvaluationCase } from "../../types";
import { useAuth } from "../../contexts/AuthContext";
import "../PdpPathAnnotation.css"; // 复用相同的样式
import { usePklPreloader } from "./hooks/usePklPreloader";
import { pklCache } from "./cache-manager";

const { Title, Text } = Typography;

// * -------------------------------------------------------------------------- fetcher

/**
 * 保存场景标签
 */
const saveSceneTags = async (params: {
  pkl_id: string | number;
  tags: string[];
  bag_id: string | number;
  delete_annotation?: boolean;
}) => {
  return axios.post("/api/annotation/lane-scene/scene-tags", params);
};

/**
 * 保存选中的中心线
 */
const saveCenterLines = async (params: {
  pkl_id: string | number;
  selected_centerline_ids: string[];
  bag_id: string | number;
  employee_id: string | null;
}) => {
  return axios.post("/api/annotation/lane-scene/selected-centerlines", params);
};

/**
 * 获取bag集详情
 */
const getBagSetDetail = async (bagSetId: string, params: any) => {
  return axios.get(`/api/bag-sets/${bagSetId}`, { params });
};

/**
 * 获取bag的pkl列表
 */
const getBagPkls = async (bagSetId: string, bagId: string | number) => {
  return axios.get(`/api/bag-sets/${bagSetId}/bags/${bagId}/pkls`);
};

/**
 * 获取pkl详情数据
 */
const getPklDetail = async (
  bagSetId: string,
  bagId: string | number,
  pklId: string | number,
) => {
  return axios.get(`/api/bag-sets/${bagSetId}/bags/${bagId}/pkl/${pklId}`);
};

/**
 * 获取pkl详情数据（带缓存）
 */
const getPklDetailWithCache = async (
  bagSetId: string,
  bagId: string | number,
  pklId: string | number,
) => {
  // 🎯 先尝试从缓存获取中心线数据
  const cachedCenterlineData = await pklCache.getCenterlineData(
    bagSetId,
    bagId,
    pklId,
  );

  if (cachedCenterlineData) {
    console.log("🎯 从缓存加载中心线数据:", { bagSetId, bagId, pklId });
    return {
      data: {
        success: true,
        centerline_id_list: cachedCenterlineData.centerline_id_list,
        selected_centerline_ids: cachedCenterlineData.selected_centerline_ids,
        // 这些字段从API获取，暂时设为null
        sign_image: null,
        ego_img_data: null,
        scene_tags: [],
      },
    };
  }

  // 缓存未命中，从API获取
  console.log("🌐 从API加载中心线数据:", { bagSetId, bagId, pklId });
  const response = await getPklDetail(bagSetId, bagId, pklId);

  // 如果API调用成功，缓存数据
  if (response.data.success) {
    await pklCache.setCenterlineData(
      bagSetId,
      bagId,
      pklId,
      response.data.centerline_id_list || [],
      response.data.selected_centerline_ids || [],
    );
  }

  return response;
};

// * -------------------------------------------------------------------------- component

/**
 * 加载 bag 集详情参数类型
 */
interface LoadBagSetDetailParams {
  page?: number;
  pageSize?: number;
  year?: string;
  month?: string;
  day?: string;
  hour?: string;
  minute?: string;
  second?: string;
  order?: "asc" | "desc";
  sort_by?: string;
  annotationFilter?: number | null;
  /** 是否标记为已完成 */
  status?: 0 | 1 | undefined;
  assigner_id: string;
}

const PdpLaneSceneAnnotation: React.FC = () => {
  const { bagSetId } = useParams<{ bagSetId?: string }>();
  const { user, isAuthenticated } = useAuth();
  console.log("🚀 ~ PdpLaneSceneAnnotation ~ user:", user);

  // 添加预加载Hook
  const { startPreload, stopPreload, clearBagCache, getCacheStats } =
    usePklPreloader();

  // 状态管理
  const [leftSiderWidth, setLeftSiderWidth] = useState(300);
  const [rightSiderWidth, setRightSiderWidth] = useState(650);

  const [selectedCenterlineIds, setSelectedCenterlineIds] = useState<string[]>(
    [],
  );
  const [annotatedPklCount, setAnnotatedPklCount] = useState(0);
  const [minAnnotatedPklIndex, setMinAnnotatedPklIndex] = useState<
    number | null
  >(null);
  const [maxAnnotatedPklIndex, setMaxAnnotatedPklIndex] = useState<
    number | null
  >(null);
  const [pklList, setPklList] = useState<any[]>([]);
  const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
  const selectedPklRef = useRef<EvaluationCase | null>(null);

  const [signImage, setSignImage] = useState<string | null>(null);
  const [recordedPklIndex, setRecordedPklIndex] = useState<number | null>(null);
  const [egoImgData, setEgoImgData] = useState<string | null>(null);
  const [loading, setLoading] = useState({
    pklList: false,
    annotation: false,
    data: false,
    markDirty: false,
    checkPkl: false,
  });

  // 添加分页状态
  const [bagPagination, setBagPagination] = useState({
    current: 1,
    pageSize: 8, // 每页显示10个bag
    total: 0, // bag总数
  });
  const [currentPklIndex, setCurrentPklIndex] = useState(0); // 当前PKL在列表中的索引
  const [pklAnnotationInfo, setPklAnnotationInfo] = useState<{
    updated_by: string | null;
    updated_at: string | null;
  } | null>(null);
  // bag集模式相关状态
  const [bagSetDetail, setBagSetDetail] = useState<any>(null);
  const [bagList, setBagList] = useState<any[]>([]);
  const [selectedBag, setSelectedBag] = useState<any>(null);

  const [year, setYear] = useState("");
  const [month, setMonth] = useState("");
  const [day, setDay] = useState("");
  const [hour, setHour] = useState("");
  const [minute, setMinute] = useState("");
  const [second, setSecond] = useState("");
  // 在现有的 useState hooks 中添加新状态
  const [sceneTag, setSceneTag] = useState<string[]>([]); // 改为数组
  // 复制模式相关状态
  const [copyMode, setCopyMode] = useState(false); // 是否开启复制模式
  const [copiedData, setCopiedData] = useState<{
    sceneTag: string[];
    selectedCenterlineIds: string[];
  } | null>(null); // 复制的数据
  const [isFiltered, setIsFiltered] = useState(false); // 是否处于筛选状态
  const [annotationFilterValue, setAnnotationFilterValue] =
    useState<string>(""); // 输入框的值
  const [sortField, setSortField] = useState<string>("bag_timestamp");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");

  // 分配状态 0:未完成, 1:已完成, undefined:全部
  const [isCompleted, setIsCompleted] = useState<0 | 1 | undefined>(undefined);

  // 处理复制模式切换
  const handleCopyModeToggle = () => {
    if (!copyMode) {
      // 开启复制模式，记录当前数据
      if (!selectedPkl) {
        message.error("请先选择PKL文件");
        return;
      }
      setCopiedData({
        sceneTag: [...sceneTag],
        selectedCenterlineIds: [...selectedCenterlineIds],
      });
      setCopyMode(true);
      setRecordedPklIndex(currentPklIndex); // 记录当前的 PKL 索引

      message.success(
        "复制模式已开启，已记录当前PKL的标注数据 (索引: ${currentPklIndex + 1})",
      );
    } else {
      // 关闭复制模式
      setCopyMode(false);
      setCopiedData(null);
      if (selectedPkl) {
        clearCurrentPklAnnotations();
      }
      message.success("复制模式已关闭");
    }
  };

  const clearCurrentPklAnnotations = async () => {
    if (!selectedPkl) return;

    try {
      // 清除场景标签
      await saveSceneTags({
        pkl_id: selectedPkl.id,
        tags: [],
        bag_id: selectedBag.id,
        delete_annotation: true,
      });

      // 清除选中的中心线 ID
      await saveCenterLines({
        pkl_id: selectedPkl.id,
        selected_centerline_ids: [],
        bag_id: selectedBag.id,
        employee_id: user?.employee_id || null,
      });

      // 清除缓存
      await clearBagCache(bagSetId!, selectedBag.id);

      // 更新前端状态
      setSceneTag([]);

      setSelectedCenterlineIds([]);
      // 如果有 changeCenterlineIds 回调，也通知它更新
      if (changeCenterlineIds) {
        changeCenterlineIds([]);
      }
      message.success("当前PKL的标注结果已清除");
    } catch (error) {
      console.error("清除标注结果失败:", error);
      message.error("清除标注结果失败，请稍后再试");
    }
  };
  // 应用复制的数据到当前PKL
  const applyCopiedData = async (
    pkl: EvaluationCase,
    validCenterlineIds: string[],
  ) => {
    if (!copiedData) return;

    try {
      // 应用场景标签
      // setSceneTag([...copiedData.sceneTag]);

      let response;

      // bag集模式
      response = await saveSceneTags({
        pkl_id: pkl.id,
        tags: copiedData.sceneTag,
        bag_id: selectedBag.id,
        delete_annotation: copiedData.sceneTag.length === 0,
      });

      if (response && !response.data.success) {
        console.error("保存场景标签失败");
      }
      // 应用选中的centerline IDs
      // setSelectedCenterlineIds([...copiedData.selectedCenterlineIds]);

      try {
        const requestData: any = {
          pkl_id: pkl.id,
          selected_centerline_ids: validCenterlineIds,
          employee_id: user?.employee_id || null,
        };

        requestData.bag_id = selectedBag.id;

        await saveCenterLines(requestData);

        // 清除该PKL的缓存
        await pklCache.clearBagCache(bagSetId!, selectedBag.id);

        message.success("复制的数据已应用到当前PKL");
      } catch (error) {
        console.error("保存选中centerlines失败:", error);
      }
      // 更新 pklList 中的标注状态
      const updatedPklList = pklList.map((item) => {
        if (item.id === pkl.id) {
          const wasAnnotated = item.has_scene_tags || item.has_centerline;
          const isNowAnnotated =
            copiedData.sceneTag.length > 0 || validCenterlineIds.length > 0;

          // 增量更新统计信息
          incrementalUpdateAnnotationStats(item, wasAnnotated, isNowAnnotated);

          return {
            ...item,
            has_scene_tags: copiedData.sceneTag.length > 0,
            has_centerline: validCenterlineIds.length > 0,
          };
        }
        return item;
      });

      setPklList(updatedPklList);
    } catch (error) {
      console.error("应用复制数据失败:", error);
      message.error("应用复制数据失败");
    }
  };

  // 添加处理场景标签选择的函数
  const handleSceneTagSelect = async (tag: string) => {
    if (!selectedPkl) {
      message.error("请先选择PKL文件");
      return;
    }

    // 切换标签选择状态（多选模式）
    const currentTags = [...sceneTag];
    const tagIndex = currentTags.indexOf(tag);

    let newTags: string[];
    if (tagIndex > -1) {
      // 如果标签已选中，则取消选择
      newTags = currentTags.filter((t) => t !== tag);
    } else {
      // 如果标签未选中，则添加选择
      newTags = [...currentTags, tag];
    }
    setSceneTag(newTags);
    try {
      console.log("提交场景标签：", newTags);
      let response;
      response = await saveSceneTags({
        pkl_id: selectedPkl.id,
        tags: newTags,
        bag_id: selectedBag.id,
        delete_annotation: newTags.length === 0,
      });
      if (response.data.success) {
        // 清除该PKL的缓存
        await pklCache.clearBagCache(bagSetId!, selectedBag.id);

        message.success(
          newTags.length > 0 ? "场景标签保存成功" : "场景标签已清除",
        );
        // 更新 pklList 中的标注状态
        const updatedPklList = pklList.map((pkl) => {
          if (pkl.id === selectedPkl.id) {
            const wasAnnotated = pkl.has_scene_tags || pkl.has_centerline;
            const isNowAnnotated = newTags.length > 0 || pkl.has_centerline;

            // 增量更新统计信息
            incrementalUpdateAnnotationStats(pkl, wasAnnotated, isNowAnnotated);

            return { ...pkl, has_scene_tags: newTags.length > 0 };
          }
          return pkl;
        });
        setPklList(updatedPklList);
      } else {
        message.error("保存场景标签失败");
        // 恢复之前的状态
        setSceneTag(sceneTag);
      }
    } catch (error) {
      console.error("Failed to save scene tag:", error);
      message.error("保存场景标签出错");
      // 恢复之前的状态
      setSceneTag(sceneTag);
    }
  };
  useEffect(() => {
    if (!isAuthenticated) {
      message.error("请先登录后再进行标注操作");
      return;
    }

    // 根据模式加载不同的数据
    loadBagSetDetail();
  }, [isAuthenticated, bagSetId]);

  /**
   * 加载 bag 集详情
   */
  const { run: loadBagSetDetail, loading: loadingBagSetDetail } = useRequest(
    async (
      params: LoadBagSetDetailParams = { assigner_id: user?.employee_id || "" },
    ) => {
      if (!bagSetId) throw new Error("缺少 bag 集 ID");

      const {
        page = bagPagination.current,
        pageSize = bagPagination.pageSize,
        year: year_filter = year,
        month: month_filter = month,
        day: day_filter = day,
        hour: hour_filter = hour,
        minute: minute_filter = minute,
        second: second_filter = second,
        order = sortOrder,
        sort_by = sortField,
        annotationFilter = null,
        status = undefined,
      } = params;

      const requestParams: Record<string, any> = {
        assigner_id: params.assigner_id,
        page,
        per_page: pageSize,
        year: year_filter,
        month: month_filter,
        day: day_filter,
        hour: hour_filter,
        minute: minute_filter,
        second: second_filter,
        order,
        sort_by,
        ...(annotationFilter !== null && {
          annotation_filter: annotationFilter,
        }),
        ...(status !== undefined && { status: status }),
      };

      const response = await getBagSetDetail(bagSetId, requestParams);

      if (!response.data.success) {
        throw new Error("加载bag集详情失败");
      }

      return response.data;
    },
    {
      manual: true,
      onSuccess: (data) => {
        setBagSetDetail(data.bag_set);
        setBagList(data.bags);
        setBagPagination({
          current: data.page || 1,
          pageSize: data.per_page || bagPagination.pageSize,
          total: data.total_bags || 0,
        });

        // 如果有bag，默认选择第一个
        if (data.bags && data.bags.length > 0) {
          handleBagSelect(data.bags[0]);
        }
      },
      onError: (error) => {
        console.error("Failed to load bag set detail:", error);
        message.error("加载bag集详情出错");
      },
    },
  );

  /**
   * 刷新 bag 集详情
   */
  const refreshBagSetDetail = (
    overrides: Partial<LoadBagSetDetailParams> = {},
  ) => {
    loadBagSetDetail({
      page: bagPagination.current,
      pageSize: bagPagination.pageSize,
      year,
      month,
      day,
      hour,
      minute,
      second,
      order: sortOrder,
      sort_by: sortField,
      status: isCompleted,
      assigner_id: user?.employee_id || "",
      ...overrides,
    });
  };

  const incrementalUpdateAnnotationStats = (
    pkl: any,
    wasAnnotated: boolean,
    isNowAnnotated: boolean,
  ) => {
    if (wasAnnotated === isNowAnnotated) {
      // 如果标注状态没有变化，则无需更新统计信息
      return;
    }

    if (isNowAnnotated) {
      // 如果当前 PKL 从未标注变为已标注
      setAnnotatedPklCount((prev) => prev + 1);

      // 更新最小和最大标注索引
      const currentIndex = pklList.findIndex((item) => item.id === pkl.id);
      if (
        minAnnotatedPklIndex === null ||
        currentIndex + 1 < minAnnotatedPklIndex
      ) {
        setMinAnnotatedPklIndex(currentIndex + 1); // +1 是为了显示从 1 开始的索引
      }
      if (
        maxAnnotatedPklIndex === null ||
        currentIndex + 1 > maxAnnotatedPklIndex
      ) {
        setMaxAnnotatedPklIndex(currentIndex + 1);
      }
    } else {
      // 如果当前 PKL 从已标注变为未标注
      setAnnotatedPklCount((prev) => prev - 1);

      // 重新计算最小和最大标注索引
      const annotatedIndices = pklList
        .map((item, index) =>
          item.id === pkl.id
            ? isNowAnnotated // 如果是当前 PKL，使用最新标注状态
              ? index
              : null
            : item.has_scene_tags || item.has_centerline
            ? index
            : null,
        )
        .filter((index) => index !== null) as number[];

      if (annotatedIndices.length > 0) {
        setMinAnnotatedPklIndex(Math.min(...annotatedIndices) + 1);
        setMaxAnnotatedPklIndex(Math.max(...annotatedIndices) + 1);
      } else {
        setMinAnnotatedPklIndex(null);
        setMaxAnnotatedPklIndex(null);
      }
    }
  };

  // 选择bag
  const handleBagSelect = async (bag: any) => {
    //如果就是当前bag，则不处理
    if (selectedBag && selectedBag.id === bag.id) {
      return;
    }

    // 停止之前的预加载
    stopPreload();

    // 清除与当前 bag 相关的状态
    setSelectedPkl(null);
    setCurrentPklIndex(0);
    setSceneTag([]);
    setSelectedCenterlineIds([]);
    setEgoImgData(null);
    setSignImage(null);
    setCopyMode(false);
    setCopiedData(null);
    setRecordedPklIndex(null);
    setSelectedBag(bag);

    try {
      const response = await getBagPkls(bagSetId!, bag.id);
      if (response.data.success) {
        // 现在API返回的格式与evaluation_set_with_cases对齐，直接使用cases
        const pklCases = response.data.cases || [];
        setPklList(pklCases);

        // 统计已标注的 PKL 数量
        const annotatedIndices = pklCases
          .map((pkl: any, index: number) =>
            pkl.has_scene_tags || pkl.has_centerline ? index : null,
          )
          .filter((index) => index !== null) as number[];

        const annotatedCount = annotatedIndices.length;
        setAnnotatedPklCount(annotatedCount);

        // 计算最小和最大索引
        if (annotatedCount > 0) {
          setMinAnnotatedPklIndex(Math.min(...annotatedIndices) + 1); // +1 显示为从 1 开始的索引
          setMaxAnnotatedPklIndex(Math.max(...annotatedIndices) + 1);
        } else {
          setMinAnnotatedPklIndex(null);
          setMaxAnnotatedPklIndex(null);
        }

        // 如果有pkl，默认选择第一个
        if (pklCases.length > 0) {
          setCurrentPklIndex(0);
          // 这里可以选择是否自动加载第一个pkl的数据
          handlePklSelect(pklCases[0], bag);

          // 🎯 启动后台预加载（延迟2秒，让用户先看到第一个PKL）
          setTimeout(() => {
            // startPreload(bagSetId!, bag.id, pklCases);
          }, 500);
        }
      } else {
        message.error("加载bag的pkl列表失败");
      }
    } catch (error) {
      console.error("Failed to load bag pkls:", error);
      message.error("加载bag的pkl列表出错");
    }
  };

  // 处理bag分页变化
  const handleBagPageChange = (page: number, pageSize?: number) => {
    refreshBagSetDetail({
      page,
      pageSize: pageSize || bagPagination.pageSize,
    });
  };

  // 选择PKL文件时加载数据
  const handlePklSelect = async (pkl: EvaluationCase, bag?: any) => {
    setSelectedPkl(pkl);
    setSignImage(null);
    setEgoImgData(null);
    setSelectedCenterlineIds([]);
    setSceneTag([]); // 重置场景标签
    setPklAnnotationInfo(null);
    setLoading((prev) => ({ ...prev, data: true }));

    // 更新当前PKL在列表中的索引
    const index = pklList.findIndex((item) => item.id === pkl.id);
    if (index !== -1) {
      setCurrentPklIndex(index);
      const selectedPklFromList = pklList[index];

      // 设置 PKL 标注信息
      setPklAnnotationInfo({
        updated_by: selectedPklFromList.updated_by || null,
        updated_at: selectedPklFromList.updated_at || null,
      });
    }
    // 如果是复制模式，立即设置复制的数据
    if (!copyMode) {
      // 非复制模式才重置数据
      setSelectedCenterlineIds([]);
      setSceneTag([]);
    }

    try {
      let response;
      // bag集模式：调用bag集相关API
      const selectedBagToUse = bag || selectedBag;

      // 使用带缓存的函数
      response = await getPklDetailWithCache(
        bagSetId!,
        selectedBagToUse.id,
        pkl.id,
      );

      if (response.data.success) {
        if (response.data.sign_image) {
          setSignImage(response.data.sign_image);
        } else {
          setSignImage(null);
        }
        if (response.data.ego_img_data) {
          setEgoImgData(response.data.ego_img_data);
        } else {
          setEgoImgData(null);
        }
        let CenterlineIds;
        // 存储 centerline_id_list
        if (response.data.centerline_id_list) {
          CenterlineIds = response.data.centerline_id_list;
        }
        if (!copyMode) {
          // 设置选中的centerline ID
          if (response.data.selected_centerline_ids) {
            setSelectedCenterlineIds(response.data.selected_centerline_ids);
            console.log(
              "Selected Centerline IDs:",
              response.data.selected_centerline_ids,
            );
          } else {
            setSelectedCenterlineIds([]);
          }
          if (response.data.scene_tags) {
            setSceneTag(response.data.scene_tags);
            console.log("Scene Tag:", response.data.scene_tags);
          } else {
            setSceneTag([]);
          }
        } else if (copyMode && copiedData) {
          const validCenterlineIds = copiedData.selectedCenterlineIds.filter(
            (id) => CenterlineIds.includes(id),
          );
          setSelectedCenterlineIds(validCenterlineIds);
          setSceneTag(copiedData.sceneTag);
          applyCopiedData(pkl, validCenterlineIds);

          console.log("Valid Centerline IDs:", validCenterlineIds);
        }
      } else {
        message.error("加载车道场景数据失败");
      }
      // // 如果开启了复制模式，自动应用复制的数据
      // if (copyMode && copiedData) {
      //   applyCopiedData(pkl);
      // }
    } catch (error) {
      console.error("Failed to load lane scene data:", error);
      message.error("加载车道场景数据出错");
    } finally {
      setLoading((prev) => ({ ...prev, data: false }));
    }
  };

  useEffect(() => {
    selectedPklRef.current = selectedPkl;
  }, [selectedPkl]);

  const changeCenterlineIds = useCallback(
    async (ids: string[]) => {
      //   setSelectedCenterlineIds(ids);
      setSelectedCenterlineIds([...ids]);

      // 如果有选中的 PKL 文件，保存到后端
      const currentSelectedPkl = selectedPklRef.current;
      if (currentSelectedPkl) {
        try {
          console.log("Sending selected centerline IDs to backend:", ids);
          await saveCenterLines({
            pkl_id: currentSelectedPkl.id,
            selected_centerline_ids: ids,
            bag_id: selectedBag.id,
            employee_id: user?.employee_id || null,
          });

          // 清除该PKL的缓存
          await clearBagCache(bagSetId!, selectedBag.id);

          // 更新 pklList 中的标注状态
          const updatedPklList = pklList.map((pkl) => {
            if (pkl.id === currentSelectedPkl.id) {
              const wasAnnotated = pkl.has_scene_tags || pkl.has_centerline;
              const isNowAnnotated = ids.length > 0 || pkl.has_scene_tags;

              // 增量更新统计信息
              incrementalUpdateAnnotationStats(
                pkl,
                wasAnnotated,
                isNowAnnotated,
              );

              return { ...pkl, has_centerline: ids.length > 0 };
            }
            return pkl;
          });
          setPklList(updatedPklList);
        } catch (error) {
          console.error("保存选中centerlines失败:", error);
          message.error("保存选中centerlines失败");
        }
      }
    },
    [selectedPkl, user?.employee_id, selectedBag?.id],
  ); // 添加所有依赖项

  // 处理滑动条变化
  const handleSliderChange = (value: number) => {
    if (copyMode) return;
    if (pklList.length > 0 && value >= 0 && value < pklList.length) {
      const pkl = pklList[value];
      handlePklSelect(pkl);
    }
  };

  // 添加导出处理函数
  const handleExportLaneSceneAnnotations = async () => {
    if (!bagSetId) {
      message.error("缺少bag集ID");
      return;
    }
    try {
      const exportUrl = `/api/bag-sets/export-bag-set/${bagSetId}`;
      window.open(exportUrl, "_blank");
    } catch (error) {
      console.error("Export failed:", error);
      message.error("导出失败，请稍后再试");
    }
  };

  const handleDateTimeFilter = () => {
    refreshBagSetDetail({ page: 1 }); // 重置到第一页
  };

  const handleClearDateTimeFilter = () => {
    setYear("");
    setMonth("");
    setDay("");
    setHour("");
    setMinute("");
    setSecond("");
    refreshBagSetDetail({
      page: 1,
      year: "",
      month: "",
      day: "",
      hour: "",
      minute: "",
      second: "",
    });
  };

  const handleSort = (sort: string) => {
    const newOrder =
      sortField === sort && sortOrder === "desc" ? "asc" : "desc";
    setSortField(sort);
    setSortOrder(newOrder);

    // 重新加载数据
    refreshBagSetDetail({
      page: 1,
      sort_by: sort,
      order: newOrder,
    });
  };

  const handleComplete = (value: 0 | 1 | undefined) => {
    const newOrder =
      isCompleted === value && sortOrder === "desc" ? "asc" : "desc";
    setIsCompleted(value);
    refreshBagSetDetail({
      page: 1,
      status: value,
      order: newOrder,
    });
  };

  const handleAnnotationFilter = () => {
    const filterValue = annotationFilterValue.trim();
    const isNumber = !isNaN(Number(filterValue));
    setIsFiltered(true);

    const annotationFilter =
      isNumber && filterValue !== "" ? Number(filterValue) : 0;

    refreshBagSetDetail({
      page: 1,
      annotationFilter,
    });
  };

  const handleResetFilter = () => {
    setAnnotationFilterValue("");
    setIsFiltered(false);

    refreshBagSetDetail({
      page: 1,
      annotationFilter: null,
    });
  };

  // 在其他处理函数附近添加 handlePklNavigation 函数
  const handlePklNavigation = async (step: number) => {
    if (pklList.length === 0) return;

    const newIndex = currentPklIndex + step;
    if (newIndex >= 0 && newIndex < pklList.length) {
      const targetIndices = Array.from(
        { length: Math.abs(step) },
        (_, i) => currentPklIndex + (step > 0 ? i + 1 : -(i + 1)),
      ).filter((index) => index >= 0 && index < pklList.length);

      for (const index of targetIndices) {
        const pkl = pklList[index];
        await new Promise((resolve) => {
          handlePklSelect(pkl); // 渲染当前 PKL
          setTimeout(() => {
            resolve(null); // 等待完成
          }, 1000); // 每个 PKL 渲染间隔 500 毫秒
        });
      }
    }
  };

  const { run: setBagCompleted, loading: setBagCompletedLoading } = useRequest(
    (params: { status: number; bag_name: string; bag_set_id: number }) =>
      axios.post("/api/bags/status", params),
    {
      manual: true,
      onSuccess: () => {
        message.success("设置 bag 为完成状态成功");
        refreshBagSetDetail({ page: 1 });
      },
      onError: () => {
        message.error("设置 bag 为完成状态失败");
      },
    },
  );

  // 设置 bag 为完成状态
  const handleMarkBagCompleted = () => {
    setBagCompleted({
      status: 1,
      bag_name: selectedBag.name,
      bag_set_id: Number(bagSetId!),
    });
  };

  // 只在组件挂载时清理过期缓存
  useEffect(() => {
    if (bagSetId && selectedBag) {
      pklCache.clearExpiredBagCache(bagSetId, selectedBag.id);
    }
  }, [bagSetId, selectedBag]);

  // 在组件卸载时停止预加载
  useEffect(() => {
    return () => {
      stopPreload();
    };
  }, [stopPreload]);

  return (
    <div className="lane-scene-annotation-layout h-[calc(100vh - 50px)] flex">
      {/* 左侧列表 */}
      <ResizableSider
        minWidth={200}
        maxWidth={600}
        position="left"
        width={leftSiderWidth}
        onResize={setLeftSiderWidth}
        className="flex flex-col h-full! border-r border-[#d9d9d9]"
      >
        <div className="h-full flex flex-col overflow-hidden">
          {/* 请求参数 */}
          <div className="shrink-0 p-2">
            <Space direction="vertical" style={{ width: "100%" }} size="small">
              {bagSetDetail && (
                <div className="flex gap-2 justify-between">
                  <div className="text-sm text-[#1890ff] border border-[#1890ff] rounded px-2 py-[1px]">
                    {bagSetDetail.set_name}
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="small"
                      type="default"
                      icon={<ExportOutlined />}
                      onClick={handleExportLaneSceneAnnotations}
                    >
                      导出标注
                    </Button>
                    <Button
                      size="small"
                      type="primary"
                      loading={setBagCompletedLoading}
                      onClick={handleMarkBagCompleted}
                    >
                      完成
                    </Button>
                  </div>
                </div>
              )}
              <div className="flex flex-col gap-1 mb-2 rounded border border-[#d9d9d9] p-2">
                <Text strong className="text-xs! text-[#666] mb-1">
                  按日期时间过滤:
                </Text>
                <div
                  style={{ display: "flex", gap: "4px", marginBottom: "6px" }}
                >
                  <Input
                    placeholder="年"
                    value={year}
                    onChange={(e) => setYear(e.target.value)}
                    size="small"
                    style={{ flex: 1 }}
                    maxLength={4}
                  />
                  <Input
                    placeholder="月"
                    value={month}
                    onChange={(e) => setMonth(e.target.value)}
                    size="small"
                    style={{ flex: 1 }}
                    maxLength={2}
                  />
                  <Input
                    placeholder="日"
                    value={day}
                    onChange={(e) => setDay(e.target.value)}
                    size="small"
                    style={{ flex: 1 }}
                    maxLength={2}
                  />
                </div>
                <div
                  style={{ display: "flex", gap: "4px", marginBottom: "6px" }}
                >
                  <Input
                    placeholder="时"
                    value={hour}
                    onChange={(e) => setHour(e.target.value)}
                    size="small"
                    style={{ flex: 1 }}
                    maxLength={2}
                  />
                  <Input
                    placeholder="分"
                    value={minute}
                    onChange={(e) => setMinute(e.target.value)}
                    size="small"
                    style={{ flex: 1 }}
                    maxLength={2}
                  />
                  <Input
                    placeholder="秒"
                    value={second}
                    onChange={(e) => setSecond(e.target.value)}
                    size="small"
                    style={{ flex: 1 }}
                    maxLength={2}
                  />
                </div>
                <div style={{ display: "flex", gap: "4px" }}>
                  <Button
                    type="primary"
                    size="small"
                    onClick={handleDateTimeFilter}
                    style={{ flex: 1 }}
                  >
                    应用
                  </Button>
                  <Button
                    size="small"
                    onClick={handleClearDateTimeFilter}
                    style={{ flex: 1 }}
                  >
                    清空
                  </Button>
                </div>
              </div>
            </Space>
            <div className="flex flex-col gap-2 mb-2 rounded border border-[#d9d9d9] p-2">
              <Text strong className="text-xs! text-[#666]">
                标注结果筛选:
              </Text>
              <div className="flex gap-2">
                <Input
                  size="small"
                  className="flex-1"
                  placeholder="大于该数量的 bag"
                  value={annotationFilterValue}
                  onChange={(e) => setAnnotationFilterValue(e.target.value)}
                />
                <Button
                  size="small"
                  type={isFiltered ? "primary" : "default"}
                  onClick={handleAnnotationFilter}
                >
                  筛选
                </Button>
                <Button size="small" onClick={handleResetFilter}>
                  重置
                </Button>
              </div>
            </div>
            <div className="flex flex-col gap-2 w-full justify-center items-center">
              <div className="flex gap-1 items-center">
                <Button
                  size="small"
                  onClick={() => handleSort("bag_timestamp")}
                  type={sortField === "bag_timestamp" ? "primary" : "default"}
                >
                  T
                </Button>
                <Button
                  size="small"
                  onClick={() => handleSort("uturn")}
                  type={sortField === "uturn" ? "primary" : "default"}
                >
                  U
                </Button>
                <Button
                  size="small"
                  onClick={() => handleSort("left_turn")}
                  type={sortField === "left_turn" ? "primary" : "default"}
                >
                  Left
                </Button>
                <Button
                  size="small"
                  onClick={() => handleSort("right_turn")}
                  type={sortField === "right_turn" ? "primary" : "default"}
                >
                  Right
                </Button>
                <Button
                  size="small"
                  onClick={() => handleSort("straight")}
                  type={sortField === "straight" ? "primary" : "default"}
                >
                  S
                </Button>
              </div>

              <div className="flex items-center gap-1">
                <Radio.Group
                  size="small"
                  optionType="button"
                  buttonStyle="solid"
                  value={isCompleted}
                  onChange={(e) => handleComplete(e.target.value)}
                >
                  <Radio value={undefined}>全部</Radio>
                  <Radio value={1}>已完成</Radio>
                  <Radio value={0}>未完成</Radio>
                </Radio.Group>
              </div>
            </div>
          </div>

          <div className="flex flex-col flex-1 overflow-auto">
            <div className="flex-1 overflow-hidden">
              <List
                loading={loading.pklList}
                dataSource={bagList}
                split={false}
                style={{
                  height: "100%",
                  overflowY: "auto",
                  padding: 0,
                }}
                renderItem={(bag) => (
                  <List.Item
                    onClick={() => handleBagSelect(bag)}
                    className={`
                    !p-0 cursor-pointer transition-colors duration-200
                    border-b border-gray-100 last:border-b-0
                    hover:bg-blue-50
                    ${
                      selectedBag?.id === bag.id
                        ? "bg-blue-100 border-blue-200"
                        : ""
                    }
                  `}
                  >
                    <div className="w-full p-2 flex flex-col gap-2">
                      <div className="flex gap-1 items-center">
                        <div className="font-medium text-sm text-gray-800 overflow-hidden line-clamp-2 break-words text-center px-4">
                          {bag.bag_name}
                        </div>

                        <div
                          className={`
                            shrink-0 flex items-center gap-1 p-1 rounded
                            ${
                              bag.status === 1
                                ? "bg-green-50 border-green-200 text-green-700"
                                : "bg-yellow-50 border-gray-200 text-gray-700"
                            }
                          `}
                        >
                          {bag.status === 1 ? (
                            <>
                              <CheckCircleOutlined className="text-xs" />
                              <span className="text-xs font-medium">完成</span>
                            </>
                          ) : (
                            <>
                              <CloseCircleOutlined className="text-xs" />
                              <span className="text-xs font-medium">
                                待完成
                              </span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex gap-2 justify-center">
                        <div className="text-xs text-gray-500">
                          <Tag color="#108ee9">{bag.pkl_count || 0} PKLs</Tag>
                        </div>

                        <div className="flex text-xs text-gray-600">
                          <Tag>U: {bag.uturn || 0}</Tag>
                          <Tag>L: {bag.left_turn || 0}</Tag>
                          <Tag>R: {bag.right_turn || 0}</Tag>
                          <Tag>S: {bag.straight || 0}</Tag>
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </div>

            <div className="flex shrink-0 justify-center p-2 border-t border-[#f0f0f0] shadow-[0_-1px_4px_rgba(0,0,0,0.1)]">
              <Pagination
                current={bagPagination.current}
                pageSize={bagPagination.pageSize}
                total={bagPagination.total}
                onChange={handleBagPageChange}
                showSizeChanger={false}
                size="small"
                simple
              />
            </div>
          </div>
        </div>
      </ResizableSider>

      {/* 中间可视化区域 */}
      <div
        className="lane-visualization-content"
        style={{
          flex: 1,
          background: "#fff",
          borderRight: "1px solid #f0f0f0",
          overflow: "hidden",
          position: "relative", // 添加相对定位以便滑动条绝对定位
        }}
      >
        {selectedPkl ? (
          <div className="visualization-container w-full h-full">
            <div className="visualization-area w-full h-full relative">
              <div className="w-full h-[calc(100vh-145px)] relative">
                {/* 3D可视化区域 - 占满整个空间 */}
                <RotatePickleVisualizer
                  pdpPaths={{}}
                  highlightPathIndex={null}
                  bagId={selectedBag?.id}
                  evaluationCase={selectedPkl}
                  changeCenterlineIds={changeCenterlineIds}
                  selectedCenterlineIds={selectedCenterlineIds}
                />

                {/* 图片区域 - 绝对定位在右下角*/}
                {signImage && (
                  <div className="absolute w-[200px] h-[200px] max-w-[40%] max-h-[250px] bg-white/95 rounded shadow overflow-hidden z-10 p-1 bottom-4 right-4">
                    <img
                      src={`data:image/png;base64,${signImage}`}
                      alt="车道标志图"
                      style={{
                        width: "100%",
                        height: "auto",
                        maxHeight: "250px",
                        objectFit: "contain", // 保持图片比例
                        display: "block",
                      }}
                    />
                  </div>
                )}

                {loading.data && (
                  <div className="absolute inset-0 flex justify-center items-center h-full bg-white/20" />
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex justify-center items-center h-full text-[#999]">
            <Text>请从左侧选择一个PKL文件</Text>
          </div>
        )}

        {/* 导航滑动条 */}
        {pklList.length > 0 && selectedBag && (
          <div className="bg-white/80 shadow-md relative overflow-hidden z-10 p-4 mx-2 mb-4 rounded">
            {/* 添加导航按钮 */}
            <div className="flex justify-center gap-2 mb-2">
              {/* 后退按钮 */}
              {[-20, -15, -10, -5, -1].map((step) => (
                <Button
                  key={step}
                  size="small"
                  onClick={() => handlePklNavigation(step)}
                  disabled={currentPklIndex <= 0}
                >
                  {step === -1 ? "<" : step.toString()}
                </Button>
              ))}

              {/* 输入框 */}
              <Input
                type="number"
                size="small"
                className="w-16! text-center"
                value={currentPklIndex + 1} // 显示当前索引（+1 是为了显示从 1 开始的索引）
                onChange={(e) => {
                  if (copyMode) {
                    message.warning("复制模式开启时无法跳转");
                    return;
                  }
                  const value = parseInt(e.target.value, 10);
                  if (!isNaN(value) && value >= 1 && value <= pklList.length) {
                    setCurrentPklIndex(value - 1); // 更新索引（-1 是为了匹配数组索引）
                    handlePklSelect(pklList[value - 1]);
                  }
                }}
                onPressEnter={() => {
                  const targetPkl = pklList[currentPklIndex];
                  if (targetPkl) {
                    handlePklSelect(targetPkl); // 切换到指定的 PKL
                  } else {
                    message.error("无效的 PKL 索引");
                  }
                }}
              />
              <span>/ {pklList.length}</span>

              {/* 前进按钮 */}
              {[1, 5, 10, 15, 20].map((step) => (
                <Button
                  key={step}
                  size="small"
                  onClick={() => handlePklNavigation(step)}
                  disabled={currentPklIndex >= pklList.length - 1}
                >
                  {step === 1 ? ">" : `+${step}`}
                </Button>
              ))}
            </div>

            <Slider
              min={0}
              max={pklList.length - 1}
              value={currentPklIndex}
              keyboard={true}
              onChange={handleSliderChange}
              tooltip={{
                formatter: (value) => {
                  if (value !== undefined && pklList[value]) {
                    return `${value + 1}/${pklList.length}: ${
                      pklList[value].pkl_name
                    }`;
                  }
                  return "";
                },
              }}
              style={{ margin: 0 }}
            />

            <div className="mb-2 text-xs text-[#666]">
              <div>
                {selectedBag && `bag: ${selectedBag.bag_name} - `}
                PKL: {currentPklIndex + 1} / {pklList.length}
              </div>
              {selectedPkl && (
                <span style={{ marginLeft: "8px", fontFamily: "monospace" }}>
                  {selectedPkl.pkl_name}
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 右侧车道场景标注面板 */}
      <ResizableSider
        minWidth={300}
        maxWidth={1000}
        position="right"
        width={rightSiderWidth}
        onResize={setRightSiderWidth}
        className="h-full! overflow-hidden border-l border-[#d9d9d9]"
      >
        <div className="annotation-panel">
          {/* 添加统计信息 */}
          <div className="mb-4 p-2 bg-[#f9f9f9] border border-[#d9d9d9] rounded">
            <Typography.Text strong className="text-sm">
              当前 Bag 标注统计
            </Typography.Text>
            <div className="mt-1 text-xs text-[#666]">
              <div>{`已标注的 PKL 数量: ${annotatedPklCount}`}</div>
              {annotatedPklCount > 0 && (
                <div>
                  {`最小标注索引: ${minAnnotatedPklIndex}  最大标注索引: ${maxAnnotatedPklIndex}`}
                </div>
              )}
            </div>
          </div>

          <div className="annotation-header">
            <Title level={5} className="mb-4">
              车道场景标注
            </Title>
            {/* 标注信息显示在标题右侧 */}
            {pklAnnotationInfo &&
              (pklAnnotationInfo.updated_by ||
                pklAnnotationInfo.updated_at) && (
                <div className="text-xs text-[#666] min-w-[120px] text-right">
                  {pklAnnotationInfo.updated_by && (
                    <div>标注人: {pklAnnotationInfo.updated_by}</div>
                  )}
                  {pklAnnotationInfo.updated_at && (
                    <div>
                      更新时间:{" "}
                      {new Date(pklAnnotationInfo.updated_at).toLocaleString(
                        "zh-CN",
                        {
                          month: "2-digit",
                          day: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                        },
                      )}
                    </div>
                  )}
                </div>
              )}
            {/* 复制模式按钮 */}
            <div className="mb-4">
              <Button
                type={copyMode ? "primary" : "default"}
                icon={<CopyOutlined />}
                onClick={handleCopyModeToggle}
                className="w-full"
                style={{
                  backgroundColor: copyMode ? "#52c41a" : undefined,
                  borderColor: copyMode ? "#52c41a" : undefined,
                }}
              >
                {copyMode ? "复制模式已开启" : "开启复制模式"}
              </Button>
            </div>
          </div>

          {recordedPklIndex !== null && (
            <Button
              type="primary"
              style={{ marginTop: "8px" }}
              onClick={() => {
                if (copyMode) {
                  setCopyMode(false);
                  setCopiedData(null);
                  message.info("已退出复制模式");
                }
                const targetPkl = pklList[recordedPklIndex];
                if (targetPkl) {
                  handlePklSelect(targetPkl); // 直接跳转到对应的 PKL
                } else {
                  message.error("无法找到记录的 PKL 文件");
                }
              }}
            >
              跳转到第{recordedPklIndex + 1}个PKL
            </Button>
          )}

          {/* 标注侧面板 */}
          {!selectedPkl ? (
            <div className="empty-annotation">
              <Text>请先选择一个PKL文件</Text>
            </div>
          ) : loading.data ? (
            <div className="loading-paths">
              <Spin tip="加载数据..." />
            </div>
          ) : (
            <div className="lane-annotation-container">
              {/* 选中的中心线 */}
              <CenterlineIds lineIds={selectedCenterlineIds} />

              {/* 场景标签选择 */}
              <SceneTagAnnotation
                sceneTag={sceneTag}
                handleSceneTagSelect={handleSceneTagSelect}
              />

              {/* egoImgData - 放置在右侧面板下方 */}
              <EgoImg img={egoImgData} />
            </div>
          )}
        </div>
      </ResizableSider>
    </div>
  );
};

// * -------------------------------------------------------------------------- widgets

/**
 * 中心线 ID 列表
 */
const CenterlineIds: React.FC<{ lineIds: string[] }> = ({ lineIds }) => {
  if (lineIds.length === 0) {
    return <Card title="中心线id" size="small" className="mb-4!" />;
  }

  const count = lineIds.length;

  return (
    <Card title="中心线id" size="small" className="mb-4!">
      <div className="flex flex-wrap gap-2">
        {lineIds.map((id) => (
          <div
            key={id}
            className="px-2 py-1 bg-[#f5f5f5] rounded text-xs font-mono"
          >
            {id}
          </div>
        ))}
      </div>
      <div className="mt-2 text-xs text-[#666]">总计: {count} 条中心线</div>
    </Card>
  );
};

// * ------------------------------------------------------

// 定义场景标签选项
const sceneTagOptions = [
  // 进路口标签
  { value: "进路口-直行", label: "进路口-直行", category: "进路口" },
  { value: "进路口-左转", label: "进路口-左转", category: "进路口" },
  { value: "进路口-右转", label: "进路口-右转", category: "进路口" },
  { value: "进路口-左掉头", label: "进路口-左掉头", category: "进路口" },

  // 出路口标签
  { value: "出路口-直行", label: "出路口-直行", category: "出路口" },
  { value: "出路口-左转", label: "出路口-左转", category: "出路口" },
  { value: "出路口-右转", label: "出路口-右转", category: "出路口" },
  { value: "出路口-左掉头", label: "出路口-左掉头", category: "出路口" },

  // 新增标签
  { value: "直行", label: "直行", category: "场景" },
  { value: "SplitY", label: "SplitY", category: "场景" },
  { value: "MergeY", label: "MergeY", category: "场景" },
];

const sceneTagOptionsByCategory = {
  进路口: sceneTagOptions.filter((option) => option.category === "进路口"),
  出路口: sceneTagOptions.filter((option) => option.category === "出路口"),
  场景: sceneTagOptions.filter((option) => option.category === "场景"),
};

/**
 * 场景标签标注
 */
const SceneTagAnnotation: React.FC<{
  sceneTag: string[];
  handleSceneTagSelect: (tag: string) => void;
}> = ({ sceneTag, handleSceneTagSelect }) => {
  return (
    <Card title="场景标签" size="small" className="mb-4">
      <div className="flex gap-4">
        {Object.entries(sceneTagOptionsByCategory).map(
          ([categoryName, options]) => (
            <div key={categoryName} className="flex-1">
              <Text strong className="text-xs! mb-2 block">
                {categoryName}
              </Text>
              <div className="flex flex-col gap-2">
                {options.map((option) => (
                  <Button
                    key={option.value}
                    size="small"
                    type={
                      sceneTag.includes(option.value) ? "primary" : "default"
                    }
                    onClick={() => handleSceneTagSelect(option.value)}
                    className="text-xs! h-auto px-2"
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>
          ),
        )}
      </div>
    </Card>
  );
};

// * ------------------------------------------------------

/**
 * 自车图片容器
 */
const EgoImg: React.FC<{ img?: string | null }> = ({ img }) => {
  if (!img) {
    return null;
  }

  return (
    <div className="mt-4 p-2 bg-[#f9f9f9] border border-[#d9d9d9] rounded text-center relative">
      <img
        src={`data:image/jpeg;base64,${img}`}
        alt="ego"
        style={{
          maxWidth: "100%",
          maxHeight: "400px",
          objectFit: "contain",
        }}
      />
    </div>
  );
};

// * -------------------------------------------------------------------------- export

export default PdpLaneSceneAnnotation;
