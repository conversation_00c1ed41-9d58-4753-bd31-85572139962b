export interface VisualizationData {
  polylines: {
    id: string;
    points: number[][];
    color: string;
    thickness: number;
    type: string;
  }[];
  polygons: {
    id: string;
    vertices: number[][];
    color: string;
    opacity: number;
    type: string;
  }[];
  arrows: {
    id: string;
    start: number[];
    end: number[];
    color: string;
    headSize: number;
  }[];
  texts: {
    id: string;
    position: number[];
    content: string;
    fontSize: number;
    color: string;
  }[];
  objects: {
    id: string;
    position: number[];
    dimensions: {
      width: number;
      height: number;
      depth: number;
    };
    rotation: number[];
    color: string;
    type: string;
    opacity: number;
  }[];
  tbt?: {
    dist: number;
    maneuver: string;
    lane_action: string;
    next_cross_infos?: number;
  };
  metadata: {
    filename: string;
    timestamp: string;
    ego_heading: number;
  };
  traffic_lights?: {
    color: string;
    sign: string;
  };
  future_obj_infos?: {
    id: string;
    points: number[][]; // 轨迹点数组，每个点是[x, y, z]
    timestamps: number[]; // 时间戳数组
    rotation: number[]; // 朝向角度数组
  }[];
  recommend_lanes?: {
    id: string;
    start: number[];
    end: number[];
    color: string;
    headSize: number;
  }[];
  future_obj_pred?: {
    id: string;
    points: number[][]; // 预测轨迹点数组，每个点是[x, y, z]
    timestamps: number[]; // 时间戳数组
  }[];
  centerlines_for_drivable?: {
    id: string;
    points: number[][]; // 中心线点坐标数组
    points_recommend_label: number; // 推荐标签，1为黄色，0为白色
  }[];
}
