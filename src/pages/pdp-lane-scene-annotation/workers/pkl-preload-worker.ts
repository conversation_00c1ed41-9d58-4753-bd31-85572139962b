import { get, set } from "idb-keyval";

const CACHE_MAX_AGE = 30 * 60 * 1000; // 30分钟过期
const BATCH_SIZE = 3; // 每批处理的PKL数量
const BATCH_DELAY = 1000; // 批次间延迟（毫秒）

// 🎯 纯函数 - 获取PKL详情数据
async function fetchPklDetails(
  baseUrl: string,
  bagSetId: string,
  bagId: string | number,
  pklId: string | number
) {
  const apiUrl = `${baseUrl}/api/bag-sets/${bagSetId}/bags/${bagId}/pkl/${pklId}`;
  const response = await fetch(apiUrl);

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
  }

  const data = await response.json();
  
  if (!data.success) {
    throw new Error(`API返回失败状态: ${JSON.stringify(data)}`);
  }

  return {
    centerline_id_list: data.centerline_id_list || [],
    selected_centerline_ids: data.selected_centerline_ids || [],
  };
}

// 🎯 纯函数 - 获取可视化数据
async function fetchVisualizationData(
  baseUrl: string,
  pklDir: string,
  pklName: string
) {
  const fullPath = `${pklDir}/${pklName}`;
  const response = await fetch(`${baseUrl}/api/visualize-pickle-rotate`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      pickle_path: fullPath,
      config: {},
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
  }

  return await response.json();
}

// 🎯 Worker版本的缓存操作（优化版本 - 独立存储）

// 🎯 生成缓存键
function getVisualizationKey(bagSetId: string, bagId: string | number, pklId: string | number) {
  return `pkl-viz-${bagSetId}-${bagId}-${pklId}`;
}

function getCenterlineKey(bagSetId: string, bagId: string | number, pklId: string | number) {
  return `pkl-centerline-${bagSetId}-${bagId}-${pklId}`;
}

async function getCachedVisualizationData(bagSetId: string, bagId: string | number, pklId: string | number) {
  try {
    const key = getVisualizationKey(bagSetId, bagId, pklId);
    const cachedItem = await get(key);
    
    if (!cachedItem || Date.now() - cachedItem.timestamp > CACHE_MAX_AGE) {
      return null;
    }
    
    console.log(`✅ Worker: 可视化缓存命中: ${bagSetId}-${bagId}-${pklId}`);
    return cachedItem.data;
  } catch (error) {
    console.error("❌ Worker: 获取可视化缓存失败:", error);
    return null;
  }
}

async function setCachedVisualizationData(bagSetId: string, bagId: string | number, pklId: string | number, data: any) {
  try {
    const key = getVisualizationKey(bagSetId, bagId, pklId);
    await set(key, {
      data,
      timestamp: Date.now(),
    });
    console.log(`💾 Worker: 可视化数据已缓存: ${bagSetId}-${bagId}-${pklId}`);
  } catch (error) {
    console.error("❌ Worker: 设置可视化缓存失败:", error);
  }
}

async function getCachedCenterlineData(bagSetId: string, bagId: string | number, pklId: string | number) {
  try {
    const key = getCenterlineKey(bagSetId, bagId, pklId);
    const cachedItem = await get(key);
    
    if (!cachedItem || Date.now() - cachedItem.timestamp > CACHE_MAX_AGE) {
      return null;
    }
    
    console.log(`✅ Worker: 中心线缓存命中: ${bagSetId}-${bagId}-${pklId}`);
    return {
      centerline_id_list: cachedItem.centerline_id_list,
      selected_centerline_ids: cachedItem.selected_centerline_ids,
    };
  } catch (error) {
    console.error("❌ Worker: 获取中心线缓存失败:", error);
    return null;
  }
}

async function setCachedCenterlineData(bagSetId: string, bagId: string | number, pklId: string | number, centerline_id_list: string[], selected_centerline_ids: string[]) {
  try {
    const key = getCenterlineKey(bagSetId, bagId, pklId);
    await set(key, {
      centerline_id_list,
      selected_centerline_ids,
      timestamp: Date.now(),
    });
    console.log(`💾 Worker: 中心线数据已缓存: ${bagSetId}-${bagId}-${pklId}`);
  } catch (error) {
    console.error("❌ Worker: 设置中心线缓存失败:", error);
  }
}

interface PreloadProgress {
  total: number;
  loaded: number;
  cached: number;
  failed: number;
  errors: Array<{
    pklId: string | number;
    pklName: string;
    error: string;
  }>;
}

// 🎯 批量检查缓存状态（性能优化）
async function batchCheckCache(bagSetId: string, bagId: string | number, pklList: any[]) {
  const cacheStatus = new Map<string | number, {
    hasVisualization: boolean;
    hasCenterline: boolean;
  }>();

  // 并行检查所有PKL的缓存状态
  const checkPromises = pklList.map(async (pkl) => {
    const [cachedVisualization, cachedCenterline] = await Promise.all([
      getCachedVisualizationData(bagSetId, bagId, pkl.id),
      getCachedCenterlineData(bagSetId, bagId, pkl.id)
    ]);

    cacheStatus.set(pkl.id, {
      hasVisualization: !!cachedVisualization,
      hasCenterline: !!cachedCenterline,
    });
  });

  await Promise.all(checkPromises);
  return cacheStatus;
}

// 预加载PKL详情数据
async function preloadPklDetails(
  bagSetId: string,
  bagId: string | number,
  pklList: any[],
  baseUrl: string,
) {
  const results: PreloadProgress = {
    total: pklList.length,
    loaded: 0,
    cached: 0,
    failed: 0,
    errors: [],
  };

  // 🎯 批量检查缓存状态，减少IndexedDB访问次数
  console.log(`🔍 Worker: 开始批量检查 ${pklList.length} 个PKL的缓存状态...`);
  const cacheStatus = await batchCheckCache(bagSetId, bagId, pklList);
  console.log(`✅ Worker: 缓存状态检查完成`);

  // 分批处理
  for (let i = 0; i < pklList.length; i += BATCH_SIZE) {
    const batch = pklList.slice(i, i + BATCH_SIZE);

    // 并行处理当前批次
    const batchPromises = batch.map(async (pkl) => {
      try {
        const status = cacheStatus.get(pkl.id);
        
        // 如果两种数据都已缓存，直接跳过
        if (status?.hasVisualization && status?.hasCenterline) {
          results.cached++;
          self.postMessage({
            type: "progress",
            data: {
              ...results,
              currentPkl: pkl.pkl_name,
              status: "cached",
            },
          });
          return;
        }

        // 🎯 获取中心线数据（如果未缓存）
        let centerlineData;
        if (status?.hasCenterline) {
          centerlineData = await getCachedCenterlineData(bagSetId, bagId, pkl.id);
        } else {
          centerlineData = await fetchPklDetails(baseUrl, bagSetId, bagId, pkl.id);
          await setCachedCenterlineData(
            bagSetId,
            bagId,
            pkl.id,
            centerlineData.centerline_id_list,
            centerlineData.selected_centerline_ids,
          );
        }

        // 🎯 获取可视化数据（如果未缓存）
        let visualizationData = null;
        if (status?.hasVisualization) {
          visualizationData = await getCachedVisualizationData(bagSetId, bagId, pkl.id);
        } else {
          try {
            visualizationData = await fetchVisualizationData(baseUrl, pkl.pkl_dir, pkl.pkl_name);
            console.log("🚀 ~ preloadPklDetails ~ visualizationData:", visualizationData);
            await setCachedVisualizationData(bagSetId, bagId, pkl.id, visualizationData);
          } catch (vizError) {
            console.warn(`⚠️ Worker: 可视化数据获取失败 ${pkl.pkl_name}:`, vizError);
          }
        }

        results.loaded++;

        // 通知主线程缓存更新
        if (centerlineData) {
          self.postMessage({
            type: "centerline_cache_updated",
            data: {
              bagSetId,
              bagId,
              pklId: pkl.id,
              centerlineData,
            },
          });
        }

        if (visualizationData) {
          self.postMessage({
            type: "visualization_cache_updated",
            data: {
              bagSetId,
              bagId,
              pklId: pkl.id,
              visualizationData,
            },
          });
        }

        self.postMessage({
          type: "progress",
          data: {
            ...results,
            currentPkl: pkl.pkl_name,
            status: "loaded",
          },
        });
      } catch (error: any) {
        console.error(`❌ Worker: PKL ${pkl.pkl_name} 预加载失败:`, error);
        results.failed++;
        results.errors.push({
          pklId: pkl.id,
          pklName: pkl.pkl_name,
          error: error.message,
        });

        self.postMessage({
          type: "progress",
          data: {
            ...results,
            currentPkl: pkl.pkl_name,
            status: "failed",
            error: error.message,
          },
        });
      }
    });

    await Promise.all(batchPromises);

    // 批次间延迟
    if (i + BATCH_SIZE < pklList.length) {
      await new Promise((resolve) => setTimeout(resolve, BATCH_DELAY));
    }
  }

  // 发送完成消息
  self.postMessage({
    type: "completed",
    data: results,
  });
}

// 🎯 新增：清理指定bag的缓存
async function clearBagCache(bagSetId: string, bagId: string | number) {
  try {
    // 由于Worker环境无法直接获取所有键，我们使用一个简化的清理策略
    // 在实际应用中，可以维护一个键列表或使用其他方案
    console.log(`🗑️ Worker: 开始清理 bag ${bagSetId}-${bagId} 的缓存...`);
    
    // 这里可以根据需要实现具体的清理逻辑
    // 例如：维护一个已缓存的PKL ID列表，然后逐个删除
    
    self.postMessage({
      type: "cache_cleared",
      data: { bagSetId, bagId }
    });
  } catch (error) {
    console.error("❌ Worker: 清理缓存失败:", error);
    self.postMessage({
      type: "error",
      data: { message: `清理缓存失败` }
    });
  }
}

// 监听消息
self.addEventListener("message", async (event) => {
  const { type, data } = event.data;

  switch (type) {
    case "preload":
      const { bagSetId, bagId, pklList, baseUrl } = data;
      await preloadPklDetails(bagSetId, bagId, pklList, baseUrl);
      break;

    case "clear_bag_cache":
      const { bagSetId: clearBagSetId, bagId: clearBagId } = data;
      await clearBagCache(clearBagSetId, clearBagId);
      break;

    case "stop":
      self.postMessage({ type: "stopped" });
      break;

    default:
      console.warn(`❌ Worker: 未知消息类型: ${type}`);
  }
});
