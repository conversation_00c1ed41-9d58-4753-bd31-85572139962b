import { useRef, useCallback, useEffect, useState } from "react";
import { message } from "antd";
import PklPreloadWorker from "../workers/pkl-preload-worker.ts?worker";
import { pklCache } from "../cache-manager";

interface PreloadProgress {
  total: number;
  loaded: number;
  cached: number;
  failed: number;
  currentPkl?: string;
  status?: "loaded" | "cached" | "failed";
  error?: string;
  errors?: Array<{
    pklId: string | number;
    pklName: string;
    error: string;
  }>;
}

export const usePklPreloader = () => {
  const workerRef = useRef<Worker | null>(null);
  const [isPreloading, setIsPreloading] = useState(false);
  const [progress, setProgress] = useState<PreloadProgress | null>(null);

  // 初始化 Worker
  const initWorker = useCallback(() => {
    if (workerRef.current) return;

    try {
      workerRef.current = new PklPreloadWorker();

      workerRef.current.onmessage = (event) => {
        const { type, data } = event.data;

        switch (type) {
          case "progress":
            setProgress(data);
            break;

          case "completed":
            setIsPreloading(false);
            setProgress(data);
            
            // 显示完成统计信息
            const { total, loaded, cached, failed } = data;
            if (failed > 0) {
              message.warning(
                `预加载完成: 成功 ${loaded + cached}/${total}，失败 ${failed} 个`
              );
            } else {
              message.success(
                `预加载完成: ${loaded + cached}/${total} (新加载 ${loaded}，缓存命中 ${cached})`
              );
            }
            break;

          case "centerline_cache_updated":
            console.log(`🎯 中心线缓存已更新: ${data.bagSetId}-${data.bagId}-${data.pklId}`);
            break;

          case "visualization_cache_updated":
            console.log(`🎯 可视化缓存已更新: ${data.bagSetId}-${data.bagId}-${data.pklId}`);
            break;

          case "stopped":
            setIsPreloading(false);
            message.info("预加载已停止");
            break;

          case "error":
            console.error("Worker错误:", data);
            message.error(`预加载错误: ${data.message}`);
            setIsPreloading(false);
            break;
        }
      };

      workerRef.current.onerror = (error) => {
        console.error("Worker运行错误:", error);
        message.error("预加载Worker出现错误");
        setIsPreloading(false);
      };
    } catch (error) {
      console.error("初始化Worker失败:", error);
      message.error("无法启动后台预加载功能");
    }
  }, []);

  // 开始预加载
  const startPreload = useCallback(
    (bagSetId: string, bagId: string | number, pklList: any[]) => {
      if (!pklList || pklList.length === 0) {
        message.warning("没有PKL数据需要预加载");
        return;
      }

      if (isPreloading) {
        message.warning("预加载正在进行中...");
        return;
      }

      initWorker();

      if (!workerRef.current) {
        message.error("无法启动预加载Worker");
        return;
      }

      setIsPreloading(true);
      setProgress({
        total: pklList.length,
        loaded: 0,
        cached: 0,
        failed: 0,
      });

      workerRef.current.postMessage({
        type: "preload",
        data: {
          bagSetId,
          bagId,
          pklList,
          baseUrl: window.location.origin,
        },
      });

      message.info(`开始预加载 ${pklList.length} 个PKL文件的可视化和中心线数据...`);
    },
    [initWorker, isPreloading],
  );

  // 停止预加载
  const stopPreload = useCallback(() => {
    if (workerRef.current && isPreloading) {
      workerRef.current.postMessage({ type: "stop" });
    }
  }, [isPreloading]);

  // 清理指定 bag 的缓存
  const clearBagCache = useCallback(async (bagSetId: string, bagId: string | number) => {
    try {
      await pklCache.clearBagCache(bagSetId, bagId);
      message.success(`已清理 bag ${bagSetId}-${bagId} 的缓存`);
    } catch (error) {
      console.error("清理缓存失败:", error);
      message.error("清理缓存失败");
    }
  }, []);

  // 清理过期缓存
  const clearExpiredCache = useCallback(async (bagSetId: string, bagId: string | number) => {
    try {
      await pklCache.clearExpiredBagCache(bagSetId, bagId);
      message.success("已清理过期缓存");
    } catch (error) {
      console.error("清理过期缓存失败:", error);
      message.error("清理过期缓存失败");
    }
  }, []);

  // 获取缓存统计
  const getCacheStats = useCallback(async (bagSetId: string, bagId: string | number) => {
    try {
      return await pklCache.getBagCacheStats(bagSetId, bagId);
    } catch (error) {
      console.error("获取缓存统计失败:", error);
      return { visualizationCount: 0, centerlineCount: 0, totalSize: 0 };
    }
  }, []);

  // 清理
  useEffect(() => {
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
        workerRef.current = null;
      }
    };
  }, []);

  return {
    startPreload,
    stopPreload,
    isPreloading,
    progress,
    clearBagCache,
    clearExpiredCache, 
    getCacheStats,
  };
};