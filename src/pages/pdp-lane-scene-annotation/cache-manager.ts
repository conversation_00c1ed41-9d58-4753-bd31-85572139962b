import { VisualizationData } from "./types";
import { get, set, del } from "idb-keyval";

// 🎯 缓存管理器 - 优化版本（独立存储每个PKL）
class PklCacheManager {
  private static CACHE_MAX_AGE = 30 * 60 * 1000; // 30分钟
  
  // 🎯 生成缓存键
  private static getVisualizationKey(bagSetId: string, bagId: string | number, pklId: string | number) {
    return `pkl-viz-${bagSetId}-${bagId}-${pklId}`;
  }
  
  private static getCenterlineKey(bagSetId: string, bagId: string | number, pklId: string | number) {
    return `pkl-centerline-${bagSetId}-${bagId}-${pklId}`;
  }

  private static getBagPrefixPattern(bagSetId: string, bagId: string | number) {
    return `pkl-${bagSetId}-${bagId}-`;
  }

  // ==================== 可视化数据缓存 ====================

  static async getVisualizationData(
    bagSetId: string,
    bagId: string | number,
    pklId: string | number,
  ): Promise<VisualizationData | null> {
    try {
      const key = this.getVisualizationKey(bagSetId, bagId, pklId);
      const cachedItem = await get(key);
      
      if (!cachedItem) return null;
      
      // 检查过期
      if (Date.now() - cachedItem.timestamp > this.CACHE_MAX_AGE) {
        await del(key);
        return null;
      }
      
      console.log(`✅ 可视化缓存命中: ${bagSetId}-${bagId}-${pklId}`);
      return cachedItem.data;
    } catch (error) {
      console.error("获取可视化缓存失败:", error);
      return null;
    }
  }

  static async setVisualizationData(
    bagSetId: string,
    bagId: string | number,
    pklId: string | number,
    data: VisualizationData,
  ): Promise<void> {
    try {
      const key = this.getVisualizationKey(bagSetId, bagId, pklId);
      await set(key, {
        data,
        timestamp: Date.now(),
      });
      console.log(`💾 可视化数据已缓存: ${bagSetId}-${bagId}-${pklId}`);
    } catch (error) {
      console.error("设置可视化缓存失败:", error);
    }
  }

  // ==================== 中心线数据缓存 ====================

  static async getCenterlineData(
    bagSetId: string,
    bagId: string | number,
    pklId: string | number,
  ): Promise<{
    centerline_id_list: string[];
    selected_centerline_ids: string[];
  } | null> {
    try {
      const key = this.getCenterlineKey(bagSetId, bagId, pklId);
      const cachedItem = await get(key);
      
      if (!cachedItem) return null;
      
      // 检查过期
      if (Date.now() - cachedItem.timestamp > this.CACHE_MAX_AGE) {
        await del(key);
        return null;
      }
      
      console.log(`✅ 中心线缓存命中: ${bagSetId}-${bagId}-${pklId}`);
      return {
        centerline_id_list: cachedItem.centerline_id_list,
        selected_centerline_ids: cachedItem.selected_centerline_ids,
      };
    } catch (error) {
      console.error("获取中心线缓存失败:", error);
      return null;
    }
  }

  static async setCenterlineData(
    bagSetId: string,
    bagId: string | number,
    pklId: string | number,
    centerline_id_list: string[],
    selected_centerline_ids: string[],
  ): Promise<void> {
    try {
      const key = this.getCenterlineKey(bagSetId, bagId, pklId);
      await set(key, {
        centerline_id_list,
        selected_centerline_ids,
        timestamp: Date.now(),
      });
      console.log(`💾 中心线数据已缓存: ${bagSetId}-${bagId}-${pklId}`);
    } catch (error) {
      console.error("设置中心线缓存失败:", error);
    }
  }

  // ==================== Bag 维度管理 ====================

  // 🎯 清除指定 bag 的所有缓存
  static async clearBagCache(
    bagSetId: string,
    bagId: string | number,
  ): Promise<void> {
    try {
      // 获取所有键
      const keys = await this.getAllKeys();
      const vizPrefix = `pkl-viz-${bagSetId}-${bagId}-`;
      const centerlinePrefix = `pkl-centerline-${bagSetId}-${bagId}-`;
      
      // 找到匹配的键并删除
      const keysToDelete = keys.filter(key => 
        key.startsWith(vizPrefix) || key.startsWith(centerlinePrefix)
      );
      
      await Promise.all(keysToDelete.map(key => del(key)));
      
      console.log(`🗑️ 已清除 bag ${bagSetId}-${bagId} 的所有缓存 (${keysToDelete.length} 项)`);
    } catch (error) {
      console.error("清除 bag 缓存失败:", error);
    }
  }

  // 🎯 检查并清除指定 bag 的过期缓存
  static async clearExpiredBagCache(
    bagSetId: string,
    bagId: string | number,
  ): Promise<void> {
    try {
      const now = Date.now();
      const keys = await this.getAllKeys();
      const vizPrefix = `pkl-viz-${bagSetId}-${bagId}-`;
      const centerlinePrefix = `pkl-centerline-${bagSetId}-${bagId}-`;
      
      const bagKeys = keys.filter(key => 
        key.startsWith(vizPrefix) || key.startsWith(centerlinePrefix)
      );
      
      let expiredCount = 0;
      for (const key of bagKeys) {
        try {
          const item = await get(key);
          if (item && now - item.timestamp > this.CACHE_MAX_AGE) {
            await del(key);
            expiredCount++;
          }
        } catch (error) {
          console.warn(`清理键 ${key} 时出错:`, error);
        }
      }

      console.log(`🧹 已清理 bag ${bagSetId}-${bagId} 的过期缓存 (${expiredCount} 项)`);
    } catch (error) {
      console.error("清理过期缓存失败:", error);
    }
  }

  // 🎯 获取指定 bag 的缓存统计信息
  static async getBagCacheStats(
    bagSetId: string,
    bagId: string | number,
  ): Promise<{
    visualizationCount: number;
    centerlineCount: number;
    totalSize: number;
  }> {
    try {
      const keys = await this.getAllKeys();
      const vizPrefix = `pkl-viz-${bagSetId}-${bagId}-`;
      const centerlinePrefix = `pkl-centerline-${bagSetId}-${bagId}-`;
      
      const vizCount = keys.filter(key => key.startsWith(vizPrefix)).length;
      const centerlineCount = keys.filter(key => key.startsWith(centerlinePrefix)).length;

      return {
        visualizationCount: vizCount,
        centerlineCount: centerlineCount,
        totalSize: vizCount + centerlineCount,
      };
    } catch (error) {
      console.error("获取缓存统计失败:", error);
      return { visualizationCount: 0, centerlineCount: 0, totalSize: 0 };
    }
  }

  // ==================== 单个 PKL 操作 ====================

  private static async removeVisualizationData(
    bagSetId: string,
    bagId: string | number,
    pklId: string | number,
  ): Promise<void> {
    const key = this.getVisualizationKey(bagSetId, bagId, pklId);
    await del(key);
  }

  private static async removeCenterlineData(
    bagSetId: string,
    bagId: string | number,
    pklId: string | number,
  ): Promise<void> {
    const key = this.getCenterlineKey(bagSetId, bagId, pklId);
    await del(key);
  }

  // ==================== 全局操作 ====================

  static async clearAllCache(): Promise<void> {
    try {
      const keys = await this.getAllKeys();
      const pklKeys = keys.filter(key => 
        key.startsWith('pkl-viz-') || key.startsWith('pkl-centerline-')
      );
      
      await Promise.all(pklKeys.map(key => del(key)));
      console.log(`🗑️ 已清除所有PKL缓存 (${pklKeys.length} 项)`);
    } catch (error) {
      console.error("清除所有缓存失败:", error);
    }
  }

  // ==================== 辅助方法 ====================

  // 🎯 获取所有键（用于管理操作）
  private static async getAllKeys(): Promise<string[]> {
    try {
      // idb-keyval 没有直接获取所有键的方法，我们需要用一个变通方案
      // 这里我们维护一个键列表，或者使用 IndexedDB 原生 API
      
      // 方案1：使用 IndexedDB 原生 API
      return new Promise((resolve, reject) => {
        const request = indexedDB.open('keyval-store');
        request.onsuccess = () => {
          const db = request.result;
          const transaction = db.transaction(['keyval'], 'readonly');
          const store = transaction.objectStore('keyval');
          const getAllKeysRequest = store.getAllKeys();
          
          getAllKeysRequest.onsuccess = () => {
            const keys = getAllKeysRequest.result as string[];
            resolve(keys);
          };
          
          getAllKeysRequest.onerror = () => {
            reject(getAllKeysRequest.error);
          };
        };
        
        request.onerror = () => {
          reject(request.error);
        };
      });
    } catch (error) {
      console.error("获取所有键失败:", error);
      return [];
    }
  }

  // 🎯 批量操作：预加载多个PKL
  static async preloadPklData(
    bagSetId: string,
    bagId: string | number,
    pklDataList: Array<{
      pklId: string | number;
      visualizationData?: VisualizationData;
      centerlineData?: {
        centerline_id_list: string[];
        selected_centerline_ids: string[];
      };
    }>
  ): Promise<void> {
    try {
      const operations: Promise<void>[] = [];
      
      pklDataList.forEach(({ pklId, visualizationData, centerlineData }) => {
        if (visualizationData) {
          operations.push(
            this.setVisualizationData(bagSetId, bagId, pklId, visualizationData)
          );
        }
        
        if (centerlineData) {
          operations.push(
            this.setCenterlineData(
              bagSetId, 
              bagId, 
              pklId, 
              centerlineData.centerline_id_list,
              centerlineData.selected_centerline_ids
            )
          );
        }
      });
      
      await Promise.all(operations);
      console.log(`🚀 批量预加载完成: ${pklDataList.length} 个PKL`);
    } catch (error) {
      console.error("批量预加载失败:", error);
    }
  }
}

// 🎯 导出便捷的访问函数
export const pklCache = {
  // 可视化数据
  getVisualizationData:
    PklCacheManager.getVisualizationData.bind(PklCacheManager),
  setVisualizationData:
    PklCacheManager.setVisualizationData.bind(PklCacheManager),

  // 中心线数据
  getCenterlineData: PklCacheManager.getCenterlineData.bind(PklCacheManager),
  setCenterlineData: PklCacheManager.setCenterlineData.bind(PklCacheManager),

  // Bag 维度管理
  clearBagCache: PklCacheManager.clearBagCache.bind(PklCacheManager),
  clearExpiredBagCache:
    PklCacheManager.clearExpiredBagCache.bind(PklCacheManager),
  getBagCacheStats: PklCacheManager.getBagCacheStats.bind(PklCacheManager),

  // 全局操作
  clearAllCache: PklCacheManager.clearAllCache.bind(PklCacheManager),

  // 🎯 新增：批量操作
  preloadPklData: PklCacheManager.preloadPklData.bind(PklCacheManager),
};
