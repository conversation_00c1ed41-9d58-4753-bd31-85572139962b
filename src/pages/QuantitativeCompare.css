.qualitative-compare {
    height: 100vh;
    width: 100%;
}

.compare-header {
    display: flex;
    align-items: center;
    padding: 0 24px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1;
}

.compare-header h3 {
    margin: 0;
    margin-left: 16px;
}

.compare-content {
    padding: 24px;
    background: #fff;
    margin: 24px;
    border-radius: 4px;
}

.description {
    margin-bottom: 24px;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    flex-direction: column;
}