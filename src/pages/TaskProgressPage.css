.task-progress-container {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.task-progress-content {
  max-width: 900px;
  margin: 0 auto;
}

.progress-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.status-title {
  margin-left: 16px;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.status-badge {
  font-size: 14px;
  padding: 3px 10px;
  border-radius: 12px;
  margin-left: 12px;
}

.status-badge.running {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-badge.completed {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-badge.failed {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.status-badge.pending {
  background-color: #fffbe6;
  color: #faad14;
}

.progress-message {
  margin-bottom: 24px;
  color: #666;
  font-size: 16px;
}

.progress-section {
  margin-bottom: 20px;
}

.progress-details {
  margin: 20px 0;
}

.stat-card {
  text-align: center;
  background-color: #fafafa;
}

.error-stat {
  color: #ff4d4f;
  margin-top: 8px;
  font-size: 14px;
}

.error-alert {
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
}