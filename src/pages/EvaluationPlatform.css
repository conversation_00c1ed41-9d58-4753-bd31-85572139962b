.evaluation-platform-container {
    display: flex;
    height: 100%;
    position: relative;
}

.manager-toggle {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
}

.toggle-button {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.evaluation-platform-sider {
    overflow-y: auto;
    transition: all 0.3s ease;
}

.platform-divider {
    height: 100%;
    margin: 0;
}

.right-panel {
    background: #f5f5f5;
}