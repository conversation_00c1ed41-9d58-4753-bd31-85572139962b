import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Layout, Button, Card, Typography, Tag, message, Spin, Space, Select } from 'antd';
import { ArrowLeftOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import PickleVisualizer from '../../components/PickleVisualizer';
import { EvaluationCase, PdpPathInfo } from '../../types';
import '../PdpPathAnnotation.css'; // 导入CSS文件

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

interface AnnotatorProgress {
    annotator_id: string;
    annotated_pairs: number[];
    annotations: Record<string, string>;
}

interface PathInfo {
    index: number;
    type: 'ground_truth' | 'predicted';
    probability: number;
    points: number[][];
}

interface PathPair {
    pair_id: number;
    path_a: PathInfo;
    path_b: PathInfo;
}

const PathPairComparison: React.FC = () => {
    const { evaluationSetId, pklId } = useParams<{ evaluationSetId: string; pklId: string }>();
    const navigate = useNavigate();
    const location = useLocation();
    const { user } = useAuth();

    const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
    const [pathPairs, setPathPairs] = useState<PathPair[]>([]);
    const [currentPairIndex, setCurrentPairIndex] = useState(0);
    const [annotatorProgress, setAnnotatorProgress] = useState<AnnotatorProgress[]>([]);
    const [loading, setLoading] = useState(false);
    const [imageData, setImageData] = useState<string | null>(null); // 添加图像状态

    // 从location.state获取标注员进度数据
    useEffect(() => {
        if (location.state?.annotatorProgress) {
            setAnnotatorProgress(location.state.annotatorProgress);
        }
    }, [location.state]);

    // 加载PKL数据和路径pairs
    const loadData = async () => {
        if (!pklId || !evaluationSetId) return;

        setLoading(true);
        try {
            // 修复：使用正确的API端点获取PKL信息
            const pklResponse = await axios.get(`/api/evaluation_sets/${evaluationSetId}`, {
                params: {
                    case_id: pklId
                }
            });

            if (pklResponse.data.success) {
                const cases = pklResponse.data.cases || [];
                const targetCase = cases.find((c: any) => c.id.toString() === pklId);
                if (targetCase) {
                    setSelectedPkl(targetCase);
                }
            }

            // 加载路径pairs和图像数据
            const pairsResponse = await axios.get(`/api/annotation/path-pairs/${pklId}`, {
                params: { evaluation_set_id: evaluationSetId }
            });

            if (pairsResponse.data.success) {
                setPathPairs(pairsResponse.data.path_pairs);

                if (pairsResponse.data.image_data) {
                    setImageData(pairsResponse.data.image_data);
                } else {
                    setImageData(null);
                }
            }
        } catch (error) {
            message.error('加载数据失败');
            console.error('Error loading data:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadData();
    }, [pklId, evaluationSetId]);

    // 转换pathPair为PdpPathInfo格式
    const convertPathPairToPdpPaths = (pathPair: PathPair): Record<number, PdpPathInfo> => {
        const pdpPaths: Record<number, PdpPathInfo> = {};

        pdpPaths[pathPair.path_a.index] = {
            index: pathPair.path_a.index,
            probability: pathPair.path_a.probability,
            points_count: pathPair.path_a.points.length,
            visualization_points: pathPair.path_a.points,
            is_ground_truth: pathPair.path_a.type === 'ground_truth',
            middle_point: pathPair.path_a.points[Math.floor(pathPair.path_a.points.length / 2)] || [0, 0, 0],
            color: "#ffff00",
            lineWidth: 4
        };

        pdpPaths[pathPair.path_b.index] = {
            index: pathPair.path_b.index,
            probability: pathPair.path_b.probability,
            points_count: pathPair.path_b.points.length,
            visualization_points: pathPair.path_b.points,
            is_ground_truth: pathPair.path_b.type === 'ground_truth',
            middle_point: pathPair.path_b.points[Math.floor(pathPair.path_b.points.length / 2)] || [0, 0, 0],
            color: "#0000ff",
            lineWidth: 4
        };

        return pdpPaths;
    };

    // 获取当前pair的所有标注结果
    const getCurrentAnnotations = () => {
        const currentPair = pathPairs[currentPairIndex];
        if (!currentPair) return [];

        return annotatorProgress.map(ap => ({
            annotator: ap.annotator_id,
            result: ap.annotations[currentPairIndex.toString()] || '未标注'
        }));
    };

    // 获取结果颜色
    const getResultColor = (result: string) => {
        switch (result) {
            case 'A_better': return 'gold';
            case 'B_better': return 'blue';
            case 'indistinguishable': return 'orange';
            default: return 'default';
        }
    };

    // 检查当前pair是否有冲突
    const hasConflict = () => {
        const annotations = getCurrentAnnotations();
        const validResults = annotations.filter(ann => ann.result !== '未标注').map(ann => ann.result);
        const uniqueResults = new Set(validResults);
        return validResults.length > 1 && uniqueResults.size > 1;
    };

    const currentPair = pathPairs[currentPairIndex];
    const currentAnnotations = getCurrentAnnotations();

    if (loading) {
        return (
            <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Spin size="large" tip="加载中..." />
            </div>
        );
    }

    return (
        <Layout style={{ height: 'calc(100vh - 120px)' }}>
            {/* 左侧导航 */}
            <Sider width={300} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
                <div style={{ padding: '16px' }}>
                    <div style={{ marginBottom: '16px' }}>
                        <Button
                            icon={<ArrowLeftOutlined />}
                            onClick={() => navigate(`/path-pair-statistics/${evaluationSetId}`)}
                        >
                            返回统计页面
                        </Button>
                    </div>

                    <Title level={4}>标注结果对比</Title>
                    <Text strong>PKL文件：</Text>
                    <br />
                    <Text style={{ fontSize: '12px' }}>{selectedPkl?.pkl_name}</Text>

                    <div style={{ marginTop: '16px' }}>
                        <Text strong>Pair 导航</Text>
                        <div style={{ marginTop: '8px' }}>
                            <Select
                                style={{ width: '100%' }}
                                value={currentPairIndex}
                                onChange={setCurrentPairIndex}
                                placeholder="选择Pair"
                            >
                                {pathPairs.map((_, index) => {
                                    const pairAnnotations = annotatorProgress.map(ap => ({
                                        annotator: ap.annotator_id,
                                        result: ap.annotations[index.toString()] || '未标注'
                                    }));
                                    const validResults = pairAnnotations.filter(ann => ann.result !== '未标注').map(ann => ann.result);
                                    const uniqueResults = new Set(validResults);
                                    const hasConflict = validResults.length > 1 && uniqueResults.size > 1;

                                    return (
                                        <Select.Option key={index} value={index}>
                                            <span>Pair {index + 1}</span>
                                            {hasConflict && <Tag color="red" style={{ marginLeft: 8 }}>冲突</Tag>}
                                        </Select.Option>
                                    );
                                })}
                            </Select>
                        </div>
                    </div>

                    <div style={{ marginTop: '16px' }}>
                        <Space direction="vertical" style={{ width: '100%' }}>
                            <Button
                                icon={<LeftOutlined />}
                                onClick={() => setCurrentPairIndex(prev => Math.max(0, prev - 1))}
                                disabled={currentPairIndex === 0}
                                size="small"
                                block
                            >
                                上一个Pair
                            </Button>
                            <Button
                                icon={<RightOutlined />}
                                onClick={() => setCurrentPairIndex(prev => Math.min(pathPairs.length - 1, prev + 1))}
                                disabled={currentPairIndex === pathPairs.length - 1}
                                size="small"
                                block
                            >
                                下一个Pair
                            </Button>
                        </Space>
                    </div>
                </div>
            </Sider>

            {/* 中间可视化区域 */}
            <Content style={{ background: '#fff', position: 'relative' }}>
                {selectedPkl && currentPair ? (
                    <div className="visualization-area">
                        {/* E2E图像显示区域 */}
                        {imageData && (
                            <div className="e2e-image-container">
                                <img
                                    src={`data:image/jpeg;base64,${imageData}`}
                                    alt="E2E图像"
                                    className="e2e-image"
                                />
                            </div>
                        )}

                        {/* 3D可视化区域 */}
                        <div style={{ flex: 1 }}>
                            <PickleVisualizer
                                evaluationCase={{
                                    ...selectedPkl,
                                    pkl_dir: selectedPkl.pkl_dir || '/default/path'
                                }}
                                pdpPaths={convertPathPairToPdpPaths(currentPair)}
                                highlightPathIndex={null}
                                showGroundTruth={false}
                            />
                        </div>
                    </div>
                ) : (
                    <div style={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#999'
                    }}>
                            {loading ? (
                                <Spin size="large" tip="加载数据中..." />
                            ) : (
                                    <Text>加载中...</Text>
                            )}
                    </div>
                )}
            </Content>

            {/* 右侧标注对比面板 */}
            <Sider width={320} style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}>
                <div style={{ padding: '16px', height: '100%' }}>
                    <Title level={4}>
                        Pair {currentPairIndex + 1}/{pathPairs.length}
                        {hasConflict() && <Tag color="red" style={{ marginLeft: 8 }}>存在冲突</Tag>}
                    </Title>

                    <div style={{ marginBottom: '20px' }}>
                        <Text strong>路径信息：</Text>
                        {currentPair && (
                            <div style={{ marginTop: '8px' }}>
                                <div style={{ marginBottom: '8px' }}>
                                    <Tag color="gold">Path A</Tag>
                                    <Text>
                                        {currentPair.path_a.type === 'ground_truth' ? 'GT路径' : `预测路径 ${currentPair.path_a.index}`}
                                        {currentPair.path_a.type !== 'ground_truth' && (
                                            <span> (概率: {(currentPair.path_a.probability * 100).toFixed(1)}%)</span>
                                        )}
                                    </Text>
                                </div>
                                <div>
                                    <Tag color="blue">Path B</Tag>
                                    <Text>
                                        {currentPair.path_b.type === 'ground_truth' ? 'GT路径' : `预测路径 ${currentPair.path_b.index}`}
                                        {currentPair.path_b.type !== 'ground_truth' && (
                                            <span> (概率: {(currentPair.path_b.probability * 100).toFixed(1)}%)</span>
                                        )}
                                    </Text>
                                </div>
                            </div>
                        )}
                    </div>

                    <div style={{ marginBottom: '20px' }}>
                        <Text strong>标注结果对比：</Text>
                        <div style={{ marginTop: '12px' }}>
                            {currentAnnotations.length === 0 ? (
                                <Text type="secondary">暂无标注数据</Text>
                            ) : (
                                <Space direction="vertical" style={{ width: '100%' }}>
                                    {currentAnnotations.map(annotation => (
                                        <Card key={annotation.annotator} size="small">
                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <Text strong>{annotation.annotator}</Text>
                                                <Tag color={getResultColor(annotation.result)}>
                                                    {annotation.result === 'A_better' ? 'A > B' :
                                                        annotation.result === 'B_better' ? 'A < B' :
                                                            annotation.result === 'indistinguishable' ? '不能区分' :
                                                                '未标注'}
                                                </Tag>
                                            </div>
                                        </Card>
                                    ))}
                                </Space>
                            )}
                        </div>
                    </div>

                    {hasConflict() && (
                        <Card title="冲突分析" size="small" style={{ backgroundColor: '#fff7e6', borderColor: '#ffa940' }}>
                            <Text type="warning">
                                此pair存在标注冲突，需要进一步讨论确认最终结果。
                            </Text>
                        </Card>
                    )}
                </div>
            </Sider>
        </Layout>
    );
};

export default PathPairComparison;