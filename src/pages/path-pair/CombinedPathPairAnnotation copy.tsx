import React, { useState, useEffect, use<PERSON>allback, useMemo } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { Layout, List, Button, Card, Typography, Progress, Tag, message, Spin, Space, Alert, Modal, Checkbox, Select, Input, Table, Divider } from 'antd';
import { LeftOutlined, RightOutlined, CheckOutlined, DownloadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import PickleVisualizer from '../../components/PickleVisualizer';
import '../PdpPathAnnotation.css';
import { EvaluationCase, EvaluationSet, PdpPathInfo } from '../../types';

const { Sider, Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

interface PathInfo {
    index: number;
    type: 'ground_truth' | 'predicted';
    probability: number;
    points: number[][];
}

interface PathPair {
    pair_id: number;
    path_a: PathInfo;
    path_b: PathInfo;
}

interface PairAnnotation {
    pkl_id: number;
    path_a_index: number;
    path_b_index: number;
    comparison_result: 'A_better' | 'B_better' | 'indistinguishable';
    annotator_id: string;
    created_at: string;
    evaluation_set_id: number;
}

interface CombinedEvaluationCase extends EvaluationCase {
    evaluation_set_id: number;
    evaluation_set_name: string;
}

const CombinedPathPairAnnotation: React.FC = () => {
    const navigate = useNavigate();
    const { user, isAuthenticated } = useAuth();
    const [searchParams, setSearchParams] = useSearchParams();

    // 状态管理
    const [leftSiderWidth, setLeftSiderWidth] = useState(350);
    const [rightSiderWidth, setRightSiderWidth] = useState(280);

    const [evaluationSets, setEvaluationSets] = useState<EvaluationSet[]>([]);
    const [selectedEvaluationSets, setSelectedEvaluationSets] = useState<number[]>([]);
    const [combinedPklList, setCombinedPklList] = useState<CombinedEvaluationCase[]>([]);
    const [selectedPkl, setSelectedPkl] = useState<CombinedEvaluationCase | null>(null);

    const [pathPairs, setPathPairs] = useState<PathPair[]>([]);
    const [currentPairIndex, setCurrentPairIndex] = useState(0);
    const [annotations, setAnnotations] = useState<PairAnnotation[]>([]);

    const [loading, setLoading] = useState({
        evaluationSets: false,
        pklList: false,
        pathPairs: false,
        annotation: false,
    });

    const [pklPagination, setPklPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });

    const [setupModalVisible, setSetupModalVisible] = useState(true);
    const [imageData, setImageData] = useState<string | null>(null);

    // 从URL参数初始化选中的评测集
    useEffect(() => {
        const setsParam = searchParams.get('sets');
        if (setsParam) {
            try {
                const setIds = setsParam.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
                if (setIds.length > 0) {
                    setSelectedEvaluationSets(setIds);
                    setSetupModalVisible(false); // 如果URL中有参数，跳过设置模态框
                }
            } catch (error) {
                console.error('解析URL参数失败:', error);
            }
        }
    }, [searchParams]);

    // 更新URL参数
    const updateUrlParams = (setIds: number[]) => {
        if (setIds.length > 0) {
            setSearchParams({ sets: setIds.join(',') });
        } else {
            setSearchParams({});
        }
    };

    // 验证用户身份
    useEffect(() => {
        if (!isAuthenticated) {
            navigate('/login');
        }
    }, [isAuthenticated, navigate]);

    // 加载评测集列表
    const loadEvaluationSets = async () => {
        setLoading(prev => ({ ...prev, evaluationSets: true }));
        try {
            const response = await axios.get('/api/evaluation_sets', {
                params: {
                    page: 1,
                    per_page: 100
                }
            });
            // console.log('Evaluation Sets Response:', response.data);
            if (response.data.success) {
                setEvaluationSets(response.data.data);
            }
            console.log(evaluationSets);
        } catch (error) {
            message.error('加载评测集列表失败');
        } finally {
            setLoading(prev => ({ ...prev, evaluationSets: false }));
        }
    };

    // 加载组合后的PKL列表
    const loadCombinedPklList = async (page = 1, pageSize = 20) => {
        if (selectedEvaluationSets.length === 0) return;

        setLoading(prev => ({ ...prev, pklList: true }));
        try {
            const response = await axios.post('/api/annotation/combined-pkl-list', {
                evaluation_set_ids: selectedEvaluationSets,
                page,
                per_page: pageSize,
                dirty_filter: true
            });

            if (response.data.success) {
                setCombinedPklList(response.data.combined_cases);
                setPklPagination({
                    current: page,
                    pageSize,
                    total: response.data.total_count || 0
                });
            }
        } catch (error) {
            message.error('加载PKL列表失败');
        } finally {
            setLoading(prev => ({ ...prev, pklList: false }));
        }
    };

    // 加载路径pairs
    const loadPathPairs = async (pkl: CombinedEvaluationCase) => {
        setLoading(prev => ({ ...prev, pathPairs: true }));
        try {
            const response = await axios.get(`/api/annotation/path-pairs/${pkl.id}`, {
                params: {
                    evaluation_set_id: pkl.evaluation_set_id
                }
            });

            if (response.data.success) {
                setPathPairs(response.data.path_pairs);
                setAnnotations(response.data.annotations);
                setCurrentPairIndex(0);

                if (response.data.image_data) {
                    setImageData(response.data.image_data);
                } else {
                    setImageData(null);
                }
            }
        } catch (error) {
            message.error('加载路径pairs失败');
            console.error('Error loading path pairs:', error);
        } finally {
            setLoading(prev => ({ ...prev, pathPairs: false }));
        }
    };

    // 处理标注
    const handleAnnotate = async (result: 'A_better' | 'B_better' | 'indistinguishable') => {
        if (!selectedPkl || pathPairs.length === 0) return;

        const currentPair = pathPairs[currentPairIndex];
        setLoading(prev => ({ ...prev, annotation: true }));

        try {
            const response = await axios.post('/api/annotation/path-pair-annotation', {
                pkl_id: selectedPkl.id,
                path_a_index: currentPair.path_a.index,
                path_b_index: currentPair.path_b.index,
                comparison_result: result,
                evaluation_set_id: selectedPkl.evaluation_set_id,
                annotator_id: user?.username
            });

            if (response.data.success) {
                const newAnnotation: PairAnnotation = {
                    pkl_id: selectedPkl.id,
                    path_a_index: currentPair.path_a.index,
                    path_b_index: currentPair.path_b.index,
                    comparison_result: result,
                    annotator_id: user?.username || '',
                    created_at: new Date().toISOString(),
                    evaluation_set_id: selectedPkl.evaluation_set_id
                };

                setAnnotations(prev => {
                    const filtered = prev.filter(ann =>
                        !(ann.path_a_index === currentPair.path_a.index &&
                            ann.path_b_index === currentPair.path_b.index &&
                            ann.annotator_id === user?.username)
                    );
                    return [...filtered, newAnnotation];
                });

                message.success('标注成功');

                if (currentPairIndex < pathPairs.length - 1) {
                    setCurrentPairIndex(prev => prev + 1);
                }
            }
        } catch (error) {
            message.error('标注失败');
            console.error('Error annotating:', error);
        } finally {
            setLoading(prev => ({ ...prev, annotation: false }));
        }
    };

    // 键盘事件处理
    const handleKeyDown = useCallback((event: KeyboardEvent) => {
        if (loading.annotation) return;

        switch (event.key) {
            case 'ArrowLeft':
            case 'a':
            case 'A':
                event.preventDefault();
                setCurrentPairIndex(prev => Math.max(0, prev - 1));
                break;
            case 'ArrowRight':
            case 'd':
            case 'D':
                event.preventDefault();
                setCurrentPairIndex(prev => Math.min(pathPairs.length - 1, prev + 1));
                break;
            case '1':
                event.preventDefault();
                handleAnnotate('A_better');
                break;
            case '2':
                event.preventDefault();
                handleAnnotate('B_better');
                break;
            case '3':
                event.preventDefault();
                handleAnnotate('indistinguishable');
                break;
        }
    }, [pathPairs.length, loading.annotation, handleAnnotate]);

    useEffect(() => {
        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [handleKeyDown]);

    // 处理PKL选择
    const handlePklSelect = async (pkl: CombinedEvaluationCase) => {
        setSelectedPkl(pkl);
        setImageData(null);
        await loadPathPairs(pkl);
    };

    // 获取当前pair的标注状态
    const getCurrentAnnotation = () => {
        if (!selectedPkl || pathPairs.length === 0) return null;

        const currentPair = pathPairs[currentPairIndex];
        return annotations.find(ann =>
            ann.path_a_index === currentPair.path_a.index &&
            ann.path_b_index === currentPair.path_b.index &&
            ann.annotator_id === user?.username
        );
    };

    // 计算当前PKL的标注进度
    const getAnnotationProgress = () => {
        if (!selectedPkl || pathPairs.length === 0) return { completed: 0, total: 0 };

        const userAnnotations = annotations.filter(ann => ann.annotator_id === user?.username);
        return {
            completed: userAnnotations.length,
            total: pathPairs.length
        };
    };

    // 确认设置并开始标注
    const handleSetupConfirm = () => {
        if (selectedEvaluationSets.length === 0) {
            message.error('请至少选择一个评测集');
            return;
        }
        updateUrlParams(selectedEvaluationSets); // 更新URL参数
        setSetupModalVisible(false);
        loadCombinedPklList();
    };

    // 处理评测集选择变化
    const handleEvaluationSetChange = (setId: number, selected: boolean) => {
        let newSelectedSets: number[];
        if (selected) {
            newSelectedSets = [...selectedEvaluationSets, setId];
        } else {
            newSelectedSets = selectedEvaluationSets.filter(id => id !== setId);
        }
        setSelectedEvaluationSets(newSelectedSets);
        // updateUrlParams(newSelectedSets); // 实时更新URL参数
    };

    // 重新选择评测集
    const handleReopenSetup = () => {
        setSetupModalVisible(true);
    };

    // 初始化加载
    useEffect(() => {
        loadEvaluationSets();
    }, []);

    // 当selectedEvaluationSets变化且不是通过模态框设置时，自动加载PKL列表
    useEffect(() => {
        if (selectedEvaluationSets.length > 0 && !setupModalVisible) {
            loadCombinedPklList();
        }
    }, [selectedEvaluationSets, setupModalVisible]);

    const currentPair = pathPairs[currentPairIndex];
    const currentAnnotation = getCurrentAnnotation();
    const progress = getAnnotationProgress();

    const memoizedEvaluationCase = useMemo(() => {
        if (!selectedPkl) return null;
        return {
            ...selectedPkl,
            pkl_dir: selectedPkl.pkl_dir || '/default/path'
        };
    }, [selectedPkl]);

    const convertPathPairToPdpPaths = (pathPair: PathPair): Record<number, PdpPathInfo> => {
        const pdpPaths: Record<number, PdpPathInfo> = {};

        pdpPaths[pathPair.path_a.index] = {
            index: pathPair.path_a.index,
            probability: pathPair.path_a.probability,
            points_count: pathPair.path_a.points.length,
            visualization_points: pathPair.path_a.points,
            is_ground_truth: pathPair.path_a.type === 'ground_truth',
            middle_point: pathPair.path_a.points[Math.floor(pathPair.path_a.points.length / 2)] || [0, 0, 0],
            color: "#ffff00",
            lineWidth: 4
        };

        pdpPaths[pathPair.path_b.index] = {
            index: pathPair.path_b.index,
            probability: pathPair.path_b.probability,
            points_count: pathPair.path_b.points.length,
            visualization_points: pathPair.path_b.points,
            is_ground_truth: pathPair.path_b.type === 'ground_truth',
            middle_point: pathPair.path_b.points[Math.floor(pathPair.path_b.points.length / 2)] || [0, 0, 0],
            color: "#0000ff",
            lineWidth: 4
        };

        return pdpPaths;
    };

    // 修改评测集选择的表格列
    const evaluationSetColumns = [
        {
            title: '评测集名称',
            dataIndex: 'set_name',
            key: 'set_name',
        },
        {
            title: '创建者',
            dataIndex: 'creator_name',
            key: 'creator_name',
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record: EvaluationSet) => (
                <Button
                    type={selectedEvaluationSets.includes(record.id) ? 'primary' : 'default'}
                    size="small"
                    onClick={() => handleEvaluationSetChange(record.id, !selectedEvaluationSets.includes(record.id))}
                >
                    {selectedEvaluationSets.includes(record.id) ? '取消选择' : '选择'}
                </Button>
            ),
        },
    ];

    return (
        <>
            {/* 设置模态框 */}
            <Modal
                title="组合评测集设置"
                open={setupModalVisible}
                onOk={handleSetupConfirm}
                onCancel={() => navigate('/dashboard')}
                width={800}
                okText="开始标注"
                cancelText="取消"
            >
                <div>
                    <Text strong>选择要组合的评测集：</Text>
                    <div style={{ marginTop: '16px' }}>
                        <Table
                            dataSource={evaluationSets}
                            columns={evaluationSetColumns}
                            rowKey="id"
                            loading={loading.evaluationSets}
                            pagination={{ pageSize: 10 }}
                            size="small"
                        />
                    </div>
                    {selectedEvaluationSets.length > 0 && (
                        <Alert
                            style={{ marginTop: '16px' }}
                            message={`已选择 ${selectedEvaluationSets.length} 个评测集`}
                            description={`选中的评测集ID: ${selectedEvaluationSets.join(', ')}`}
                            type="info"
                            showIcon
                        />
                    )}
                </div>
            </Modal>

            <Layout style={{ height: 'calc(100vh - 120px)' }}>
                {/* 左侧PKL列表 */}
                <Sider
                    width={leftSiderWidth}
                    style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}
                >
                    <div style={{ padding: '16px' }}>
                        <Title level={4}>组合PKL列表</Title>
                        <div style={{ marginBottom: '16px' }}>
                            <Text strong>包含评测集：</Text>
                            <br />
                            <div style={{ marginTop: '8px' }}>
                                {selectedEvaluationSets.map(setId => {
                                    const evalSet = evaluationSets.find(s => s.id === setId);
                                    return evalSet ? (
                                        <Tag key={setId} color="blue" style={{ marginBottom: '4px' }}>
                                            {evalSet.set_name}
                                        </Tag>
                                    ) : null;
                                })}
                            </div>
                        </div>

                        <Button
                            type="default"
                            onClick={handleReopenSetup}
                            style={{ marginBottom: '16px', width: '100%' }}
                            size="small"
                        >
                            重新选择评测集
                        </Button>

                        {/* 添加分享链接按钮 */}
                        {selectedEvaluationSets.length > 0 && (
                            <Button
                                type="default"
                                onClick={() => {
                                    const currentUrl = window.location.href;
                                    navigator.clipboard.writeText(currentUrl).then(() => {
                                        message.success('链接已复制到剪贴板');
                                    }).catch(() => {
                                        message.error('复制链接失败');
                                    });
                                }}
                                style={{ marginBottom: '16px', width: '100%' }}
                                size="small"
                            >
                                复制分享链接
                            </Button>
                        )}

                        {selectedPkl && (
                            <Card size="small" style={{ marginBottom: '16px' }}>
                                <Text strong>当前标注进度</Text>
                                <Progress
                                    percent={progress.total > 0 ? Math.round((progress.completed / progress.total) * 100) : 0}
                                    format={() => `${progress.completed}/${progress.total}`}
                                />

                            </Card>
                        )}
                    </div>

                    <div style={{ height: 'calc(100% - 320px)', overflow: 'auto' }}>
                        <List
                            loading={loading.pklList}
                            dataSource={combinedPklList}
                            pagination={{
                                onChange: (page, pageSize) => {
                                    loadCombinedPklList(page, pageSize);
                                },
                                current: pklPagination.current,
                                pageSize: pklPagination.pageSize,
                                total: pklPagination.total,
                                size: 'small',
                            }}
                            renderItem={(pkl) => (
                                <List.Item
                                    onClick={() => handlePklSelect(pkl)}
                                    style={{
                                        cursor: 'pointer',
                                        backgroundColor: selectedPkl?.id === pkl.id && selectedPkl?.evaluation_set_id === pkl.evaluation_set_id ? '#e6f7ff' : 'transparent',
                                        padding: '12px 16px'
                                    }}
                                >
                                    <div style={{ width: '100%' }}>
                                        <div style={{
                                            fontWeight: selectedPkl?.id === pkl.id && selectedPkl?.evaluation_set_id === pkl.evaluation_set_id ? 'bold' : 'normal',
                                            marginBottom: '4px',
                                            fontSize: '14px'
                                        }}>
                                            {pkl.pkl_name}
                                        </div>
                                    </div>
                                </List.Item>
                            )}
                        />
                    </div>
                </Sider>

                {/* 中间可视化区域 */}
                <Content style={{ background: '#fff', position: 'relative' }}>
                    {selectedPkl && pathPairs.length > 0 && memoizedEvaluationCase ? (
                        <div className="visualization-area">
                            {imageData && (
                                <div className="e2e-image-container">
                                    <img
                                        src={`data:image/jpeg;base64,${imageData}`}
                                        alt="E2E图像"
                                        className="e2e-image"
                                    />
                                </div>
                            )}

                            <div style={{ flex: 1 }}>
                                <PickleVisualizer
                                    evaluationCase={memoizedEvaluationCase}
                                    pdpPaths={convertPathPairToPdpPaths(currentPair)}
                                    highlightPathIndex={null}
                                    showGroundTruth={false}
                                />
                            </div>
                        </div>
                    ) : (
                        <div style={{
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#999'
                        }}>
                            {loading.pklList ? (
                                <Spin size="large" tip="加载PKL列表中..." />
                            ) : loading.pathPairs ? (
                                <Spin size="large" tip="加载路径数据中..." />
                            ) : combinedPklList.length === 0 ? (
                                <Text>请选择评测集后开始标注</Text>
                            ) : (
                                <Text>请选择PKL文件开始标注</Text>
                            )}
                        </div>
                    )}
                </Content>

                {/* 右侧标注面板 */}
                <Sider
                    width={rightSiderWidth}
                    style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}
                >
                    <div style={{ padding: '16px', height: '100%' }}>
                        {selectedPkl && pathPairs.length > 0 ? (
                            <>
                                <Title level={4}>
                                    Pair {currentPairIndex + 1}/{pathPairs.length}
                                </Title>

                                <div style={{ marginBottom: '20px' }}>
                                    <Space direction="vertical" style={{ width: '100%' }}>
                                        <Button
                                            icon={<LeftOutlined />}
                                            onClick={() => setCurrentPairIndex(prev => Math.max(0, prev - 1))}
                                            disabled={currentPairIndex === 0}
                                            size="small"
                                        >
                                            上一个 (←/A)
                                        </Button>
                                        <Button
                                            icon={<RightOutlined />}
                                            onClick={() => setCurrentPairIndex(prev => Math.min(pathPairs.length - 1, prev + 1))}
                                            disabled={currentPairIndex === pathPairs.length - 1}
                                            size="small"
                                        >
                                            下一个 (→/D)
                                        </Button>
                                    </Space>
                                </div>

                                <div style={{ marginBottom: '20px' }}>
                                    <Text strong>请选择比较结果：</Text>
                                    <Space direction="vertical" style={{ width: '100%', marginTop: '12px' }}>
                                        <Button
                                            type={currentAnnotation?.comparison_result === 'A_better' ? 'primary' : 'default'}
                                            block
                                            onClick={() => handleAnnotate('A_better')}
                                            loading={loading.annotation}
                                            icon={currentAnnotation?.comparison_result === 'A_better' ? <CheckOutlined /> : null}
                                            style={{
                                                backgroundColor: currentAnnotation?.comparison_result === 'A_better' ? '#ffeb3b' : '#fff',
                                                borderColor: '#ffeb3b',
                                                color: currentAnnotation?.comparison_result === 'A_better' ? '#000' : '#ffeb3b'
                                            }}
                                        >
                                            Path A 更好 (1)
                                        </Button>
                                        <Button
                                            type={currentAnnotation?.comparison_result === 'B_better' ? 'primary' : 'default'}
                                            block
                                            onClick={() => handleAnnotate('B_better')}
                                            loading={loading.annotation}
                                            icon={currentAnnotation?.comparison_result === 'B_better' ? <CheckOutlined /> : null}
                                            style={{
                                                backgroundColor: currentAnnotation?.comparison_result === 'B_better' ? '#2196f3' : '#fff',
                                                borderColor: '#2196f3',
                                                color: currentAnnotation?.comparison_result === 'B_better' ? '#fff' : '#2196f3'
                                            }}
                                        >
                                            Path B 更好 (2)
                                        </Button>
                                        <Button
                                            type={currentAnnotation?.comparison_result === 'indistinguishable' ? 'primary' : 'default'}
                                            block
                                            onClick={() => handleAnnotate('indistinguishable')}
                                            loading={loading.annotation}
                                            icon={currentAnnotation?.comparison_result === 'indistinguishable' ? <CheckOutlined /> : null}
                                            style={{
                                                backgroundColor: currentAnnotation?.comparison_result === 'indistinguishable' ? '#f44336' : '#fff',
                                                borderColor: '#f44336',
                                                color: currentAnnotation?.comparison_result === 'indistinguishable' ? '#fff' : '#f44336'
                                            }}
                                        >
                                            不能区分 (3)
                                        </Button>
                                    </Space>
                                </div>

                                <Divider />

                            </>
                        ) : (
                            <div style={{ textAlign: 'center', color: '#999', marginTop: '50px' }}>
                                <Text>选择PKL文件后开始标注</Text>
                            </div>
                        )}
                    </div>
                </Sider>
            </Layout>
        </>
    );
};

export default CombinedPathPairAnnotation;