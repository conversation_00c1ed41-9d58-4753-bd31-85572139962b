import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Table, Statistic, Row, Col, Progress, Typography, Button, Tag, Space, message, Tooltip, Modal } from 'antd';
import { ArrowLeftOutlined, EditOutlined, FileOutlined, SyncOutlined, LinkOutlined, CheckCircleOutlined, EyeOutlined, SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;

interface AnnotatorProgress {
    annotator_id: string;
    annotated_pairs: number[];  // 已标注的pair索引数组
    annotations: Record<string, string>; // pair_key -> comparison_result
}

interface PairStatistics {
    pkl_id: number;
    pkl_name: string;
    total_pairs: number;
    annotated_pairs: number;
    annotator_count: number;
    consistent_pairs: number;
    inconsistent_pairs: number;
    annotator_progress: AnnotatorProgress[]; // 每个标注员的详细进度
}

interface OverallStatistics {
    total_pkls: number;
    annotated_pkls: number;     // 有任何标注的PKL数量
    completed_pkls: number;     // 完全完成标注的PKL数量
    pending_pkls: number;       // 完全未开始标注的PKL数量
    total_pairs: number;
    annotated_pairs: number;
    consistent_pairs: number;
    inconsistent_pairs: number;
    pending_pairs: number;
}

interface EvaluationSet {
    id: number;
    set_name: string;
    creator_name: string;
    description: string;
}

// 网格显示组件
// 网格显示组件
const AnnotationGrid: React.FC<{
    annotatorProgress: AnnotatorProgress[],
    totalPairs: number,
    onViewComparison: () => void
}> = ({ annotatorProgress, totalPairs, onViewComparison }) => {
    // 添加安全检查
    if (!annotatorProgress || annotatorProgress.length === 0) {
        return (
            <div style={{ padding: '8px', color: '#999', fontSize: '12px' }}>
                暂无标注数据
            </div>
        );
    }

    const gridSize = 8;// 计算网格大小
    const pairIndices = Array.from({ length: totalPairs }, (_, i) => i);

    // 获取pair的颜色
    const getPairColor = (pairIndex: number, annotatorProgress: AnnotatorProgress[]) => {
        const annotations = annotatorProgress
            .map(ap => ap.annotations?.[pairIndex.toString()])
            .filter(Boolean);

        if (annotations.length === 0) return '#f5f5f5'; // 未标注
        if (annotations.length === 1) return '#e6f7ff'; // 单人标注

        // 多人标注，检查是否一致
        const uniqueResults = new Set(annotations);
        if (uniqueResults.size === 1) return '#f6ffed'; // 一致
        return '#fff2e8'; // 冲突
    };

    // 获取pair的标注结果文本
    const getPairTooltip = (pairIndex: number, annotatorProgress: AnnotatorProgress[]) => {
        const results = annotatorProgress
            .map(ap => ({
                annotator: ap.annotator_id,
                result: ap.annotations?.[pairIndex.toString()]
            }))
            .filter(item => item.result);

        if (results.length === 0) return '未标注';

        return results.map(r => `${r.annotator}: ${r.result}`).join('\n');
    };

    return (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            {/* 图例 */}
            <div style={{ display: 'flex', gap: '15px', fontSize: '10px', marginBottom: '4px' }}>
                <span><span style={{ display: 'inline-block', width: '20px', height: '20px', backgroundColor: '#f5f5f5', marginRight: '4px', border: '1px solid #d9d9d9' }}></span>未标注</span>
                <span><span style={{ display: 'inline-block', width: '20px', height: '20px', backgroundColor: '#e6f7ff', marginRight: '4px', border: '1px solid #d9d9d9' }}></span>单人标注</span>
                <span><span style={{ display: 'inline-block', width: '20px', height: '20px', backgroundColor: '#f6ffed', marginRight: '4px', border: '1px solid #d9d9d9' }}></span>多人一致</span>
                <span><span style={{ display: 'inline-block', width: '20px', height: '20px', backgroundColor: '#fff2e8', marginRight: '4px', border: '1px solid #d9d9d9' }}></span>存在冲突</span>
            </div>

            {/* 网格 */}
            <div style={{
                display: 'grid',
                gridTemplateColumns: `repeat(${Math.min(gridSize, 15)}, 24px)`, // 每列宽度固定为24px
                gap: '0px', // 去掉单元格之间的间距
                maxWidth: '180px', // 限制网格宽度
            }}>
                {pairIndices.map(pairIndex => (
                    <Tooltip key={pairIndex} title={getPairTooltip(pairIndex, annotatorProgress)}>
                        <div
                            style={{
                                width: '24px',
                                height: '24px',
                                backgroundColor: getPairColor(pairIndex, annotatorProgress),
                                border: '1px solid #d9d9d9',
                                borderRadius: '2px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '8px',
                                cursor: 'pointer'
                            }}
                        >
                            {pairIndex + 1}
                        </div>
                    </Tooltip>
                ))}
            </div>

            {/* 标注员列表 */}
            <div style={{ marginTop: '4px' }}>
                {annotatorProgress.map(ap => (
                    <div key={ap.annotator_id} style={{ fontSize: '10px', color: '#666' }}>
                        <Text>{ap.annotator_id}: {ap.annotated_pairs?.length || 0}/{totalPairs}</Text>
                    </div>
                ))}
            </div>
        </div>
    );
};

// 对比查看模态框
const ComparisonModal: React.FC<{
    visible: boolean;
    onClose: () => void;
    pklId: number;
    evaluationSetId: string;
    annotatorProgress: AnnotatorProgress[];
}> = ({ visible, onClose, pklId, evaluationSetId, annotatorProgress }) => {
    const navigate = useNavigate();

    const handleViewComparison = () => {
        onClose();
        navigate(`/path-pair-comparison/${evaluationSetId}/${pklId}`, {
            state: { annotatorProgress }
        });
    };

    // 添加安全检查
    if (!annotatorProgress || annotatorProgress.length === 0) {
        return (
            <Modal
                title="标注结果对比"
                open={visible}
                onCancel={onClose}
                footer={[
                    <Button key="close" onClick={onClose}>关闭</Button>
                ]}
                width={600}
            >
                <div>
                    <Text type="secondary">暂无标注数据</Text>
                </div>
            </Modal>
        );
    }

    // 修复类型定义
    interface ConflictPair {
        pairIndex: number;
        annotations: { annotator: string; result: string; }[];
    }

    const conflictPairs: ConflictPair[] = [];
    for (let i = 0; i < 15; i++) { // 假设最多15个pairs
        const annotations = annotatorProgress
            .map(ap => ap.annotations?.[i.toString()])
            .filter(Boolean);

        if (annotations.length > 1) {
            const uniqueResults = new Set(annotations);
            if (uniqueResults.size > 1) {
                conflictPairs.push({
                    pairIndex: i,
                    annotations: annotatorProgress
                        .map(ap => ({
                            annotator: ap.annotator_id,
                            result: ap.annotations?.[i.toString()]
                        }))
                        .filter(item => item.result)
                });
            }
        }
    }

    return (
        <Modal
            title="标注结果对比"
            open={visible}
            onCancel={onClose}
            footer={[
                <Button key="close" onClick={onClose}>关闭</Button>,
                <Button key="view" type="primary" onClick={handleViewComparison}>
                    进入可视化对比界面
                </Button>
            ]}
            width={600}
        >
            <div>
                <Title level={4}>冲突标注统计</Title>
                {conflictPairs.length === 0 ? (
                    <Text type="secondary">暂无冲突标注</Text>
                ) : (
                    <div>
                        {conflictPairs.map(conflict => (
                            <Card key={conflict.pairIndex} size="small" style={{ marginBottom: '8px' }}>
                                <Text strong>Pair {conflict.pairIndex + 1}:</Text>
                                <div style={{ marginTop: '4px' }}>
                                    {conflict.annotations.map(ann => (
                                        <Tag key={ann.annotator} color={
                                            ann.result === 'A_better' ? 'gold' :
                                                ann.result === 'B_better' ? 'blue' : 'orange'
                                        }>
                                            {ann.annotator}: {ann.result}
                                        </Tag>
                                    ))}
                                </div>
                            </Card>
                        ))}
                    </div>
                )}
            </div>
        </Modal>
    );
};

const PathPairStatistics: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();

    const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(null);
    const [overallStats, setOverallStats] = useState<OverallStatistics | null>(null);
    const [pklStats, setPklStats] = useState<PairStatistics[]>([]);
    const [loading, setLoading] = useState(true);
    const [overviewLoading, setOverviewLoading] = useState(true);
    const [sortField, setSortField] = useState<string>('');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
    const [comparisonModal, setComparisonModal] = useState<{
        visible: boolean;
        pklId: number;
        annotatorProgress: AnnotatorProgress[];
    }>({ visible: false, pklId: 0, annotatorProgress: [] });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });

    // 加载总体统计数据
    const loadOverviewStatistics = async () => {
        setOverviewLoading(true);
        try {
            const response = await axios.get(`/api/annotation/path-pair-overview-statistics/${id}`);
            if (response.data.success) {
                setOverallStats(response.data.overview_statistics);
            }
        } catch (error) {
            message.error('加载总体统计数据失败');
            console.error('Error loading overview statistics:', error);
        } finally {
            setOverviewLoading(false);
        }
    };

    // 加载分页统计数据
    const loadPaginatedStatistics = async (page = 1, pageSize = 20, sortField = 'pkl_name', sortOrder = 'desc') => {
        setLoading(true);
        try {
            const response = await axios.get(`/api/annotation/path-pair-statistics/${id}`, {
                params: { page, page_size: pageSize, sort_field: sortField, sort_order: sortOrder }
            });

            if (response.data.success) {
                setPklStats(response.data.pkl_statistics);
                setPagination({
                    current: response.data.pagination.current_page,
                    pageSize: response.data.pagination.page_size,
                    total: response.data.pagination.total_count
                });
            }
        } catch (error) {
            message.error('加载分页统计数据失败');
            console.error('Error loading paginated statistics:', error);
        } finally {
            setLoading(false);
        }
    };

    // 加载评测集信息
    const loadEvaluationSet = async () => {
        try {
            const response = await axios.get(`/api/evaluation_sets/${id}`);
            if (response.data.success) {
                setEvaluationSet(response.data.evaluation_set);
            }
        } catch (error) {
            message.error('加载评测集信息失败');
            console.error('Error loading evaluation set:', error);
        }
    };

    useEffect(() => {
        if (id) {
            loadEvaluationSet();
            loadOverviewStatistics();
            loadPaginatedStatistics();
        }
    }, [id]);

    // 排序处理
    const handleSort = (field: string) => {
        // 取消所有排序方式，统一使用asc
        setSortField(field);
        setSortOrder('asc');

        // 调用后端API重新加载数据
        loadPaginatedStatistics(pagination.current, pagination.pageSize, field);
    };

    const columns = [
        {
            title: 'PKL文件名',
            dataIndex: 'pkl_name',
            key: 'pkl_name',
            width: 300,
            render: (text: string) => (
                <Text style={{ fontSize: '12px' }}>{text}</Text>
            )
        },
        {
            title: (
                <div style={{ cursor: 'pointer' }} onClick={() => handleSort('progress')}>
                    标注进度
                    {sortField === 'progress' && (
                        <SortAscendingOutlined style={{ marginLeft: 4 }} />
                    )}
                </div>
            ),
            key: 'progress',
            width: 350,
            render: (record: PairStatistics) => (
                <AnnotationGrid
                    annotatorProgress={record.annotator_progress}
                    totalPairs={record.total_pairs}
                    onViewComparison={() => setComparisonModal({
                        visible: true,
                        pklId: record.pkl_id,
                        annotatorProgress: record.annotator_progress
                    })}
                />
            )
        },
        {
            title: (
                <div style={{ cursor: 'pointer' }} onClick={() => handleSort('consistency')}>
                    一致性
                    {sortField === 'consistency' && (
                        <SortAscendingOutlined style={{ marginLeft: 4 }} />
                    )}
                </div>
            ),
            key: 'consistency',
            width: 120,
            render: (record: PairStatistics) => {
                if (record.annotated_pairs === 0) return <Text type="secondary">-</Text>;

                const consistencyRate = record.annotated_pairs > 0
                    ? Math.round((record.consistent_pairs / record.annotated_pairs) * 100)
                    : 0;

                let color = 'default';
                if (consistencyRate >= 80) color = 'success';
                else if (consistencyRate >= 60) color = 'warning';
                else color = 'error';

                return <Tag color={color}>{consistencyRate}%</Tag>;
            }
        },
        {
            title: '标注员数',
            dataIndex: 'annotator_count',
            key: 'annotator_count',
            width: 80,
            align: 'center' as const
        },
        {
            title: (
                <div style={{ cursor: 'pointer' }} onClick={() => handleSort('inconsistent_pairs')}>
                    冲突数
                    {sortField === 'inconsistent_pairs' && (
                        <SortAscendingOutlined style={{ marginLeft: 4 }} />
                    )}
                </div>
            ),
            dataIndex: 'inconsistent_pairs',
            key: 'inconsistent_pairs',
            width: 80,
            align: 'center' as const,
            render: (value: number) => (
                <Text type={value > 0 ? 'warning' : 'secondary'}>{value}</Text>
            )
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (record: PairStatistics) => (
                <Space>
                    <Button
                        type="link"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => {
                            navigate(`/path-pair-annotation/${id}`, {
                                state: { selectedPklId: record.pkl_id }
                            });
                        }}
                    >
                        标注
                    </Button>
                    {record.annotator_count > 1 && (
                        <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => setComparisonModal({
                                visible: true,
                                pklId: record.pkl_id,
                                annotatorProgress: record.annotator_progress
                            })}
                        >
                            查看
                        </Button>
                    )}
                </Space>
            )
        }
    ];

    return (
        <div style={{ padding: '24px' }}>
            {/* 页面头部 */}
            <div style={{ marginBottom: '24px' }}>
                <Title level={2}>路径Pair标注统计</Title>
                {evaluationSet && (
                    <div>
                        <Text strong>评测集：</Text>
                        <Text>{evaluationSet.set_name}</Text>
                        <br />
                        <Text type="secondary">创建者：{evaluationSet.creator_name}</Text>
                    </div>
                )}
            </div>

            {/* 总体统计卡片 */}
            {overallStats && (
                <Row gutter={16} style={{ marginBottom: '24px' }}>
                    <Col span={6}>
                        <Card loading={overviewLoading}>
                            <Statistic
                                title="PKL文件总数"
                                value={overallStats.total_pkls}
                                prefix={<FileOutlined />}
                            />
                            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                                已开始: {overallStats.annotated_pkls} |
                                已完成: {overallStats.completed_pkls} |
                                未开始: {overallStats.pending_pkls}
                            </div>
                        </Card>
                    </Col>
                    <Col span={6}>
                        <Card loading={overviewLoading}>
                            <Statistic
                                title="标注进度"
                                value={overallStats.total_pkls > 0 ? Math.round((overallStats.annotated_pkls / overallStats.total_pkls) * 100) : 0}
                                suffix="%"
                                prefix={<CheckCircleOutlined />}
                            />
                            <Progress
                                percent={overallStats.total_pkls > 0 ? Math.round((overallStats.annotated_pkls / overallStats.total_pkls) * 100) : 0}
                                size="small"
                                showInfo={false}
                            />
                        </Card>
                    </Col>
                    <Col span={6}>
                        <Card loading={overviewLoading}>
                            <Statistic
                                title="Pair标注总数"
                                value={overallStats.annotated_pairs}
                                suffix={`/ ${overallStats.total_pairs}`}
                                prefix={<LinkOutlined />}
                            />
                            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                                待标注: {overallStats.pending_pairs}
                            </div>
                        </Card>
                    </Col>
                    <Col span={6}>
                        <Card loading={overviewLoading}>
                            <Statistic
                                title="标注一致性"
                                value={overallStats.annotated_pairs > 0 ? Math.round((overallStats.consistent_pairs / overallStats.annotated_pairs) * 100) : 0}
                                suffix="%"
                                prefix={<SyncOutlined />}
                            />
                            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                                一致: {overallStats.consistent_pairs} |
                                冲突: {overallStats.inconsistent_pairs}
                            </div>
                        </Card>
                    </Col>
                </Row>
            )}

            {/* PKL文件详情表格 */}
            <Card title="PKL文件标注详情" style={{ marginBottom: '24px' }}>
                <Table
                    columns={columns}
                    dataSource={pklStats}
                    rowKey="pkl_id"
                    loading={loading}
                    pagination={{
                        current: pagination.current,
                        pageSize: pagination.pageSize,
                        total: pagination.total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 个PKL文件`,
                        onChange: (page, size) => {
                            loadPaginatedStatistics(page, size || pagination.pageSize, sortField);
                        },
                        onShowSizeChange: (current, size) => {
                            loadPaginatedStatistics(1, size, sortField);
                        }
                    }}
                    size="middle"
                />
            </Card>

            {/* 对比模态框 */}
            <ComparisonModal
                visible={comparisonModal.visible}
                onClose={() => setComparisonModal({ ...comparisonModal, visible: false })}
                pklId={comparisonModal.pklId}
                evaluationSetId={id!}
                annotatorProgress={comparisonModal.annotatorProgress}
            />

            {/* 操作按钮 */}
            <div style={{ textAlign: 'center' }}>
                <Space>
                    <Button onClick={() => navigate(`/`)}>
                        返回
                    </Button>
                </Space>
            </div>
        </div>
    );
};

export default PathPairStatistics;