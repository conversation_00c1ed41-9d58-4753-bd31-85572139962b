import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Layout, List, Button, Card, Typography, Progress, Tag, message, Spin, Space, Alert, Modal, Checkbox, Radio, RadioChangeEvent, Input } from 'antd';
import { LeftOutlined, RightOutlined, CheckOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import PickleVisualizer from '../../components/PickleVisualizer';
import ImageWithPaths from '../../components/ImageWithPaths';
import '../PdpPathAnnotation.css';
import { EvaluationCase, EvaluationSet, PdpPathInfo } from '../../types';

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

interface PathInfo {
    index: number;  // -1表示GT路径
    type: 'ground_truth' | 'predicted';
    probability: number;
    points: number[][];
}

interface PathPair {
    pair_id: number;
    path_a: PathInfo;
    path_b: PathInfo;
}

interface PairAnnotation {
    pkl_id: number;
    path_a_index: number;
    path_b_index: number;
    comparison_result: 'A_better' | 'B_better' | 'indistinguishable';
    annotator_id: string;
    created_at: string;
}

const PathPairAnnotation: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const { user, isAuthenticated } = useAuth();

    // 状态管理
    const [leftSiderWidth, setLeftSiderWidth] = useState(300);
    const [rightSiderWidth, setRightSiderWidth] = useState(280);

    const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(null);
    const [pklList, setPklList] = useState<EvaluationCase[]>([]);
    const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);

    const [pathPairs, setPathPairs] = useState<PathPair[]>([]);
    const [currentPairIndex, setCurrentPairIndex] = useState(0);
    const [annotations, setAnnotations] = useState<PairAnnotation[]>([]);

    // 添加筛选状态
    const [showAnnotatedOnly, setShowAnnotatedOnly] = useState(false);
    
    // 修改：将VIN码改为bag_name过滤相关状态
    const [bagName, setBagName] = useState('');
    const [timeNs, setTimeNs] = useState('');
    const [timeRange, setTimeRange] = useState('');

    const [loading, setLoading] = useState({
        pklList: false,
        pathPairs: false,
        annotation: false,
    });

    const [pklPagination, setPklPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0
    });

    const [exportModalVisible, setExportModalVisible] = useState(false);
    const [exportConfig, setExportConfig] = useState({
        includeConflicts: true,
        format: 'json' as 'json' | 'csv'
    });
    const [exportLoading, setExportLoading] = useState(false);

    const [imageData, setImageData] = useState<string | null>(null);

    // 新增：ego车体姿态相关状态
    const [ego2img, setEgo2img] = useState<number[][] | null>(null);
    const [egoYaw, setEgoYaw] = useState<number | null>(null);

    // 验证用户身份
    useEffect(() => {
        if (!isAuthenticated) {
            navigate('/login');
        }
    }, [isAuthenticated, navigate]);
    // 处理标注
    const handleAnnotate = async (result: 'A_better' | 'B_better' | 'indistinguishable') => {
        if (!selectedPkl || pathPairs.length === 0) return;

        const currentPair = pathPairs[currentPairIndex];
        setLoading(prev => ({ ...prev, annotation: true }));

        try {
            const response = await axios.post('/api/annotation/path-pair-annotation', {
                pkl_id: selectedPkl.id,
                path_a_index: currentPair.path_a.index,
                path_b_index: currentPair.path_b.index,
                comparison_result: result,
                evaluation_set_id: parseInt(id!),
                annotator_id: user?.username
            });

            if (response.data.success) {
                // 更新本地标注状态
                const newAnnotation: PairAnnotation = {
                    pkl_id: selectedPkl.id,
                    path_a_index: currentPair.path_a.index,
                    path_b_index: currentPair.path_b.index,
                    comparison_result: result,
                    annotator_id: user?.username || '',
                    created_at: new Date().toISOString()
                };

                setAnnotations(prev => {
                    const filtered = prev.filter(ann =>
                        !(ann.path_a_index === currentPair.path_a.index &&
                            ann.path_b_index === currentPair.path_b.index &&
                            ann.annotator_id === user?.username)
                    );
                    return [...filtered, newAnnotation];
                });

                message.success('标注成功');

                // 自动跳转到下一个pair
                if (currentPairIndex < pathPairs.length - 1) {
                    setCurrentPairIndex(prev => prev + 1);
                }
            }
        } catch (error) {
            message.error('标注失败');
            console.error('Error annotating:', error);
        } finally {
            setLoading(prev => ({ ...prev, annotation: false }));
        }
    };

    // 键盘事件处理
    const handleKeyDown = useCallback((event: KeyboardEvent) => {
        if (loading.annotation) return; // 正在标注时禁用键盘操作
        const activeElement = document.activeElement;
        if (activeElement && (
            activeElement.tagName === 'INPUT' || 
            activeElement.tagName === 'TEXTAREA' || 
            activeElement.getAttribute('contenteditable') === 'true'
        )) {
            return; // 当前焦点在编辑框内，禁用键盘快捷键
        }
        
        switch (event.key) {
            case 'ArrowLeft':
            case 'a':
            case 'A':
                event.preventDefault();
                setCurrentPairIndex(prev => Math.max(0, prev - 1));
                break;
            case 'ArrowRight':
            case 'd':
            case 'D':
                event.preventDefault();
                setCurrentPairIndex(prev => Math.min(pathPairs.length - 1, prev + 1));
                break;
            case '1':
                event.preventDefault();
                handleAnnotate('A_better');
                break;
            case '2':
                event.preventDefault();
                handleAnnotate('B_better');
                break;
            case '3':
                event.preventDefault();
                handleAnnotate('indistinguishable');
                break;
        }
    }, [pathPairs.length, loading.annotation, handleAnnotate]);

    // 添加键盘事件监听
    useEffect(() => {
        window.addEventListener('keydown', handleKeyDown);
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [handleKeyDown]);

    // 加载评测集信息和PKL列表
    const loadEvaluationSet = async () => {
        try {
            const response = await axios.get(`/api/evaluation_sets/${id}`);
            if (response.data.success) {
                // 修复：正确设置评测集数据结构
                const evaluationSetData: EvaluationSet = {
                    id: response.data.evaluation_set.id,
                    set_name: response.data.evaluation_set.name, // 注意这里是 name，不是 set_name
                    creator_name: response.data.evaluation_set.creator_name || '',
                    description: response.data.evaluation_set.description || '',
                    cases: response.data.cases || [],
                    case_count: response.data.case_count || 0,
                    created_at: response.data.evaluation_set.created_at
                };
                setEvaluationSet(evaluationSetData);
            }
        } catch (error) {
            console.error('Failed to load evaluation set:', error);
            message.error('加载评测集信息失败');
        }
    };

    const loadPklList = async (
        page = 1, 
        pageSize = 20,
        annotatedOnly = showAnnotatedOnly,
        bag_name = bagName,
        time_ns = timeNs,
        time_range = timeRange
    ) => {
        setLoading(prev => ({ ...prev, pklList: true }));
        try {
            const params: any = {
                page,
                per_page: pageSize,
                dirty_filter: false, // 过滤掉脏数据
                annotated_filter: annotatedOnly // 添加标注筛选参数
            };

            // 只有当bag_name、时间戳和时间范围都不为空时，才添加这些参数
            if (bag_name && time_ns && time_range) {
                params.bag_name = bag_name;
                params.time_ns = time_ns;
                params.time_range = time_range;
            }

            const response = await axios.get(`/api/evaluation_sets/${id}`, { params });

            if (response.data.success) {
                setPklList(response.data.cases);
                setPklPagination({
                    current: page,
                    pageSize,
                    total: response.data.case_count || 0
                });
            }
        } catch (error) {
            message.error('加载PKL列表失败');
        } finally {
            setLoading(prev => ({ ...prev, pklList: false }));
        }
    };

    // 加载路径pairs
    const loadPathPairs = async (pklId: number) => {
        setLoading(prev => ({ ...prev, pathPairs: true }));
        try {
            const response = await axios.get(`/api/annotation/path-pairs/${pklId}`, {
                params: {
                    evaluation_set_id: id
                }
            });

            if (response.data.success) {
                setPathPairs(response.data.path_pairs);
                setAnnotations(response.data.annotations);
                setCurrentPairIndex(0);

                // 设置图像数据
                if (response.data.image_data) {
                    setImageData(response.data.image_data);
                } else {
                    setImageData(null);
                }

                // 设置投影所需的数据
                if (response.data.ego2img) {
                    setEgo2img(response.data.ego2img);
                } else {
                    setEgo2img(null);
                }

                if (response.data.ego_yaw !== null && response.data.ego_yaw !== undefined) {
                    setEgoYaw(response.data.ego_yaw);
                } else {
                    setEgoYaw(null);
                }
            }
        } catch (error) {
            message.error('加载路径pairs失败');
            console.error('❌ 加载路径pairs错误:', error);
        } finally {
            setLoading(prev => ({ ...prev, pathPairs: false }));
        }
    };

    // 处理PKL选择
    const handlePklSelect = async (pkl: EvaluationCase) => {
        setSelectedPkl(pkl);
        setImageData(null);
        setEgo2img(null);    // 添加这行
        setEgoYaw(null);     // 添加这行
        await loadPathPairs(pkl.id);
    };


    // 获取当前pair的标注状态
    const getCurrentAnnotation = () => {
        if (!selectedPkl || pathPairs.length === 0) return null;

        const currentPair = pathPairs[currentPairIndex];
        return annotations.find(ann =>
            ann.path_a_index === currentPair.path_a.index &&
            ann.path_b_index === currentPair.path_b.index &&
            ann.annotator_id === user?.username
        );
    };

    // 计算当前PKL的标注进度
    const getAnnotationProgress = () => {
        if (!selectedPkl || pathPairs.length === 0) return { completed: 0, total: 0 };

        const userAnnotations = annotations.filter(ann => ann.annotator_id === user?.username);
        return {
            completed: userAnnotations.length,
            total: pathPairs.length
        };
    };

    // 导出标注结果
    const handleExport = async () => {
        setExportLoading(true);
        try {
            // 构建查询参数
            const params = new URLSearchParams({
                include_conflicts: exportConfig.includeConflicts.toString()
            });

            // 直接跳转到API接口
            const exportUrl = `/api/annotation/export-pair-annotations/${id}?${params.toString()}`;
            window.open(exportUrl, '_blank');

            message.success('正在新窗口中显示导出结果');
            setExportModalVisible(false);
        } catch (error) {
            message.error('导出失败');
            console.error('Export error:', error);
        } finally {
            setExportLoading(false);
        }
    };

    // 初始化加载
    useEffect(() => {
        if (id) {
            loadEvaluationSet();
            loadPklList();
        }
    }, [id]);

    const currentPair = pathPairs[currentPairIndex];
    const currentAnnotation = getCurrentAnnotation();
    const progress = getAnnotationProgress();
    const memoizedEvaluationCase = useMemo(() => {
        if (!selectedPkl) return null;
        return {
            ...selectedPkl,
            pkl_dir: selectedPkl.pkl_dir || '/default/path' // 确保有 pkl_dir
        };
    }, [selectedPkl]);

    // 添加函数来转换pathPair为PdpPathInfo格式
    const convertPathPairToPdpPaths = (pathPair: PathPair): Record<number, PdpPathInfo> => {
        const pdpPaths: Record<number, PdpPathInfo> = {};

        // 转换 path_a
        pdpPaths[pathPair.path_a.index] = {
            index: pathPair.path_a.index,
            probability: pathPair.path_a.probability,
            points_count: pathPair.path_a.points.length,
            visualization_points: pathPair.path_a.points,
            is_ground_truth: pathPair.path_a.type === 'ground_truth',
            middle_point: pathPair.path_a.points[Math.floor(pathPair.path_a.points.length / 2)] || [0, 0, 0],
            color: "#ffff00",      // 黄色
            lineWidth: 4           // 宽度为4
        };

        // 转换 path_b
        pdpPaths[pathPair.path_b.index] = {
            index: pathPair.path_b.index,
            probability: pathPair.path_b.probability,
            points_count: pathPair.path_b.points.length,
            visualization_points: pathPair.path_b.points,
            is_ground_truth: pathPair.path_b.type === 'ground_truth',
            middle_point: pathPair.path_b.points[Math.floor(pathPair.path_b.points.length / 2)] || [0, 0, 0],
            color: "#0000ff",      // 蓝色
            lineWidth: 4
        };

        return pdpPaths;
    };

    // 添加函数来转换pathPair为ImageWithPaths所需的格式
    const convertPathPairToImagePaths = (pathPair: PathPair): Record<number, PdpPathInfo> => {
        if (!pathPair) {
            return {};
        }

        const imagePaths: Record<number, PdpPathInfo> = {};

        // 转换 path_a - 使用黄色
        if (pathPair.path_a && pathPair.path_a.points) {
            imagePaths[pathPair.path_a.index] = {
                index: pathPair.path_a.index,
                probability: pathPair.path_a.probability,
                points_count: pathPair.path_a.points.length,
                visualization_points: pathPair.path_a.points,
                is_ground_truth: pathPair.path_a.type === 'ground_truth',
                middle_point: pathPair.path_a.points[Math.floor(pathPair.path_a.points.length / 2)] || [0, 0, 0],
                annotation: undefined, // 改为undefined
                color: "#ffff00",
                lineWidth: 4
            };
        }

        // 转换 path_b - 使用蓝色
        if (pathPair.path_b && pathPair.path_b.points) {
            imagePaths[pathPair.path_b.index] = {
                index: pathPair.path_b.index,
                probability: pathPair.path_b.probability,
                points_count: pathPair.path_b.points.length,
                visualization_points: pathPair.path_b.points,
                is_ground_truth: pathPair.path_b.type === 'ground_truth',
                middle_point: pathPair.path_b.points[Math.floor(pathPair.path_b.points.length / 2)] || [0, 0, 0],
                annotation: undefined, // 改为undefined
                color: "#0000ff",
                lineWidth: 4
            };
        }

        return imagePaths;
    };

    // 处理筛选切换
    const handleFilterToggle = () => {
        const newFilterState = !showAnnotatedOnly;
        setShowAnnotatedOnly(newFilterState);
        // 重置到第一页
        setPklPagination(prev => ({ ...prev, current: 1 }));
        // 清除当前选择
        setSelectedPkl(null);
        setPathPairs([]);
        setAnnotations([]);
        setCurrentPairIndex(0);
        // 重新加载列表
        loadPklList(1, pklPagination.pageSize, newFilterState, bagName, timeNs, timeRange);
    };

    // 修改：处理bag_name和时间戳过滤
    const handleBagTimeFilter = () => {
        loadPklList(1, pklPagination.pageSize, showAnnotatedOnly, bagName, timeNs, timeRange);
        setPklPagination(prev => ({ ...prev, current: 1 }));
    };

    // 修改：清空bag_name和时间戳过滤
    const handleClearBagTimeFilter = () => {
        setBagName('');
        setTimeNs('');
        setTimeRange('');
        loadPklList(1, pklPagination.pageSize, showAnnotatedOnly, '', '', '');
        setPklPagination(prev => ({ ...prev, current: 1 }));
    };

    // 当筛选状态改变时重新加载列表
    useEffect(() => {
        if (id) {
            loadPklList(1, pklPagination.pageSize);
        }
    }, [showAnnotatedOnly]);

    return (
        <Layout style={{ height: 'calc(100vh - 120px)' }}>
            {/* 左侧PKL列表 */}
            <Sider
                width={leftSiderWidth}
                style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}
            >
                <div style={{ padding: '2px 4px' }}>
                    {/* 修改：压缩测试集名字显示 */}
                    <Title level={5} style={{ marginBottom: '2px', fontSize: '12px', lineHeight: '1.2' }}>
                        {evaluationSet?.set_name || 'PKL文件列表'}
                    </Title>

                    {/* 修改：压缩bag_name和时间戳过滤输入框 */}
                    <div style={{ marginBottom: '4px', border: '1px solid #d9d9d9', padding: '3px', borderRadius: '3px' }}>
                        <Text strong style={{ fontSize: '9px', color: '#666' }}>bag_name时间戳过滤:</Text>
                        <Input
                            placeholder="bag_name (例: LFZ63AZ52SD000199_record_data_2025_05_04_08_40_39)"
                            value={bagName}
                            onChange={e => setBagName(e.target.value)}
                            size="small"
                            style={{ marginTop: '1px' }} 
                        />
                        <Input
                            placeholder="时间戳(纳秒)"
                            value={timeNs}
                            onChange={e => setTimeNs(e.target.value)}
                            size="small"
                            style={{ marginTop: '1px' }} 
                        />
                        <Input
                            placeholder="时间范围(秒)"
                            value={timeRange}
                            onChange={e => setTimeRange(e.target.value)}
                            size="small"
                            style={{ marginTop: '1px' }} 
                        />
                        <Space size="small" style={{ marginTop: '1px', width: '100%' }}>
                            <Button
                                type="primary"
                                size="small"
                                onClick={handleBagTimeFilter}
                                style={{ flex: 1 }}
                                icon={<SearchOutlined />}
                            >
                                应用过滤
                            </Button>
                            <Button
                                size="small"
                                onClick={handleClearBagTimeFilter}
                                style={{ flex: 1 }}
                            >
                                清空
                            </Button>
                        </Space>
                    </div>

                    {/* 修改：压缩筛选按钮 */}
                    <Button
                        type={showAnnotatedOnly ? 'primary' : 'default'}
                        onClick={handleFilterToggle}
                        style={{ marginBottom: '2px', width: '100%' }} 
                        size="small"
                    >
                        {showAnnotatedOnly ? '显示全部PKL' : '仅显示已标注PKL'}
                    </Button>

                    {/* 修改：压缩导出按钮 */}
                    <Button
                        type="primary"
                        icon={<DownloadOutlined />}
                        onClick={() => setExportModalVisible(true)}
                        style={{ marginBottom: '2px', width: '100%' }} 
                        size="small"
                    >
                        导出标注结果
                    </Button>

                    {/* 修改：压缩进度卡片 */}
                    {selectedPkl && (
                        <Card size="small" style={{ marginBottom: '2px' }}>
                            <Text strong style={{ fontSize: '9px' }}>当前标注进度</Text>
                            <Progress
                                percent={progress.total > 0 ? Math.round((progress.completed / progress.total) * 100) : 0}
                                format={() => `${progress.completed}/${progress.total}`}
                                size="small"
                            />
                        </Card>
                    )}
                </div>

                {/* 修改：调整PKL列表区域高度 */}
                <div style={{ height: 'calc(100% - 220px)', overflow: 'auto', paddingLeft: '16px', paddingRight: '16px' }}>
                    <List
                        loading={loading.pklList}
                        dataSource={pklList}
                        pagination={{
                            onChange: (page, pageSize) => {
                                loadPklList(page, pageSize, showAnnotatedOnly, bagName, timeNs, timeRange);
                            },
                            current: pklPagination.current,
                            pageSize: pklPagination.pageSize,
                            total: pklPagination.total,
                            size: 'small',
                            showSizeChanger: false,
                        }}
                        renderItem={(pkl) => (
                            <List.Item
                                onClick={() => handlePklSelect(pkl)}
                                style={{
                                    cursor: 'pointer',
                                    backgroundColor: selectedPkl?.id === pkl.id ? '#e6f7ff' : 'transparent',
                                    padding: '4px 0',
                                    borderRadius: '3px',
                                    margin: '1px 0'
                                }}
                            >
                                <div style={{ width: '100%' }}>
                                    <div style={{
                                        fontWeight: selectedPkl?.id === pkl.id ? 'bold' : 'normal',
                                        marginBottom: '1px',
                                        fontSize: '9px',
                                        lineHeight: '1.2',
                                        wordBreak: 'break-all',
                                        whiteSpace: 'normal'
                                    }}>
                                        {pkl.pkl_name}
                                    </div>
                                </div>
                            </List.Item>
                        )}
                    />
                </div>
            </Sider>

            {/* 中间可视化区域 */}
            <Content style={{ background: '#fff', position: 'relative' }}>
                {selectedPkl ? (
                    loading.pathPairs ? (
                        <div style={{
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <Spin size="large" tip="加载路径数据中..." />
                        </div>
                    ) : pathPairs.length > 0 && currentPair ? (
                        <div className="visualization-area" style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                            {/* E2E图像显示区域 - 修复横向居中问题 */}
                            {imageData ? (
                                <div className="e2e-image-container" style={{ 
                                    width: '100%', 
                                    display: 'flex',          // 添加flex布局
                                    justifyContent: 'center', // 水平居中
                                    alignItems: 'center',     // 垂直居中
                                    marginBottom: '10px'
                                }}>
                                    {ego2img && egoYaw !== null ? (
                                        // 使用 ImageWithPaths 组件显示投影路径
                                        <ImageWithPaths
                                            imageData={imageData}
                                            pdpPaths={convertPathPairToImagePaths(currentPair)}
                                            highlightPathIndex={null}
                                            ego2img={ego2img}
                                            egoYaw={egoYaw}
                                            style={{ 
                                                maxWidth: '100%',
                                                maxHeight: '200px',
                                                display: 'block'  // 确保图像块级显示
                                            }}
                                        />
                                    ) : (
                                        // 如果没有投影数据，显示原始图像
                                        <img
                                            src={`data:image/jpeg;base64,${imageData}`}
                                            alt="E2E图像"
                                            style={{
                                                maxWidth: '100%',
                                                maxHeight: '200px',
                                                objectFit: 'contain',
                                                display: 'block'  // 确保图像块级显示
                                            }}
                                        />
                                    )}
                                </div>
                            ) : (
                                <div style={{ 
                                    height: '100px', 
                                    marginBottom: '10px',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    border: '1px dashed #d9d9d9',
                                    borderRadius: '4px',
                                    color: '#999'
                                }}>
                                    <Text>无图像数据</Text>
                                </div>
                            )}

                            {/* 3D可视化区域 - 参考PdpPathAnnotation的样式 */}
                            <div style={{ 
                                width: '100%', 
                                height: imageData ? 'calc(100% - 220px)' : '100%',
                                minHeight: '400px'
                            }}>
                                {memoizedEvaluationCase ? (
                                    <PickleVisualizer
                                        evaluationCase={memoizedEvaluationCase}
                                        pdpPaths={convertPathPairToPdpPaths(currentPair)}
                                        highlightPathIndex={null}
                                        showGroundTruth={false}
                                        height={imageData ? 'calc(100vh - 320px)' : '60vh'}
                                    />
                                ) : (
                                    <div style={{
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: '#999'
                                    }}>
                                        <Text>无可视化数据</Text>
                                    </div>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div style={{
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#999'
                        }}>
                            <Text>该PKL文件无路径对数据</Text>
                        </div>
                    )
                ) : (
                    <div style={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#999'
                    }}>
                        <Text>请选择PKL文件开始标注</Text>
                    </div>
                )}
            </Content>

            {/* 右侧标注面板 */}
            <Sider
                width={rightSiderWidth}
                style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}
            >
                <div style={{ padding: '16px', height: '100%' }}>
                    {selectedPkl && pathPairs.length > 0 ? (
                        <>
                            <Title level={4}>
                                Pair {currentPairIndex + 1}/{pathPairs.length}
                            </Title>

                            <div style={{ marginBottom: '20px' }}>
                                <Space direction="vertical" style={{ width: '100%' }}>
                                    <Button
                                        icon={<LeftOutlined />}
                                        onClick={() => setCurrentPairIndex(prev => Math.max(0, prev - 1))}
                                        disabled={currentPairIndex === 0}
                                        size="small"
                                    >
                                        上一个 (←/A)
                                    </Button>
                                    <Button
                                        icon={<RightOutlined />}
                                        onClick={() => setCurrentPairIndex(prev => Math.min(pathPairs.length - 1, prev + 1))}
                                        disabled={currentPairIndex === pathPairs.length - 1}
                                        size="small"
                                    >
                                        下一个 (→/D)
                                    </Button>
                                </Space>
                            </div>

                            <div style={{ marginBottom: '20px' }}>
                                <Text strong>请选择比较结果：</Text>
                                <Space direction="vertical" style={{ width: '100%', marginTop: '12px' }}>
                                    <Button
                                        type={currentAnnotation?.comparison_result === 'A_better' ? 'primary' : 'default'}
                                        block
                                        onClick={() => handleAnnotate('A_better')}
                                        loading={loading.annotation}
                                        icon={currentAnnotation?.comparison_result === 'A_better' ? <CheckOutlined /> : null}
                                        style={{
                                            backgroundColor: currentAnnotation?.comparison_result === 'A_better' ? '#ffeb3b' : '#fff',
                                            borderColor: '#ffeb3b',
                                            color: currentAnnotation?.comparison_result === 'A_better' ? '#000' : '#ffeb3b'
                                        }}
                                    >
                                        Path A 更好 (1)
                                    </Button>
                                    <Button
                                        type={currentAnnotation?.comparison_result === 'B_better' ? 'primary' : 'default'}
                                        block
                                        onClick={() => handleAnnotate('B_better')}
                                        loading={loading.annotation}
                                        icon={currentAnnotation?.comparison_result === 'B_better' ? <CheckOutlined /> : null}
                                        style={{
                                            backgroundColor: currentAnnotation?.comparison_result === 'B_better' ? '#2196f3' : '#fff',
                                            borderColor: '#2196f3',
                                            color: currentAnnotation?.comparison_result === 'B_better' ? '#fff' : '#2196f3'
                                        }}
                                    >
                                        Path B 更好 (2)
                                    </Button>
                                    <Button
                                        type={currentAnnotation?.comparison_result === 'indistinguishable' ? 'primary' : 'default'}
                                        block
                                        onClick={() => handleAnnotate('indistinguishable')}
                                        loading={loading.annotation}
                                        icon={currentAnnotation?.comparison_result === 'indistinguishable' ? <CheckOutlined /> : null}
                                        style={{
                                            backgroundColor: currentAnnotation?.comparison_result === 'indistinguishable' ? '#f44336' : '#fff',
                                            borderColor: '#f44336',
                                            color: currentAnnotation?.comparison_result === 'indistinguishable' ? '#fff' : '#f44336'
                                        }}
                                    >
                                        不能区分 (3)
                                    </Button>
                                </Space>
                            </div>

                            <div style={{ marginTop: '20px' }}>
                                <Button
                                    type="link"
                                    onClick={() => navigate(`/path-pair-statistics/${id}`)}
                                    style={{ padding: 0 }}
                                >
                                    查看标注统计
                                </Button>
                            </div>
                        </>
                    ) : (
                        <div style={{ textAlign: 'center', color: '#999', marginTop: '50px' }}>
                            <Text>选择PKL文件后开始标注</Text>
                        </div>
                    )}
                </div>
            </Sider>

            {/* 导出配置弹窗 */}
            <Modal
                title="导出标注结果"
                open={exportModalVisible}
                onOk={handleExport}
                onCancel={() => setExportModalVisible(false)}
                confirmLoading={exportLoading}
                okText="导出"
                cancelText="取消"
            >
                <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                        <Checkbox
                            checked={exportConfig.includeConflicts}
                            onChange={(e) =>
                                setExportConfig(prev => ({ ...prev, includeConflicts: e.target.checked }))
                            }
                        >
                            包含冲突详情
                        </Checkbox>
                        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                            勾选后将导出有冲突标注的详细信息（包含每个标注员的标注结果）
                        </div>
                    </div>

                    <Alert
                        message="导出说明"
                        description={
                            <ul style={{ margin: 0, paddingLeft: '16px' }}>
                                <li>将在新窗口中显示JSON格式的导出结果</li>
                                <li>相同标注结果会自动合并</li>
                                <li>冲突标注会标明每个标注员的结果</li>
                                <li>最终结果采用多数决定原则</li>
                            </ul>
                        }
                        type="info"
                        showIcon
                    />
                </Space>
            </Modal>

        </Layout>
    );
};

export default PathPairAnnotation;