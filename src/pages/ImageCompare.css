.compare-header {
    display: flex;
    align-items: center;
    padding: 0 24px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1;
}

.compare-header h3 {
    margin: 0;
    margin-left: 16px;
}

.compare-content {
    padding: 24px;
    background: #fff;
    width: 100%;
}

.search-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.pagination-info {
    color: #666;
}

.cases-list {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.case-row {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    overflow: hidden;
}

.case-header {
    padding: 12px 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.visualizers-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 0px;
    width: 100%;
}

.visualizer-container {
    border-right: 1px solid #e8e8e8;
    overflow: hidden;
    width: 100%;
}

.visualizer-container:last-child {
    border-right: none;
}

.visualizer-header {
    padding: 8px 12px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    flex-direction: column;
}

.pagination-container {
    margin-top: 24px;
    display: flex;
    justify-content: center;
}