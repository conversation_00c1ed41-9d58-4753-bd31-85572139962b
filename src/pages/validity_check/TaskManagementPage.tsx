import React, { useState, useEffect } from 'react';
import { DatePicker, Card, Table, Button, Modal, Form, Input, Select, Alert, Upload, message, Statistic, Slider, Row, Col, Progress, Tabs, Checkbox } from 'antd';
import { CalendarOutlined, UploadOutlined, FileExcelOutlined, WarningOutlined } from '@ant-design/icons';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import dayjs from 'dayjs';
const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;
interface PKL {
    id: number;
    pkl_name: string;
    pkl_dir: string;
    bag_name: string;
    vehicle_type: string;
    date: string;
    mode: string;
    version: string;
    scene_tag: string;
}

interface Task {
    id: number;
    task_name: string;
    description: string;
    status: string;
    creator_id: string;
    pkl_count: number;
    assigned_count: number;
    completed_count: number;
    created_at: string;
}
interface AnnotatorProgress {
    annotator_id: string;
    annotator_name: string;
    employee_id: string;
    tasks: Array<{
        task_id: number;
        task_name: string;
        total_assigned_pkls: number;
        completed_pkls: number;
        total_bag_groups: number;
        completed_bag_groups: number;
        progress_percentage: number;
        bag_groups: Array<{
            bag_name: string;
            total_pkls: number;
            completed_pkls: number;
            is_completed: boolean;
        }>;
    }>;
    daily_progress: Array<{
        date: string;
        completed_pkls: number;
    }>;
    total_assigned_pkls: number;
    total_completed_pkls: number;
    total_bag_groups: number;
    total_completed_bag_groups: number;
    overall_progress_percentage: number;
}
interface TaskProgress {
    task_id: number;
    task_name: string;
    total_pkls: number;
    assigned_pkls: number;
    completed_pkls: number;
    progress_percentage: number;
    annotator_progresses: Array<{
        annotator_id: string;
        assigned_pkls: number;
        completed_pkls: number;
        progress_percentage: number;
    }>;
}
interface TaskAssignmentStatus {
    task_id: number;
    total_pkls: number;
    assigned_pkls: number;
    unassigned_pkls: number;
    annotator_assignments: Array<{
        annotator_id: string;
        pkl_count: number;
    }>;
}
const ValidityCheckTaskManagementPage: React.FC = () => {
    const [tasks, setTasks] = useState<Task[]>([]);
    const [unassignedPkls, setUnassignedPkls] = useState<PKL[]>([]);
    const [taskProgresses, setTaskProgresses] = useState<TaskProgress[]>([]);
    const [loading, setLoading] = useState(false);
    const [uploadModalVisible, setUploadModalVisible] = useState(false);
    const [assignModalVisible, setAssignModalVisible] = useState(false);
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);
    const [form] = Form.useForm();
    const [assignForm] = Form.useForm();
    const { user, isAuthenticated } = useAuth();
    const [annotators, setAnnotators] = useState<{ id: number, username: string, employee_id: string }[]>([]);
    const [uploadResult, setUploadResult] = useState<{
        invalidRows: any[],
        invalidCount: number,
        validCount: number
    } | null>(null);
    const [showInvalidRowsModal, setShowInvalidRowsModal] = useState(false);

    const [taskAssignmentStatus, setTaskAssignmentStatus] = useState<TaskAssignmentStatus | null>(null);
    const [assignPklCount, setAssignPklCount] = useState<number | null>(null);
    const [assignAllUnassigned, setAssignAllUnassigned] = useState(true);

    const [annotatorProgresses, setAnnotatorProgresses] = useState<AnnotatorProgress[]>([]);
    const [progressDateRange, setProgressDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
        dayjs().subtract(7, 'day'),
        dayjs()
    ]);
    const [loadingAnnotatorProgress, setLoadingAnnotatorProgress] = useState(false);
    const loadAnnotatorProgresses = async () => {
        setLoadingAnnotatorProgress(true);
        try {
            const [startDate, endDate] = progressDateRange;
            const response = await axios.get('/api/validity-check/annotators/progress', {
                params: {
                    start_date: startDate.format('YYYY-MM-DD'),
                    end_date: endDate.format('YYYY-MM-DD')
                }
            });
            setAnnotatorProgresses(response.data.annotator_progresses || []);
        } catch (error) {
            message.error('加载标注员进度失败');
            console.error('加载标注员进度失败:', error);
        } finally {
            setLoadingAnnotatorProgress(false);
        }
    };
    const annotatorProgressColumns = [
        {
            title: '标注员',
            key: 'annotator',
            width: 150,
            render: (_, record: AnnotatorProgress) => (
                <div>
                    <div style={{ fontWeight: 'bold' }}>{record.annotator_name}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>({record.employee_id})</div>
                </div>
            )
        },
        {
            title: '总体进度',
            key: 'overall_progress',
            width: 200,
            render: (_, record: AnnotatorProgress) => (
                <div>
                    <div style={{ marginBottom: 4 }}>
                        PKL: {record.total_completed_pkls}/{record.total_assigned_pkls}
                    </div>
                    <div style={{ marginBottom: 4 }}>
                        BagGroup: {record.total_completed_bag_groups}/{record.total_bag_groups}
                    </div>
                    <Progress
                        percent={record.overall_progress_percentage}
                        size="small"
                        status={record.overall_progress_percentage === 100 ? 'success' : 'active'}
                    />
                </div>
            )
        },
        {
            title: '任务详情',
            key: 'tasks',
            render: (_, record: AnnotatorProgress) => (
                <div>
                    {record.tasks.map(task => (
                        <Card key={task.task_id} size="small" style={{ marginBottom: 8 }}>
                            <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: 8 }}>
                                {task.task_name}
                            </div>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <div style={{ fontSize: '12px' }}>
                                        PKL进度: {task.completed_pkls}/{task.total_assigned_pkls}
                                    </div>
                                    <Progress
                                        percent={task.progress_percentage}
                                        size="small"
                                        showInfo={false}
                                    />
                                </Col>
                                <Col span={12}>
                                    <div style={{ fontSize: '12px' }}>
                                        BagGroup: {task.completed_bag_groups}/{task.total_bag_groups}
                                    </div>
                                    <Progress
                                        percent={task.total_bag_groups > 0 ? Math.round((task.completed_bag_groups / task.total_bag_groups) * 100) : 0}
                                        size="small"
                                        showInfo={false}
                                        strokeColor="#52c41a"
                                    />
                                </Col>
                            </Row>
                        </Card>
                    ))}
                </div>
            )
        },
        {
            title: '每日进度',
            key: 'daily_progress',
            width: 250,
            render: (_, record: AnnotatorProgress) => (
                <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                    {record.daily_progress.length > 0 ? (
                        record.daily_progress.map(daily => (
                            <div key={daily.date} style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                padding: '2px 0',
                                borderBottom: '1px solid #f0f0f0'
                            }}>
                                <span style={{ fontSize: '12px' }}>{daily.date}</span>
                                <span style={{ fontSize: '12px', fontWeight: 'bold', color: '#1890ff' }}>
                                    {daily.completed_pkls} PKL
                                </span>
                            </div>
                        ))
                    ) : (
                        <div style={{ fontSize: '12px', color: '#999' }}>暂无数据</div>
                    )}
                </div>
            )
        }
    ];

    const loadTaskAssignmentStatus = async (taskId: number) => {
        try {
            const response = await axios.get(`/api/validity-check/tasks/${taskId}/assignment-status`);
            setTaskAssignmentStatus(response.data.data);
            // 初始化滑块值为所有未分配的PKL数量
            setAssignPklCount(response.data.data.unassigned_pkls);
        } catch (error) {
            message.error('获取任务分配状态失败');
            console.error('获取任务分配状态失败:', error);
        }
    };
    // 加载标注员
    const loadAnnotators = async () => {
        try {
            // 假设后端有该接口，返回所有标注员
            const res = await axios.get('/api/validity-check/users');
            setAnnotators(res.data.users || []);
        } catch (e) {
            message.error('加载标注员失败');
        }
    };
    // 加载数据
    const loadData = async () => {
        setLoading(true);
        try {
            const [tasksRes, pklsRes] = await Promise.all([
                axios.get('/api/validity-check/tasks'),
                axios.get('/api/validity-check/pkls/unassigned'),
            ]);

            setTasks(tasksRes.data.tasks);
            setUnassignedPkls(pklsRes.data.pkls);

            // 为每个活跃任务获取详细进度
            const activeTaskIds = tasksRes.data.tasks
                .filter((task: Task) => task.status === 'active')
                .map((task: Task) => task.id);

            if (activeTaskIds.length > 0) {
                const progressPromises = activeTaskIds.map(async (taskId: number) => {
                    try {
                        const response = await axios.get(`/api/validity-check/tasks/${taskId}/overall-progress`);
                        const progressData = response.data.data;
                        const task = tasksRes.data.tasks.find((t: Task) => t.id === taskId);

                        // 映射后端数据到前端接口格式
                        return {
                            task_id: taskId,
                            task_name: task?.task_name || '未知任务',
                            total_pkls: progressData.total_pkls || 0,
                            assigned_pkls: progressData.total_pkls || 0, // 后端没有这个字段，使用 total_pkls
                            completed_pkls: progressData.completed_pkls || 0,
                            progress_percentage: progressData.progress_percentage || 0,
                            annotator_progresses: [] // 后端没有返回这个数据，设为空数组
                        };
                    } catch (error) {
                        console.error(`获取任务 ${taskId} 进度失败:`, error);
                        return null;
                    }
                });

                const progressResults = await Promise.all(progressPromises);
                const validProgresses = progressResults.filter(p => p !== null);
                setTaskProgresses(validProgresses);
            } else {
                setTaskProgresses([]);
            }
        } catch (error) {
            message.error('加载数据失败');
            console.error('加载数据失败:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadData();
        loadAnnotators();
    }, []);
    useEffect(() => {
        // 当日期范围变化时自动查询
        loadAnnotatorProgresses();
    }, [progressDateRange]);
    const handleTabChange = (activeKey: string) => {
        if (activeKey === 'annotator-progress') {
            // 切换到标注人员进度时自动查询
            loadAnnotatorProgresses();
        }
    };
    const handleUploadFile = async (file: any) => {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await axios.post('/api/validity-check/pkls/upload', formData);
            const data = response.data;

            if (data.success) {
                let successMessage = `上传成功：${data.valid_count} 条有效记录`;

                // 显示任务分配信息
                if (data.task_assignments && Object.keys(data.task_assignments).length > 0) {
                    const taskCount = Object.keys(data.task_assignments).length;
                    successMessage += `，已自动分配到 ${taskCount} 个任务`;
                }

                message.success(successMessage);

                if (data.invalid_count > 0) {
                    setUploadResult({
                        invalidRows: data.invalid_rows || [],
                        invalidCount: data.invalid_count,
                        validCount: data.valid_count
                    });

                    Modal.info({
                        title: '上传结果',
                        content: (
                            <div>
                                <p>✅ 成功导入 {data.valid_count} 条记录</p>
                                <p>🔄 自动分配到 {Object.keys(data.task_assignments || {}).length} 个任务</p>
                                {data.invalid_count > 0 && (
                                    <p style={{ color: '#ff4d4f' }}>⚠️ {data.invalid_count} 条记录格式错误，已跳过</p>
                                )}
                                {data.invalid_count > 0 && (
                                    <Button
                                        type="link"
                                        onClick={() => setShowInvalidRowsModal(true)}
                                    >
                                        查看错误详情
                                    </Button>
                                )}
                            </div>
                        ),
                        width: 500,
                    });
                } else {
                    message.success(successMessage);
                }

                setUploadModalVisible(false);
                loadData();
            } else {
                message.error('上传失败');
            }
        } catch (error: any) {
            message.error(`上传失败: ${error.response?.data?.detail || error.message}`);
        }
    };

    // 渲染无效行详情
    const renderInvalidRowDetails = (row: any, index: number) => {
        return (
            <Card size="small" style={{ marginBottom: 8 }}>
                <div style={{ fontSize: '12px' }}>
                    <div><strong>行号:</strong> {index + 1}</div>
                    <div><strong>PKL路径:</strong> {row.pkl_path || '未提供'}</div>
                    <div style={{ color: '#ff4d4f' }}>
                        <strong>可能的问题:</strong>
                        <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
                            {!row.pkl_path && <li>缺少PKL路径</li>}
                            {row.pkl_path && !row.pkl_path.match(/[A-Z0-9]+_record_data_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}/) && (
                                <li>Bag名称格式不正确（应包含类似 XXX_record_data_2023_10_01_12_30_00 的格式）</li>
                            )}
                            {row.pkl_path && !row.pkl_path.match(/^ [A-Z]\d{2}/) && (
                                <li>车辆类型格式不正确（应以空格+大写字母+两位数字开头）</li>
                            )}
                        </ul>
                    </div>
                </div>
            </Card>
        );
    };

    // 分配任务
    const handleAssignTask = async (values: any) => {
        try {
            const requestData = {
                annotator_ids: values.annotator_ids,
                assigned_by: user?.id,
                pkl_count: assignAllUnassigned ? null : assignPklCount
            };

            await axios.post(`/api/validity-check/tasks/${selectedTask?.id}/assign-incremental`, requestData);

            message.success('任务分配成功');
            setAssignModalVisible(false);
            assignForm.resetFields();
            setSelectedTask(null);
            setTaskAssignmentStatus(null);
            setAssignPklCount(null);
            setAssignAllUnassigned(true);
            loadData();
        } catch (error) {
            message.error('任务分配失败');
        }
    };

    // 激活任务
    const handleActivateTask = async (taskId: number) => {
        try {
            await axios.post(`/api/validity-check/tasks/${taskId}/activate`);
            message.success('任务已激活');
            loadData();
        } catch (error) {
            message.error('任务激活失败');
        }
    };

    // 导出结果
    const handleExportResults = async (taskId: number) => {
        try {
            const response = await axios.get(`/api/validity-check/tasks/${taskId}/export`, {
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `validity_check_results_task_${taskId}.csv`);
            document.body.appendChild(link);
            link.click();
            link.remove();
        } catch (error) {
            message.error('导出失败');
        }
    };
    const handleExportAllResults = async (excludeValid: boolean = true) => {
        try {
            const response = await axios.get('/api/validity-check/export-all-results', {
                params: { exclude_valid: excludeValid },
                responseType: 'blob'
            });

            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:]/g, '-');
            link.setAttribute('download', `all_validity_check_results_${timestamp}.csv`);
            document.body.appendChild(link);
            link.click();
            link.remove();

            message.success('导出成功');
        } catch (error) {
            message.error('导出失败');
        }
    };

    // 显示导出选项Modal
    const [exportAllModalVisible, setExportAllModalVisible] = useState(false);
    const [excludeValidResults, setExcludeValidResults] = useState(true);

    const showExportAllModal = () => {
        setExportAllModalVisible(true);
    };

    const handleExportAllConfirm = () => {
        handleExportAllResults(excludeValidResults);
        setExportAllModalVisible(false);
    };

    const taskColumns = [
        {
            title: '任务名称',
            dataIndex: 'task_name',
            key: 'task_name',
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status: string) => {
                const statusMap = {
                    'draft': { text: '草稿', color: 'gray' },
                    'active': { text: '进行中', color: 'blue' },
                    'completed': { text: '已完成', color: 'green' }
                };
                const statusInfo = statusMap[status as keyof typeof statusMap];
                return <span style={{ color: statusInfo.color }}>{statusInfo.text}</span>;
            }
        },
        {
            title: '分配进度',
            key: 'assign_progress',
            width: 200,
            render: (_, record: Task) => {
                const assignProgress = record.pkl_count > 0 ? (record.assigned_count / record.pkl_count) * 100 : 0;
                return (
                    <div>
                        <Progress
                            percent={Math.round(assignProgress)}
                            size="small"
                            status={assignProgress === 100 ? 'success' : 'active'}
                            format={() => `${record.assigned_count}/${record.pkl_count}`}
                        />
                    </div>
                );
            }
        },
        {
            title: '完成进度',
            key: 'complete_progress',
            width: 200,
            render: (_, record: Task) => {
                const completeProgress = record.assigned_count > 0 ? (record.completed_count / record.assigned_count) * 100 : 0;
                return (
                    <div>
                        <Progress
                            percent={Math.round(completeProgress)}
                            size="small"
                            status={completeProgress === 100 ? 'success' : 'active'}
                            format={() => `${record.completed_count}/${record.assigned_count}`}
                            strokeColor={completeProgress === 100 ? '#52c41a' : '#1890ff'}
                        />
                    </div>
                );
            }
        },
        {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            render: (date: string) => new Date(date).toLocaleDateString()
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record: Task) => (
                <div>
                    {record.status === 'draft' && (
                        <Button
                            type="primary"
                            size="small"
                            onClick={() => handleActivateTask(record.id)}
                            style={{ marginRight: 8 }}
                        >
                            激活
                        </Button>
                    )}
                    <Button
                        size="small"
                        onClick={async () => {
                            setSelectedTask(record);
                            await loadTaskAssignmentStatus(record.id);
                            setAssignModalVisible(true);
                        }}
                        style={{ marginRight: 8 }}
                    >
                        分配
                    </Button>
                    <Button
                        size="small"
                        icon={<FileExcelOutlined />}
                        onClick={() => handleExportResults(record.id)}
                    >
                        导出
                    </Button>
                </div>
            ),
        },
    ];

    return (
        <div style={{ padding: 24 }}>
            <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="总任务数"
                            value={tasks.length}
                            valueStyle={{ color: '#3f8600' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="进行中任务"
                            value={tasks.filter(t => t.status === 'active').length}
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="未分配PKL"
                            value={unassignedPkls.length}
                            valueStyle={{ color: '#cf1322' }}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="已完成任务"
                            value={tasks.filter(t => t.status === 'completed').length}
                            valueStyle={{ color: '#52c41a' }}
                        />
                    </Card>
                </Col>
            </Row>

            <Tabs defaultActiveKey="tasks" onChange={handleTabChange}>
                <TabPane tab="任务管理" key="tasks">
                    <Card
                        title="任务列表"
                        extra={
                            <div>
                                <Button
                                    icon={<FileExcelOutlined />}
                                    onClick={showExportAllModal}
                                    style={{ marginRight: 8 }}
                                    type="default"
                                >
                                    导出所有结果
                                </Button>
                                <Button
                                    icon={<UploadOutlined />}
                                    onClick={() => setUploadModalVisible(true)}
                                >
                                    上传PKL
                                </Button>
                            </div>
                        }
                    >
                        <Table
                            columns={taskColumns}
                            dataSource={tasks}
                            rowKey="id"
                            loading={loading}
                        />
                    </Card>
                </TabPane>
                <TabPane tab="标注人员进度" key="annotator-progress">
                    <Card
                        title="标注人员进度统计"
                        extra={
                            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                                <span style={{ fontSize: '14px' }}>统计时间范围:</span>
                                <RangePicker
                                    value={progressDateRange}
                                    onChange={(dates) => {
                                        if (dates && dates[0] && dates[1]) {
                                            setProgressDateRange([dates[0], dates[1]]);
                                        }
                                    }}
                                    format="YYYY-MM-DD"
                                />
                                <Button
                                    type="primary"
                                    icon={<CalendarOutlined />}
                                    onClick={loadAnnotatorProgresses}
                                    loading={loadingAnnotatorProgress}
                                >
                                    查询
                                </Button>
                            </div>
                        }
                    >
                        {/* 统计概览 */}
                        <Row gutter={16} style={{ marginBottom: 24 }}>
                            <Col span={6}>
                                <Card size="small">
                                    <Statistic
                                        title="活跃标注员"
                                        value={annotatorProgresses.length}
                                        valueStyle={{ color: '#1890ff' }}
                                    />
                                </Card>
                            </Col>
                            <Col span={6}>
                                <Card size="small">
                                    <Statistic
                                        title="总分配PKL"
                                        value={annotatorProgresses.reduce((sum, ap) => sum + ap.total_assigned_pkls, 0)}
                                        valueStyle={{ color: '#722ed1' }}
                                    />
                                </Card>
                            </Col>
                            <Col span={6}>
                                <Card size="small">
                                    <Statistic
                                        title="总完成PKL"
                                        value={annotatorProgresses.reduce((sum, ap) => sum + ap.total_completed_pkls, 0)}
                                        valueStyle={{ color: '#52c41a' }}
                                    />
                                </Card>
                            </Col>
                            <Col span={6}>
                                <Card size="small">
                                    <Statistic
                                        title="期间日均完成"
                                        value={Math.round(
                                            annotatorProgresses.reduce((sum, ap) =>
                                                sum + ap.daily_progress.reduce((s, dp) => s + dp.completed_pkls, 0), 0
                                            ) / (progressDateRange[1].diff(progressDateRange[0], 'day') + 1)
                                        )}
                                        suffix="PKL/天"
                                        valueStyle={{ color: '#fa8c16' }}
                                    />
                                </Card>
                            </Col>
                        </Row>

                        <Table
                            columns={annotatorProgressColumns}
                            dataSource={annotatorProgresses}
                            rowKey="annotator_id"
                            loading={loadingAnnotatorProgress}
                            // expandable={{
                            //     expandedRowRender,
                            //     rowExpandable: (record) => record.tasks.some(task => task.bag_groups.length > 0)
                            // }}
                            pagination={{
                                pageSize: 10,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                showTotal: (total) => `共 ${total} 名标注员`
                            }}
                            scroll={{ x: 1200 }}
                        />
                    </Card>
                </TabPane>
            </Tabs>


            {/* 分配任务Modal */}
            <Modal
                title={`分配任务: ${selectedTask?.task_name}`}
                visible={assignModalVisible}
                onCancel={() => {
                    setAssignModalVisible(false);
                    assignForm.resetFields();
                    setSelectedTask(null);
                    setTaskAssignmentStatus(null);
                    setAssignPklCount(null);
                    setAssignAllUnassigned(true);
                }}
                footer={null}
                width={800}
            >
                {taskAssignmentStatus && (
                    <div style={{ marginBottom: 24 }}>
                        <Card size="small" title="当前分配状态">
                            <Row gutter={16}>
                                <Col span={8}>
                                    <Statistic
                                        title="总PKL数"
                                        value={taskAssignmentStatus.total_pkls}
                                        valueStyle={{ color: '#1890ff' }}
                                    />
                                </Col>
                                <Col span={8}>
                                    <Statistic
                                        title="已分配"
                                        value={taskAssignmentStatus.assigned_pkls}
                                        valueStyle={{ color: '#52c41a' }}
                                    />
                                </Col>
                                <Col span={8}>
                                    <Statistic
                                        title="未分配"
                                        value={taskAssignmentStatus.unassigned_pkls}
                                        valueStyle={{ color: '#ff4d4f' }}
                                    />
                                </Col>
                            </Row>

                            {taskAssignmentStatus.annotator_assignments.length > 0 && (
                                <div style={{ marginTop: 16 }}>
                                    <h4>当前标注员分配情况：</h4>
                                    {taskAssignmentStatus.annotator_assignments.map(assignment => (
                                        <div key={assignment.annotator_id} style={{ marginBottom: 4 }}>
                                            标注员 {assignment.annotator_id}: {assignment.pkl_count} 个PKL
                                        </div>
                                    ))}
                                </div>
                            )}
                        </Card>
                    </div>
                )}
                <Form form={assignForm} onFinish={handleAssignTask} layout="vertical">
                    <Form.Item
                        name="annotator_ids"
                        label="选择标注员"
                        rules={[{ required: true, message: '请选择标注员' }]}
                    >
                        <Select mode="multiple" placeholder="请选择标注员">
                            {annotators.map(a => (
                                <Option value={a.id} key={a.id}>
                                    {a.username}（{a.employee_id}）
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    {taskAssignmentStatus && taskAssignmentStatus.unassigned_pkls > 0 && (
                        <Form.Item label="分配数量设置">
                            <div>
                                <Checkbox
                                    checked={assignAllUnassigned}
                                    onChange={(e) => {
                                        setAssignAllUnassigned(e.target.checked);
                                        if (e.target.checked) {
                                            setAssignPklCount(taskAssignmentStatus.unassigned_pkls);
                                        }
                                    }}
                                >
                                    分配所有未分配的PKL ({taskAssignmentStatus.unassigned_pkls} 个)
                                </Checkbox>

                                {!assignAllUnassigned && (
                                    <div style={{ marginTop: 16 }}>
                                        <div style={{ marginBottom: 8 }}>
                                            分配数量: {assignPklCount} / {taskAssignmentStatus.unassigned_pkls}
                                        </div>
                                        <Slider
                                            min={1}
                                            max={taskAssignmentStatus.unassigned_pkls}
                                            value={assignPklCount || 1}
                                            onChange={(value) => setAssignPklCount(value)}
                                            marks={{
                                                1: '1',
                                                [Math.floor(taskAssignmentStatus.unassigned_pkls / 2)]: '一半',
                                                [taskAssignmentStatus.unassigned_pkls]: '全部'
                                            }}
                                        />
                                    </div>
                                )}
                            </div>
                        </Form.Item>
                    )}

                    {taskAssignmentStatus && taskAssignmentStatus.unassigned_pkls === 0 && (
                        <Alert
                            message="没有未分配的PKL"
                            description="当前任务中的所有PKL都已经分配给标注员了。"
                            type="info"
                            style={{ marginBottom: 16 }}
                        />
                    )}
                    <Form.Item>
                        <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
                            分配
                        </Button>
                        <Button onClick={() => {
                            setAssignModalVisible(false);
                            assignForm.resetFields();
                            setSelectedTask(null);
                        }}>
                            取消
                        </Button>
                    </Form.Item>
                </Form>
            </Modal>

            {/* 上传PKL Modal */}
            <Modal
                title="上传PKL文件"
                visible={uploadModalVisible}
                onCancel={() => setUploadModalVisible(false)}
                footer={null}
            >
                <Upload.Dragger
                    accept=".csv"
                    beforeUpload={handleUploadFile}
                    showUploadList={false}
                >
                    <p className="ant-upload-drag-icon">
                        <UploadOutlined />
                    </p>
                    <p className="ant-upload-text">点击或拖拽CSV文件到此区域上传</p>
                    <p className="ant-upload-hint">
                        支持CSV格式文件<br />
                        <small style={{ color: '#666' }}>
                            CSV文件应包含pkl_path列，系统会自动解析Bag名称、车辆类型等信息
                        </small>
                    </p>
                </Upload.Dragger>
            </Modal>

            {/* 无效行详情Modal */}
            <Modal
                title={
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <WarningOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                        CSV文件格式错误详情
                    </div>
                }
                visible={showInvalidRowsModal}
                onCancel={() => setShowInvalidRowsModal(false)}
                width={800}
                footer={[
                    <Button key="close" onClick={() => setShowInvalidRowsModal(false)}>
                        关闭
                    </Button>
                ]}
            >
                {uploadResult && (
                    <div>
                        <Alert
                            message="上传结果统计"
                            description={
                                <div>
                                    <p>✅ 成功导入: {uploadResult.validCount} 条记录</p>
                                    <p>❌ 格式错误: {uploadResult.invalidCount} 条记录</p>
                                </div>
                            }
                            type="info"
                            style={{ marginBottom: 16 }}
                        />

                        <div style={{ marginBottom: 16 }}>
                            <h4>数据格式要求说明：</h4>
                            <ul style={{ fontSize: '12px', color: '#666' }}>
                                <li>pkl_path字段必须提供</li>
                                <li>路径中必须包含Bag名称，格式如：XXX_record_data_2023_10_01_12_30_00</li>
                                <li>路径开头必须包含车辆类型，格式如：空格+大写字母+两位数字（如" A01"）</li>
                                <li>系统会从路径中自动提取日期、车辆类型等信息</li>
                            </ul>
                        </div>

                        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                            <h4>错误记录详情：</h4>
                            {uploadResult.invalidRows.map((row, index) =>
                                renderInvalidRowDetails(row, index)
                            )}
                        </div>
                    </div>
                )}
            </Modal>
            <Modal
                title="导出所有任务标注结果"
                visible={exportAllModalVisible}
                onOk={handleExportAllConfirm}
                onCancel={() => setExportAllModalVisible(false)}
                okText="确认导出"
                cancelText="取消"
            >
                <div>
                    <p>将导出所有任务的标注结果，包含以下信息：</p>
                    <ul style={{ paddingLeft: 20 }}>
                        <li>任务名称</li>
                        <li>PKL详细信息（名称、目录、Bag名称等）</li>
                        <li>标注结果和标注员信息</li>
                        <li>标注时间</li>
                    </ul>

                    <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f6f6f6', borderRadius: 4 }}>
                        <Checkbox
                            checked={excludeValidResults}
                            onChange={(e) => setExcludeValidResults(e.target.checked)}
                        >
                            排除 validity=valid 的结果（推荐）
                        </Checkbox>
                        <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                            勾选此选项将只导出有问题的标注结果，排除标记为"valid"的正常结果
                        </div>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default ValidityCheckTaskManagementPage;