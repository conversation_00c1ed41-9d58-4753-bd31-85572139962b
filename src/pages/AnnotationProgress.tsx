import React, { useState, useEffect } from 'react';
import {
    Card,
    Table,
    Progress,
    DatePicker,
    Select,
    Button,
    Row,
    Col,
    Statistic,
    Tag,
    Space,
    message,
    Empty,
    Tooltip
} from 'antd';
import {
    CalendarOutlined,
    UserOutlined,
    DatabaseOutlined,
    TrophyOutlined,
    ReloadOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import dayjs from 'dayjs';
import axios from 'axios';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface EvaluationSetProgress {
    evaluation_set_id: number;
    evaluation_set_name: string;
    total_pkls: number;
    annotated_pkls: number;
    progress_percentage: number;
    last_updated: string | null;
}

interface EmployeeProgress {
    employee_id: string;
    annotation_date: string;
    annotated_pkls_count: number;
}

interface AnnotationSummary {
    total_annotated_pkls: number;
    total_annotators: number;
    total_evaluation_sets: number;
    total_annotations: number;
}

interface AnnotationType {
    annotation: string;
    pkl_count: number;
    annotation_count: number;
}

interface RecentActivity {
    date: string;
    active_annotators: number;
    annotated_pkls: number;
}

const AnnotationProgress: React.FC = () => {
    const [evaluationSetsProgress, setEvaluationSetsProgress] = useState<EvaluationSetProgress[]>([]);
    const [employeeProgress, setEmployeeProgress] = useState<EmployeeProgress[]>([]);
    const [summary, setSummary] = useState<AnnotationSummary | null>(null);
    const [annotationTypes, setAnnotationTypes] = useState<AnnotationType[]>([]);
    const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
    const [loading, setLoading] = useState(false);
    const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
    const [selectedEmployee, setSelectedEmployee] = useState<string | undefined>(undefined);
    const [selectedEvaluationSet, setSelectedEvaluationSet] = useState<number | undefined>(undefined);
    const [employeeList, setEmployeeList] = useState<string[]>([]);
    const [evaluationSetList, setEvaluationSetList] = useState<{ id: number, name: string }[]>([]);

    // 获取标注进展数据
    const fetchAnnotationProgress = async () => {
        setLoading(true);
        try {
            const params: any = {};
            if (dateRange) {
                params.start_date = dateRange[0].format('YYYY-MM-DD');
                params.end_date = dateRange[1].format('YYYY-MM-DD');
            }
            if (selectedEmployee) {
                params.employee_id = selectedEmployee;
            }
            if (selectedEvaluationSet) {
                params.evaluation_set_id = selectedEvaluationSet;
            }

            const response = await axios.get('/api/annotation/progress', { params });
            if (response.data.success) {
                setEvaluationSetsProgress(response.data.evaluation_sets_progress);
                setEmployeeProgress(response.data.employee_daily_progress);

                // 提取员工列表和评测集列表
                const employees = [...new Set(response.data.employee_daily_progress.map((item: EmployeeProgress) => item.employee_id))].sort() as string[];
                setEmployeeList(employees);

                const evalSets = response.data.evaluation_sets_progress.map((item: EvaluationSetProgress) => ({
                    id: item.evaluation_set_id,
                    name: item.evaluation_set_name
                }));
                setEvaluationSetList(evalSets);
            }
        } catch (error) {
            console.error('获取标注进展数据失败:', error);
            message.error('获取标注进展数据失败');
        } finally {
            setLoading(false);
        }
    };

    // 获取标注概要统计
    const fetchAnnotationSummary = async () => {
        try {
            const response = await axios.get('/api/annotation/progress/summary');
            if (response.data.success) {
                setSummary(response.data.summary);
                setAnnotationTypes(response.data.annotation_types);
                setRecentActivity(response.data.recent_activity);
            }
        } catch (error) {
            console.error('获取标注概要统计失败:', error);
            message.error('获取标注概要统计失败');
        }
    };

    useEffect(() => {
        fetchAnnotationProgress();
        fetchAnnotationSummary();
    }, []);

    // 评测集进度表格列配置
    const evaluationSetColumns = [
        {
            title: '评测集ID',
            dataIndex: 'evaluation_set_id',
            key: 'evaluation_set_id',
            width: 100,
        },
        {
            title: '评测集名称',
            dataIndex: 'evaluation_set_name',
            key: 'evaluation_set_name',
            ellipsis: true,
        },
        {
            title: '总数据量',
            dataIndex: 'total_pkls',
            key: 'total_pkls',
            width: 100,
        },
        {
            title: '已标注数据',
            dataIndex: 'annotated_pkls',
            key: 'annotated_pkls',
            width: 100,
        },
        {
            title: '进度',
            key: 'progress',
            width: 200,
            render: (record: EvaluationSetProgress) => (
                <Progress
                    percent={record.progress_percentage}
                    format={(percent) => `${percent}%`}
                    status={record.progress_percentage === 100 ? 'success' : 'active'}
                />
            ),
        },
        {
            title: '最近更新',
            dataIndex: 'last_updated',
            key: 'last_updated',
            width: 150,
            render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-',
        },
    ];

    // 员工进度表格列配置
    const employeeColumns = [
        {
            title: '员工ID',
            dataIndex: 'employee_id',
            key: 'employee_id',
            width: 120,
        },
        {
            title: '标注日期',
            dataIndex: 'annotation_date',
            key: 'annotation_date',
            width: 120,
            render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
        },
        {
            title: '标注数据量',
            dataIndex: 'annotated_pkls_count',
            key: 'annotated_pkls_count',
            width: 120,
            render: (count: number) => (
                <Tag color={count > 50 ? 'green' : count > 20 ? 'orange' : 'default'}>
                    {count}
                </Tag>
            ),
        },
    ];

    // 准备员工每日标注趋势图数据
    const getEmployeeTrendOption = () => {
        // 按日期分组员工数据
        const groupedData: { [key: string]: { [key: string]: number } } = {};

        employeeProgress.forEach(item => {
            const date = item.annotation_date;
            if (!groupedData[date]) {
                groupedData[date] = {};
            }
            groupedData[date][item.employee_id] = item.annotated_pkls_count;
        });

        // 获取最近7天的数据
        const last7Days = Object.keys(groupedData).sort().slice(-7);
        const topEmployees = employeeList.slice(0, 5); // 只显示前5个员工

        const series = topEmployees.map(employee => ({
            name: employee,
            type: 'line',
            data: last7Days.map(date => groupedData[date][employee] || 0)
        }));

        return {
            title: {
                text: '员工标注趋势',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                top: 30,
                data: topEmployees
            },
            xAxis: {
                type: 'category',
                data: last7Days.map(date => dayjs(date).format('MM-DD'))
            },
            yAxis: {
                type: 'value',
                name: '标注数量'
            },
            series: series
        };
    };

    // 准备标注类型分布饼图数据
    const getAnnotationTypeOption = () => {
        const data = annotationTypes.map(item => ({
            name: item.annotation,
            value: item.pkl_count
        }));

        return {
            title: {
                text: '标注类型分布',
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                top: 'middle'
            },
            series: [
                {
                    name: '标注类型',
                    type: 'pie',
                    radius: '50%',
                    data: data,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
    };

    // 准备最近活跃度柱状图数据
    const getActivityOption = () => {
        const dates = recentActivity.map(item => dayjs(item.date).format('MM-DD'));
        const activeAnnotators = recentActivity.map(item => item.active_annotators);
        const annotatedPkls = recentActivity.map(item => item.annotated_pkls);

        return {
            title: {
                text: '最近7天活跃度',
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['活跃标注员', '标注数据量'],
                top: 30
            },
            xAxis: [
                {
                    type: 'category',
                    data: dates,
                    axisPointer: {
                        type: 'shadow'
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '人数',
                    position: 'left'
                },
                {
                    type: 'value',
                    name: '数据量',
                    position: 'right'
                }
            ],
            series: [
                {
                    name: '活跃标注员',
                    type: 'bar',
                    data: activeAnnotators
                },
                {
                    name: '标注数据量',
                    type: 'bar',
                    yAxisIndex: 1,
                    data: annotatedPkls
                }
            ]
        };
    };

    return (
        <div style={{ padding: '24px' }}>
            <h1>标注进展</h1>

            {/* 概要统计 */}
            {summary && (
                <Row gutter={16} style={{ marginBottom: '24px' }}>
                    <Col span={6}>
                        <Card>
                            <Statistic
                                title="总标注数据"
                                value={summary.total_annotated_pkls}
                                suffix="个PKL"
                                prefix={<DatabaseOutlined />}
                            />
                        </Card>
                    </Col>
                    <Col span={6}>
                        <Card>
                            <Statistic
                                title="标注员总数"
                                value={summary.total_annotators}
                                suffix="人"
                                prefix={<UserOutlined />}
                            />
                        </Card>
                    </Col>
                    <Col span={6}>
                        <Card>
                            <Statistic
                                title="评测集总数"
                                value={summary.total_evaluation_sets}
                                suffix="个"
                                prefix={<TrophyOutlined />}
                            />
                        </Card>
                    </Col>
                    <Col span={6}>
                        <Card>
                            <Statistic
                                title="总标注次数"
                                value={summary.total_annotations}
                                suffix="次"
                                prefix={<CalendarOutlined />}
                            />
                        </Card>
                    </Col>
                </Row>
            )}

            {/* 筛选器 */}
            <Card style={{ marginBottom: '24px' }}>
                <Space wrap>
                    <RangePicker
                        value={dateRange}
                        onChange={(dates) => {
                            if (dates && dates[0] && dates[1]) {
                                setDateRange([dates[0], dates[1]]);
                            } else {
                                setDateRange(null);
                            }
                        }}

                        placeholder={['开始日期', '结束日期']}
                    />
                    <Select
                        placeholder="选择员工"
                        value={selectedEmployee}
                        onChange={setSelectedEmployee}
                        allowClear
                        style={{ width: 150 }}
                    >
                        {employeeList.map(employee => (
                            <Option key={employee} value={employee}>{employee}</Option>
                        ))}
                    </Select>
                    <Select
                        placeholder="选择评测集"
                        value={selectedEvaluationSet}
                        onChange={setSelectedEvaluationSet}
                        allowClear
                        style={{ width: 200 }}
                    >
                        {evaluationSetList.map(evalSet => (
                            <Option key={evalSet.id} value={evalSet.id}>{evalSet.name}</Option>
                        ))}
                    </Select>
                    <Button
                        type="primary"
                        icon={<ReloadOutlined />}
                        onClick={fetchAnnotationProgress}
                        loading={loading}
                    >
                        刷新数据
                    </Button>
                </Space>
            </Card>

            <Row gutter={16}>
                {/* 评测集进度 */}
                <Col span={24} style={{ marginBottom: '24px' }}>
                    <Card title="评测集标注进度" loading={loading}>
                        <Table
                            dataSource={evaluationSetsProgress}
                            columns={evaluationSetColumns}
                            rowKey="evaluation_set_id"
                            pagination={{ pageSize: 10 }}
                            scroll={{ x: 800 }}
                        />
                    </Card>
                </Col>

                {/* 员工每日标注进度 */}
                <Col span={12}>
                    <Card title="员工每日标注进度" loading={loading}>
                        <Table
                            dataSource={employeeProgress}
                            columns={employeeColumns}
                            rowKey={(record) => `${record.employee_id}-${record.annotation_date}`}
                            pagination={{ pageSize: 10 }}
                            size="small"
                        />
                    </Card>
                </Col>

                {/* 员工标注趋势图 */}
                <Col span={12}>
                    <Card title="员工标注趋势">
                        {employeeProgress.length > 0 ? (
                            <ReactECharts
                                option={getEmployeeTrendOption()}
                                style={{ height: '300px' }}
                            />
                        ) : (
                            <Empty description="暂无数据" />
                        )}
                    </Card>
                </Col>

                {/* 标注类型分布 */}
                <Col span={12}>
                    <Card title="标注类型分布">
                        {annotationTypes.length > 0 ? (
                            <ReactECharts
                                option={getAnnotationTypeOption()}
                                style={{ height: '300px' }}
                            />
                        ) : (
                            <Empty description="暂无数据" />
                        )}
                    </Card>
                </Col>

                {/* 最近活跃度 */}
                <Col span={12}>
                    <Card title="最近活跃度">
                        {recentActivity.length > 0 ? (
                            <ReactECharts
                                option={getActivityOption()}
                                style={{ height: '300px' }}
                            />
                        ) : (
                            <Empty description="暂无数据" />
                        )}
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default AnnotationProgress;