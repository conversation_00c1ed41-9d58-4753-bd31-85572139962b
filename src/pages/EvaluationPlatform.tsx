import React, { useState } from 'react';
import { Layout, Divider } from 'antd';
import EvaluationSetManager from '../components/EvaluationSetManager';
import EvaluationCaselist from '../components/EvaluationCaseList';
import EvaluationSetCreator from '../components/EvaluationSetCreator';
import PickleVisualizer from '../components/PickleVisualizer';
import './EvaluationPlatform.css';
import { EvaluationCase } from '../types/index';
const { Sider } = Layout;


const EvaluationPlatform: React.FC = () => {
    // 选中的评测集，用于在右侧可视化
    const [selectedEvaluationSet, setSelectedEvaluationSet] = useState<EvaluationCase | undefined>(undefined);

    // 创建评测集模式
    const [createMode, setCreateMode] = useState<boolean>(false);

    // 处理从列表中选择评测集的回调
    const handleSelectEvaluationSet = (evaluationSet: EvaluationCase) => {
        setSelectedEvaluationSet(evaluationSet);
    };

    // 处理创建新评测集
    const handleCreateNewSet = () => {
        setCreateMode(true);
    };

    // 关闭创建评测集模式
    const handleCloseCreateMode = () => {
        setCreateMode(false);
    };

    // 创建评测集成功后的回调
    const handleCreateSuccess = () => {
        setCreateMode(false);
        // 可以选择刷新列表或进行其他操作
    };

    // 固定面板宽度
    const widths = {
        leftSider: "45%",
        middleSider: "30%",
        rightSider: "25%"
    };

    return (
        <Layout className="evaluation-platform-container">
            {/* 左侧评测集管理 */}
            <Sider
                width={widths.leftSider}
                theme="light"
                className="evaluation-platform-sider"
            >
                {createMode ? (
                    <EvaluationSetCreator
                        onClose={handleCloseCreateMode}
                        onSuccess={handleCreateSuccess}
                    />
                ) : (
                    <EvaluationSetManager
                        onCreateNewSet={handleCreateNewSet}
                        disabled={createMode}
                    />
                )}
            </Sider>

            {/* 中间评测集列表 */}
            <Sider
                width={widths.middleSider}
                theme="light"
                className="evaluation-platform-sider"
            >
                <EvaluationCaselist
                    onSelectEvaluationSet={handleSelectEvaluationSet}
                    creationMode={createMode}
                />
            </Sider>

            {/* 右侧面板 - 可视化器 */}
            <Divider type="vertical" className="platform-divider" />
            <Sider
                width={widths.rightSider}
                theme="light"
                className="evaluation-platform-sider right-panel"
            >
                <PickleVisualizer
                    evaluationCase={selectedEvaluationSet!}
                />
            </Sider>
        </Layout>
    );
};

export default EvaluationPlatform;