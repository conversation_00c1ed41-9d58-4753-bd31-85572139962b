// src/components/CreateEvaluationTask.tsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { Layout, Divider, Button, message } from 'antd';
import InferenceConfigForm from '../components/InferenceConfigForm';
import EvaluationSetManager from '../components/EvaluationSetManager';
import TaskProgressDrawer from '../components/TaskProgressDrawer';
import './CreateEvaluationTask.css';

const { Sider, Content } = Layout;


interface InferenceConfig {
    id: number;
    json_name: string;
    pth_name: string;
    pth_upload_time: string;
}

export function parseInferenceConfig(data: any[][]): InferenceConfig[] {
    return data.map(item => ({
        id: item[0],
        json_name: item[1],
        pth_name: item[2],
        pth_upload_time: item[3],
    }));
}

const CreateEvaluationTask: React.FC = () => {
    const navigate = useNavigate();

    // 状态管理
    const [configs, setConfigs] = useState<InferenceConfig[]>([]);
    const [selectedConfig, setSelectedConfig] = useState<number | null>(null);
    const [selectedSet, setSelectedSet] = useState<number | null>(null);
    const [loading, setLoading] = useState(false);
    const [configsLoading, setConfigsLoading] = useState(false);

    // 任务进度浮窗状态
    const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
    const [drawerVisible, setDrawerVisible] = useState(false);

    // 加载推理配置
    const fetchConfigs = async () => {
        setConfigsLoading(true);
        try {
            const response = await axios.get('/api/inference_config');
            const parsedConfigs = parseInferenceConfig(response.data);
            setConfigs(parsedConfigs);
        } catch (error) {
            console.error('Failed to fetch inference configs:', error);
            message.error('加载推理配置失败');
        } finally {
            setConfigsLoading(false);
        }
    };

    // 初始加载数据
    useEffect(() => {
        fetchConfigs();
    }, []);

    // 处理评测集选择
    const handleSelectSet = (set: any) => {
        setSelectedSet(set.id);
    };

    // 处理新配置创建成功
    const handleConfigCreated = (configId: number) => {
        fetchConfigs();
        setSelectedConfig(configId);
        message.success('推理配置上传成功！');
    };

    // 创建评测任务
    const handleCreateTask = async () => {
        if (!selectedConfig || !selectedSet) {
            message.error('请选择推理配置和评测集');
            return;
        }

        setLoading(true);
        try {
            const response = await axios.post('/api/evaluation_tasks', {
                inference_config_id: selectedConfig,
                evaluation_set_ids: [selectedSet]
            });

            if (response.data.success) {
                message.success('评测任务已提交！');

                // 设置当前任务ID并打开抽屉
                setCurrentTaskId(response.data.taskId);
                setDrawerVisible(true);
            } else {
                message.error(response.data.error || '创建任务失败');
            }
        } catch (error) {
            console.error('创建任务失败:', error);
            message.error('创建评测任务失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    // 处理任务完成
    const handleTaskCompleted = () => {
        message.success('评测任务已完成！');
        // 这里可以重置表单或刷新数据
    };

    // 关闭任务进度浮窗
    const closeTaskDrawer = () => {
        setDrawerVisible(false);
    };

    // 计算组件宽度
    const getComponentWidths = () => {
        return {
            leftSider: "50%",
            rightSider: "50%"
        };
    };

    const widths = getComponentWidths();

    // 创建新评测集（传递给 EvaluationSetManager 的回调）
    const handleCreateNewSet = () => {
        navigate('/evaluation-sets/create');
    };

    return (
        <Layout className="create-task-container">
            {/* 左侧评测集选择 - 使用 EvaluationSetManager */}
            <Content style={{ width: widths.leftSider }} className="sets-content">
                <CustomEvaluationSetManager
                    onCreateNewSet={handleCreateNewSet}
                    selectedSet={selectedSet}
                    onSelectSet={handleSelectSet}
                />
            </Content>

            {/* 右侧侧配置面板 */}
            <Sider
                width={widths.rightSider}
                theme="light"
                className="task-creation-sider"
            >
                <div className="sider-content">
                    <h2 className="panel-title">推理配置管理</h2>

                    {/* 上传新配置区域 */}
                    <div className="config-upload-area">
                        <h3>上传新配置</h3>
                        <InferenceConfigForm onConfigCreated={handleConfigCreated} />
                    </div>

                    <Divider />

                    {/* 选择配置区域 */}
                    <div className="config-selector">
                        <h3>选择推理配置</h3>
                        {configsLoading ? (
                            <div className="loading-indicator">加载中...</div>
                        ) : configs.length === 0 ? (
                                <div className="empty-message">暂无可用的推理配置</div>
                            ) : (
                                    <div className="config-list">
                                        {configs.map(config => (
                                            <div
                                                key={config.id}
                                                className={`config-item ${selectedConfig === config.id ? 'selected' : ''}`}
                                                onClick={() => setSelectedConfig(config.id)}
                                            >

                                                <div className="config-name">{config.json_name}</div>
                                                <div className="config-model">{config.pth_name}</div>
                                                <div className="config-time">
                                                    上传时间: {config.pth_upload_time}
                                                </div>
                                            </div>
                                        ))}
                            </div>
                        )}
                    </div>

                    {/* 创建任务按钮 */}
                    <div className="task-creation">
                        <div className="selection-summary">
                            已选择：{selectedConfig ? '1个配置' : '0个配置'} / {selectedSet ? '1个评测集' : '0个评测集'}
                        </div>
                        <Button
                            type="primary"
                            onClick={handleCreateTask}
                            loading={loading}
                            disabled={!selectedConfig || !selectedSet}
                            className="create-button"
                        >
                            创建评测任务
                        </Button>

                        {currentTaskId && (
                            <Button
                                type="link"
                                onClick={() => setDrawerVisible(true)}
                                style={{ marginLeft: 8 }}
                            >
                                查看当前任务进度
                            </Button>
                        )}
                    </div>
                </div>
            </Sider>

            {/* 任务进度浮窗 */}
            <TaskProgressDrawer
                taskId={currentTaskId}
                configId={selectedConfig}
                setIds={selectedSet ? [selectedSet] : []}
                visible={drawerVisible}
                onClose={closeTaskDrawer}
                onTaskCompleted={handleTaskCompleted}
            />
        </Layout>
    );
};

// 封装 EvaluationSetManager，添加选择功能
const CustomEvaluationSetManager: React.FC<{
    onCreateNewSet: () => void;
    selectedSet: number | null;
    onSelectSet: (set: any) => void;
}> = ({ onCreateNewSet, selectedSet, onSelectSet }) => {
    return (
        <div className="evaluation-set-selection">
            <h2 className="panel-title">选择评测集</h2>
            <EvaluationSetManager
                onCreateNewSet={onCreateNewSet}
                onRowSelect={onSelectSet}
                // 修改为单选ID
                selectedIds={selectedSet ? [selectedSet] : []}
                selectionMode={true}
                showCreateButton={false}
            />
        </div>
    );
};

export default CreateEvaluationTask;