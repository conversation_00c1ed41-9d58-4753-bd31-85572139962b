import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Layout, Card, Progress, Button, Row, Col, Statistic, Alert, Divider, Modal } from 'antd';
import { CheckCircleOutlined, SyncOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import './TaskProgressPage.css';

const { Content } = Layout;

interface TaskProgress {
    taskId: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    message: string;
    progress: number;
    inferenceProgress: number;
    evaluationProgress: number;
    totalSets: number;
    completedSets: number;
    failedSets: number;
    results: any[];
    error?: string;
}

const TaskProgressPage: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { taskId, configId, setIds } = location.state || {};

    const [taskProgress, setTaskProgress] = useState<TaskProgress>({
        taskId: taskId || '',
        status: 'pending',
        message: '正在初始化评测任务...',
        progress: 0,
        inferenceProgress: 0,
        evaluationProgress: 0,
        totalSets: setIds?.length || 0,
        completedSets: 0,
        failedSets: 0,
        results: []
    });

    const [polling, setPolling] = useState(true);

    // 轮询任务状态
    useEffect(() => {
        if (!taskId) {
            setTaskProgress(prev => ({
                ...prev,
                status: 'failed',
                message: '任务ID无效，无法获取进度'
            }));
            return;
        }

        const pollTaskStatus = async () => {
            try {
                const response = await axios.get(`/api/task_status/${taskId}`);
                const { status, progress, results, message, inferenceProgress, evaluationProgress } = response.data;

                // 计算完成和失败的评测集数量
                const completedSets = results?.filter(r => r.success && r.evaluation?.success)?.length || 0;
                const failedSets = results?.filter(r => r.success === false || (r.evaluation !== undefined && r.evaluation.success === false))?.length || 0;
                const totalSets = completedSets + failedSets;

                setTaskProgress({
                    taskId,
                    status,
                    message,
                    progress: progress || 0,
                    inferenceProgress: inferenceProgress || 0,
                    evaluationProgress: evaluationProgress || 0,
                    totalSets: totalSets,
                    completedSets,
                    failedSets,
                    results: results || []
                });

                // 如果任务完成或失败，停止轮询
                if (status === 'completed' || status === 'failed') {
                    setPolling(false);

                    // 如果任务完成，显示提示
                    if (status === 'completed') {
                        Modal.success({
                            title: '评测任务已完成',
                            content: '所有评测任务已处理完成，您可以查看详细结果。',
                            onOk: () => {
                                // 可以选择跳转到第一个评测集的详情页面
                                if (setIds && setIds.length > 0) {
                                    navigate(`/evaluation-sets/${setIds[0]}`);
                                }
                            }
                        });
                    }
                }
            } catch (error: any) {
                console.error('获取任务状态失败:', error);
                setTaskProgress(prev => ({
                    ...prev,
                    message: '获取任务进度失败',
                    error: error.toString()
                }));
            }
        };

        // 初始查询
        pollTaskStatus();

        // 设置轮询间隔
        const intervalId = setInterval(pollTaskStatus, 3000);

        // 清理函数
        return () => {
            clearInterval(intervalId);
        };
    }, [taskId, setIds, navigate]);

    const handleViewResults = () => {
        if (setIds && setIds.length > 0) {
            navigate(`/evaluation-sets/${setIds[0]}`);
        }
    };

    const handleCancelTask = async () => {
        try {
            await axios.post(`/api/cancel_task/${taskId}`);
            setPolling(false);
            setTaskProgress(prev => ({
                ...prev,
                status: 'failed',
                message: '任务已取消'
            }));
            // message.success('任务已取消');
        } catch (error) {
            console.error('取消任务失败:', error);
            // message.error('取消任务失败');
        }
    };

    const getStatusIcon = () => {
        switch (taskProgress.status) {
            case 'completed':
                return <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />;
            case 'running':
                return <SyncOutlined spin style={{ fontSize: 48, color: '#1890ff' }} />;
            case 'failed':
                return <ExclamationCircleOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />;
            default:
                return <SyncOutlined spin style={{ fontSize: 48, color: '#faad14' }} />;
        }
    };

    return (
        <Layout className="task-progress-container">
            <Content className="task-progress-content">
                <Card className="progress-card">
                    <div className="status-header">
                        {getStatusIcon()}
                        <h1 className="status-title">
                            评测任务进度
                            {taskProgress.status === 'running' && <span className="status-badge running">处理中</span>}
                            {taskProgress.status === 'completed' && <span className="status-badge completed">已完成</span>}
                            {taskProgress.status === 'failed' && <span className="status-badge failed">失败</span>}
                            {taskProgress.status === 'pending' && <span className="status-badge pending">等待中</span>}
                        </h1>
                    </div>

                    {taskProgress.error && (
                        <Alert
                            message="错误"
                            description={taskProgress.error}
                            type="error"
                            showIcon
                            className="error-alert"
                        />
                    )}

                    <div className="progress-message">{taskProgress.message}</div>

                    <div className="progress-section">
                        <h3>总体进度</h3>
                        <Progress
                            percent={taskProgress.progress}
                            status={taskProgress.status === 'failed' ? 'exception' : undefined}
                        />
                    </div>

                    <Divider />

                    <div className="progress-details">
                        <Row gutter={16}>
                            <Col span={8}>
                                <Card bordered={false} className="stat-card">
                                    <Statistic
                                        title="推理进度"
                                        value={taskProgress.inferenceProgress}
                                        suffix="%"
                                        precision={1}
                                    />
                                    <Progress
                                        percent={taskProgress.inferenceProgress}
                                        size="small"
                                        showInfo={false}
                                    />
                                </Card>
                            </Col>
                            <Col span={8}>
                                <Card bordered={false} className="stat-card">
                                    <Statistic
                                        title="评测进度"
                                        value={taskProgress.evaluationProgress}
                                        suffix="%"
                                        precision={1}
                                    />
                                    <Progress
                                        percent={taskProgress.evaluationProgress}
                                        size="small"
                                        showInfo={false}
                                    />
                                </Card>
                            </Col>
                            <Col span={8}>
                                <Card bordered={false} className="stat-card">
                                    <Statistic
                                        title="评测集"
                                        value={`${taskProgress.completedSets}/${taskProgress.totalSets}`}
                                        valueStyle={{ color: '#3f8600' }}
                                    />
                                    {taskProgress.failedSets > 0 && (
                                        <div className="error-stat">失败: {taskProgress.failedSets}</div>
                                    )}
                                </Card>
                            </Col>
                        </Row>
                    </div>

                    <div className="action-buttons">
                        <Button
                            type="primary"
                            onClick={handleViewResults}
                            disabled={taskProgress.status !== 'completed' || taskProgress.completedSets === 0}
                        >
                            查看评测结果
                        </Button>

                        {taskProgress.status === 'running' && (
                            <Button danger onClick={handleCancelTask}>
                                取消任务
                            </Button>
                        )}

                        <Button onClick={() => navigate('/create-task')}>
                            返回任务列表
                        </Button>
                    </div>
                </Card>
            </Content>
        </Layout>
    );
};

export default TaskProgressPage;