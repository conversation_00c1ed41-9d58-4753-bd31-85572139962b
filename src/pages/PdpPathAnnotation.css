.pdp-path-annotation-layout {
  height: calc(100vh - 50px);
  display: flex;
  width: 100%;
  overflow: hidden;
}
.lane-scene-annotation-layout {
  height: calc(100vh - 50px);
  display: flex;
  width: 100%;
  overflow: auto;
}
.pkl-list-sider {
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pkl-list-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.pkl-list-main {
  padding: 0 16px;
  height: calc(100vh - 485px);
  overflow: auto;
}
.bag-list-container {
  display: flex;
  flex-direction: column;
  height: 100%; /* 确保占满父容器 */
}

.bag-list-main {
  flex: 1; /* 列表内容占据剩余空间 */
  overflow-y: auto; /* 启用垂直滚动条 */
}

.bag-pagination {
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}
.bag-list-main::-webkit-scrollbar {
  width: 6px; /* Chrome, Safari */
}

.bag-list-main::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.bag-list-main::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
.user-input {
  margin-top: 12px;
}

.pkl-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.pkl-item:hover {
  background-color: #f5f5f5;
}

.pkl-item.selected {
  background-color: #e6f7ff;
}

.pkl-item-content {
  width: 100%;
}

.pkl-name {
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.bag-pkl-name {
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: auto;
  text-overflow: ellipsis;
}
.pkl-info {
  display: flex;
  gap: 4px;
}

.pkl-pagination {
  padding: 6px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
  display: flex;
  justify-content: center;
}

.visualization-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}
.visualization-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.lane-visualization-content {
  flex: 1;
  padding: 2px;
  display: flex;
  flex-direction: column;
}

.visualization-area {
  flex: 1;
  overflow: hidden;
}

.empty-visualization {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  flex-direction: column;
  gap: 16px;
}

.annotation-sider {
  border-left: 1px solid #f0f0f0;
  overflow: hidden;
}

.annotation-panel {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 82px);
}

.annotation-header {
  margin-bottom: 16px;
}

.empty-annotation, .loading-paths, .no-paths {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}

.paths-list {
  flex:1;
  overflow: auto;
  /* display: flex;
  flex-direction: column;
  gap: 12px;
  height: calc(100vh - 230px);
  overflow: auto; */
}

.path-card {
  background-color: #fafafa;
  transition: all 0.3s;
}

.path-card.highlighted {
  border-color: #1890ff;
  background-color: #e6f7ff;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.2);
}

.path-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-bottom: 12px; */
}

.path-title {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.annotation-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}

.path-actions .ant-radio-group label {
  border-start-start-radius: 0 !important;
  border-end-start-radius: 0 !important;
  border-start-end-radius: 0 !important;
  border-end-end-radius: 0 !important;
}

.good-selected {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
  color: white !important;
}

.bad-selected {
  background-color: #f5222d !important;
  border-color: #f5222d !important;
  color: white !important;
}

.annotation-info {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px dashed #f0f0f0;
}

.annotator {
  margin-bottom: 4px;
}

.comment-preview {
  background-color: #f9f9f9;
  padding: 4px 8px;
  border-radius: 4px;
}
/* ...existing styles... */

/* 添加标注统计信息的样式 */
.annotation-stats {
  margin-top: 8px;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  /* margin-bottom: 12px; */
}

.stats-item {
  margin-bottom: 4px;
}

.stats-summary {
  display: flex;
  gap: 8px;
  /* margin-top: 8px; */
}

.stats-summary .ant-tag {
  padding: 4px 8px;
  font-size: 14px;
  margin-right: 0;
}
/* 在PdpPathAnnotation.css中添加 */
.path-card-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.path-card-container .path-card {
  flex-grow: 1;
  /* margin-right: 10px; */
}
.path-card-container .path-card .ant-card-body{
  padding: 6px;
}

.path-card-container .path-actions {
  flex-shrink: 0;
}

/* 调整按钮组的垂直样式 */
.path-actions .ant-radio-group {
  display: flex;
  flex-direction: column;
}

.path-actions .ant-radio-button-wrapper {
  margin-bottom: 4px;
}

.bad-data-button-container {
  /* margin-bottom: 16px; */
}

.pkl-item .pkl-name .ant-tag {
  margin-left: 5px;
  font-size: 12px;
}
/* 可以添加到PdpPathAnnotation.css文件中 */
.visualization-area {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.e2e-image-container {
  text-align: center;
  background: #f5f5f5;
  /* padding: 10px; */
  /* border-radius: 4px;
   */
}

.e2e-image {
  max-width: 100%;
  max-height: 320px;
  object-fit: contain;
  /* border: 1px solid #d9d9d9; */
}

/* 搜索容器样式 */
.search-container {
margin-bottom: 12px;
}

.search-container .ant-input {
margin-bottom: 8px;
}

.search-container .ant-btn {
margin-right: 8px;
}

.search-container .ant-btn:last-child {
margin-right: 0;
}

/* 筛选控件样式 */
.filter-controls {
margin-bottom: 12px;
}

.filter-controls .ant-checkbox-wrapper {
margin-bottom: 8px;
}

.filter-controls .ant-radio-group {
width: 100%;
}

.filter-controls .ant-radio-button-wrapper {
flex: 1;
text-align: center;
}


/* 整个滚动条 */
::-webkit-scrollbar {
  width: 0px; 
  height: 8px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #f1f1f1; 
  border-radius: 4px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

/* 滚动条滑块悬停状态 */
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}