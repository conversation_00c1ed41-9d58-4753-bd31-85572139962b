.evaluation-set-detail {
    display: flex;
    width: 100%;
    height: 100vh; /* 使用视口高度确保充满屏幕 */
    overflow: hidden;
    max-width: 100%; /* 确保不超出父容器 */
}

.evaluation-results-sider {
    border-right: 1px solid #f0f0f0;
    overflow-y: auto;
}

.pickle-visualizer-content {
    flex: 1;
    overflow: auto;
    padding: 16px;
    background: #fff;
    min-width: 400px; /* 确保可视化区域有最小宽度 */
}

.config-selector-sider {
    /* border-left: 1px solid #f0f0f0; */
    overflow-y: auto;
}

/* 移除竖向分隔线，因为我们已经给侧边栏添加了边框 */
.divider {
    display: none;
}

.empty-visualizer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 16px;
}

/* 确保在小屏幕上也能正常显示 */
/* @media (max-width: 1200px) {
    .evaluation-set-detail {
        flex-direction: column;
        height: auto;
    }
    
    .evaluation-results-sider,
    .config-selector-sider {
        width: 100% !important;
        max-width: 100%;
    }
} */