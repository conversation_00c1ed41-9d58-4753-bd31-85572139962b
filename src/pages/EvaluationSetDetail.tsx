import React, { useState, useEffect } from 'react';
import { Layout, Spin, message, Divider } from 'antd';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import PickleVisualizer from '../components/PickleVisualizer';
import InferenceConfigSelector from '../components/InferenceConfigSelector';
import EvaluationResults from '../components/EvaluationResults';
import './EvaluationSetDetail.css';
import { EvaluationCase, InferenceConfig, EvaluationSet, EvaluationMetrics, PdpPathInfo } from '../types';
const { Sider, Content } = Layout;

export function parseInferenceConfig(data: any[][]): InferenceConfig[] {
    return data.map(item => ({
        id: item[0],
        json_name: item[1],
        pth_name: item[2],
        pth_upload_time: item[3],
    }));
}

const EvaluationSetDetail: React.FC = () => {
    // 获取URL参数中的评测集ID
    const { id } = useParams<{ id: string }>();

    // 状态管理
    const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(null);
    const [configs, setConfigs] = useState<InferenceConfig[]>([]);
    const [selectedConfig, setSelectedConfig] = useState<number | null>(null);
    const [selectedConfigs, setSelectedConfigs] = useState<number[]>([]); // 多选模式的选中配置
    const [selectedCase, setSelectedCase] = useState<EvaluationCase | null>(null);
    const [metrics, setMetrics] = useState<EvaluationMetrics | null>(null);
    const [caseMetrics, setCaseMetrics] = useState<EvaluationMetrics | null>(null); // 新增单个案例的评测指标状态
    const [loading, setLoading] = useState({
        set: true,
        configs: false,
        metrics: false,
        caseMetrics: false,
        trajectories: false // 添加轨迹数据加载状态
    });
    const [activeTab, setActiveTab] = useState<string>('top1'); // 评测集平均指标的tab
    const [activeCaseTab, setActiveCaseTab] = useState<string>('top1'); // 新增当前案例指标的tab
    const [pdpPaths, setPdpPaths] = useState<PdpPathInfo[]>([]); // 添加路径数据状态

    // 配置分页状态
    const [configPagination, setConfigPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    // 评测案例分页状态
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    // 添加评测集平均指标Tab切换处理函数
    const handleTabChange = (tab: string) => {
        setActiveTab(tab);
        // 如果有选中的配置，重新获取对应tab的评测集指标
        if (selectedConfig && id) {
            fetchMetrics(selectedConfig, id, tab);
        }
    };

    // 添加案例指标Tab切换处理函数
    const handleCaseTabChange = (tab: string) => {
        setActiveCaseTab(tab);
        // 如果有选中的案例和配置，重新获取对应tab的案例指标
        if (selectedCase && selectedConfig) {
            fetchCaseMetrics(selectedCase.id, selectedConfig, tab);
        }
    };

    // 添加评测案例分页处理函数
    const handlePageChange = async (page: number, perPage: number) => {
        setCurrentPage(page);
        setPageSize(perPage);
        setLoading(prev => ({ ...prev, set: true }));

        try {
            const response = await axios.get(`/api/evaluation_sets/${id}`, {
                params: {
                    page,
                    per_page: perPage
                }
            });

            if (response.data.success) {
                const evaluationSetData = {
                    ...response.data.evaluation_set,
                    cases: response.data.cases || [],
                    case_count: response.data.case_count || 0
                };
                setEvaluationSet(evaluationSetData);
                // 如果有案例且之前没有选中的案例，选择第一个
                if (response.data.cases && response.data.cases.length > 0) {
                    const caseToSelect = response.data.cases[0];
                    setSelectedCase(caseToSelect);
                    fetchTrajectories(caseToSelect.id, selectedConfig);
                }
            } else {
                message.error(response.data.error || '获取评测集详情失败');
            }
        } catch (error) {
            console.error('Failed to fetch evaluation set details:', error);
            message.error('获取评测集详情失败，请稍后再试');
        } finally {
            setLoading(prev => ({ ...prev, set: false }));
        }
    };

    // 处理配置分页
    const handleConfigPageChange = async (page: number, pageSize: number) => {
        setConfigPagination(prev => ({ ...prev, current: page, pageSize }));
        await fetchConfigs(page, pageSize);
    };

    // 加载推理配置列表
    const fetchConfigs = async (page = configPagination.current, pageSize = configPagination.pageSize) => {
        setLoading(prev => ({ ...prev, configs: true }));
        try {
            const response = await axios.get('/api/inference_config', {
                params: { page, per_page: pageSize }
            });
            const parsedConfigs = parseInferenceConfig(response.data.configs || response.data);
            setConfigs(parsedConfigs || []);

            // 如果API返回了总数，更新分页信息
            if (response.data.total !== undefined) {
                setConfigPagination(prev => ({ ...prev, total: response.data.total }));
            }
        } catch (error) {
            console.error('Failed to fetch inference configs:', error);
            message.error('获取推理配置失败，请稍后再试');
        } finally {
            setLoading(prev => ({ ...prev, configs: false }));
        }
    };

    // 获取轨迹数据的函数
    const fetchTrajectories = async (caseId: number, configId: number | null) => {
        if (!caseId || !configId) {
            setPdpPaths([]);
            return;
        }

        setLoading(prev => ({ ...prev, trajectories: true }));
        try {
            const response = await axios.get('/api/inference-result', {
                params: {
                    config_id: configId,
                    pkl_id: caseId
                }
            });

            if (response.data.success) {
                // 转换为PdpPathInfo格式
                const paths: PdpPathInfo[] = response.data.trajectories.map((traj: any, index: number) => ({
                    index: index,
                    probability: traj.probability || 0,
                    points_count: traj.points?.length || 0,
                    visualization_points: traj.points || [],
                    annotation: null
                }));
                setPdpPaths(paths);
            } else {
                console.warn('Failed to fetch trajectories:', response.data.error);
                setPdpPaths([]);
            }
        } catch (error) {
            console.error('Failed to fetch trajectories:', error);
            setPdpPaths([]);
        } finally {
            setLoading(prev => ({ ...prev, trajectories: false }));
        }
    };

    // 加载评测集详情
    useEffect(() => {
        const fetchEvaluationSet = async () => {
            setLoading(prev => ({ ...prev, set: true }));
            try {
                // 添加分页参数，默认使用第1页，每页10条数据
                const response = await axios.get(`/api/evaluation_sets/${id}`, {
                    params: {
                        page: currentPage,
                        per_page: pageSize
                    }
                });

                if (response.data.success) {
                    const evaluationSetData = {
                        ...response.data.evaluation_set,
                        cases: response.data.cases || [],
                        case_count: response.data.case_count || 0
                    };
                    setEvaluationSet(evaluationSetData);
                    // 默认选择第一个评测案例进行可视化
                    if (response.data.cases && response.data.cases.length > 0) {
                        const caseToSelect = response.data.cases[0];
                        setSelectedCase(caseToSelect);
                        // 如果已选择配置，获取轨迹数据
                        if (selectedConfig) {
                            fetchTrajectories(caseToSelect.id, selectedConfig);
                        }
                    }
                } else {
                    message.error(response.data.error || '获取评测集详情失败');
                }
            } catch (error) {
                console.error('Failed to fetch evaluation set details:', error);
                message.error('获取评测集详情失败，请稍后再试');
            } finally {
                setLoading(prev => ({ ...prev, set: false }));
            }
        };

        fetchEvaluationSet();
        fetchConfigs();
    }, [id, currentPage, pageSize]);

    // 当选择推理配置时，加载评测指标和轨迹
    useEffect(() => {
        if (!selectedConfig || !id) return;
        fetchMetrics(selectedConfig, id, activeTab);

        // 如果有选中的案例，加载轨迹
        if (selectedCase) {
            fetchTrajectories(selectedCase.id, selectedConfig);
        }
    }, [selectedConfig, id, activeTab]);

    // 获取评测指标的函数
    const fetchMetrics = async (configId: number, evalSetId: string, tab: string) => {
        setLoading(prev => ({ ...prev, metrics: true }));
        try {
            const response = await axios.get(`/api/evaluation_metrics`, {
                params: {
                    evaluation_set_id: evalSetId,
                    inference_config_id: configId,
                    top_k: tab.replace('top', '') // 提取数字: top1 -> 1, top3 -> 3, top6 -> 6
                }
            });

            if (response.data.success) {
                setMetrics(response.data.metrics);
            } else {
                message.error(response.data.error || '获取评测指标失败');
            }
        } catch (error) {
            console.error('Failed to fetch evaluation metrics:', error);
            message.error('获取评测指标失败，请稍后再试');
        } finally {
            setLoading(prev => ({ ...prev, metrics: false }));
        }
    };

    // 加载单个评测案例的指标
    const fetchCaseMetrics = async (caseId: number, configId: number, tab: string) => {
        if (!caseId || !configId) return;

        setLoading(prev => ({ ...prev, caseMetrics: true }));
        try {
            const response = await axios.get('/api/case_evaluation_metrics', {
                params: {
                    case_id: caseId,
                    inference_config_id: configId,
                    top_k: tab.replace('top', '') // 提取数字
                }
            });

            if (response.data.success) {
                setCaseMetrics(response.data.metrics);
            } else {
                console.warn('Failed to fetch case metrics:', response.data.error);
                setCaseMetrics(null);
            }
        } catch (error) {
            console.error('Failed to fetch case metrics:', error);
            setCaseMetrics(null);
        } finally {
            setLoading(prev => ({ ...prev, caseMetrics: false }));
        }
    };

    // 当选择案例或配置变化时，获取单个案例的指标
    useEffect(() => {
        if (selectedCase && selectedConfig) {
            fetchCaseMetrics(selectedCase.id, selectedConfig, activeCaseTab); // 使用案例指标的tab
        } else {
            setCaseMetrics(null);
        }
    }, [selectedCase, selectedConfig, activeCaseTab]);

    // 处理单个推理配置选择（保留向后兼容）
    const handleConfigSelect = (configId: number) => {
        setSelectedConfig(configId);
        // 如果有选中的案例，立即获取该案例在新配置下的指标和轨迹
        if (selectedCase) {
            fetchCaseMetrics(selectedCase.id, configId, activeCaseTab); // 使用案例指标的tab
            fetchTrajectories(selectedCase.id, configId);
        }
    };

    // 处理多个推理配置选择
    const handleConfigsSelect = (configIds: number[]) => {
        setSelectedConfigs(configIds);
        // 如果有选中项，设置第一个为活动配置以便显示
        if (configIds.length > 0) {
            setSelectedConfig(configIds[0]);
            // 如果有选中案例，获取新配置的轨迹
            if (selectedCase) {
                fetchTrajectories(selectedCase.id, configIds[0]);
            }
        } else {
            setSelectedConfig(null);
            setPdpPaths([]);
        }
    };

    // 处理评测案例选择
    const handleCaseSelect = (caseItem: EvaluationCase) => {
        setSelectedCase(caseItem);
        // 如果有选中的配置，立即获取该案例的指标和轨迹
        if (selectedConfig) {
            fetchCaseMetrics(caseItem.id, selectedConfig, activeCaseTab); // 使用案例指标的tab
            fetchTrajectories(caseItem.id, selectedConfig);
        }
    };

    if (loading.set) {
        return (
            <div className="loading-container">
                <Spin size="large" tip="加载评测集详情..." />
            </div>
        );
    }

    return (
        <Layout className="evaluation-set-detail">
            {/* 左侧评测结果面板 - 宽度使用百分比 */}
            <Layout.Sider
                width="40%"
                theme="light"
                className="evaluation-results-sider"
                style={{ minWidth: '280px' }}
            >
                <EvaluationResults
                    evaluationSet={evaluationSet}
                    metrics={metrics}
                    caseMetrics={caseMetrics} // 传递单个案例指标
                    loading={{
                        metrics: loading.metrics,
                        caseMetrics: loading.caseMetrics
                    }}
                    onCaseSelect={handleCaseSelect}
                    selectedCase={selectedCase}
                    onPageChange={handlePageChange}
                    currentPage={currentPage}
                    pageSize={pageSize}
                    totalCount={evaluationSet?.case_count}
                    activeTab={activeTab} // 传递当前活动标签
                    onTabChange={handleTabChange} // 传递标签切换处理函数
                    activeCaseTab={activeCaseTab} // 传递案例tab状态
                    onCaseTabChange={handleCaseTabChange} // 传递案例tab变更函数
                />
            </Layout.Sider>

            {/* 中间PickleVisualizer - 使用弹性布局 */}
            <Layout.Sider className="pickle-visualizer-content" width="30%" >
                {selectedCase ? (
                    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                        <PickleVisualizer
                            evaluationCase={selectedCase}
                            pdpPaths={pdpPaths} // 使用获取的pdpPaths数据
                        />
                        {loading.trajectories && (
                            <div style={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                background: 'rgba(0, 0, 0, 0.3)'
                            }}>
                                <Spin tip="加载轨迹数据..." />
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="empty-visualizer">
                        <p>请选择一个评测案例以查看可视化结果</p>
                    </div>
                )}
            </Layout.Sider>

            {/* 右侧推理配置选择 - 宽度使用百分比 */}
            <Layout.Sider
                width="30%"
                theme="light"
                className="config-selector-sider"
            >
                <InferenceConfigSelector
                    configs={configs}
                    selectedConfig={selectedConfig}
                    selectedConfigs={selectedConfigs}
                    onConfigSelect={handleConfigSelect}
                    onConfigsSelect={handleConfigsSelect}
                    loading={loading.configs}
                    mode="multiple"
                    pagination={{
                        current: configPagination.current,
                        pageSize: configPagination.pageSize,
                        total: configPagination.total,
                        onChange: handleConfigPageChange
                    }}
                    evaluationSetId={id} // 添加评测集ID
                />
            </Layout.Sider>
        </Layout>
    );
};

export default EvaluationSetDetail;