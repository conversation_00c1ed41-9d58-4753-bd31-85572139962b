/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useRef } from "react";
import {
  Layout,
  List,
  Card,
  Button,
  Upload,
  Space,
  message,
  Spin,
  Typography,
  Row,
  Col,
} from "antd";
import {
  UploadOutlined,
  FileTextOutlined,
  ClearOutlined,
} from "@ant-design/icons";
import axios from "axios";
import ResizableSider from "../components/ResizableSider";
import ReactECharts from "echarts-for-react";
import PickleVisualizer from "../components/PickleVisualizer";
import "./PdpPathAnnotation.css"; // 复用相同的样式
import {
  PdpPathInfo,
  DlpTrajectoryInfo,
} from "../types";

const { Text } = Typography;

interface PklFileInfo {
  path: string;
  name: string;
  index: number;
}

interface VisualizationData {
  paths?: PdpPathInfo[];
  trajs?: DlpTrajectoryInfo[];
  ego_traj_length?: number[];
  ego_traj_vel?: number[];
  is_dirty?: boolean;
  selected_object_ids?: string[];
}

const Visualization: React.FC = () => {
  // 添加 refs 用于手动触发图表更新
  const velocityChartRef = useRef<any>(null);
  const accelerationChartRef = useRef<any>(null);
  const distanceChartRef = useRef<any>(null);

  // 状态管理
  const [leftSiderWidth, setLeftSiderWidth] = useState(400);
  const [pklFiles, setPklFiles] = useState<PklFileInfo[]>([]);
  const [selectedPkl, setSelectedPkl] = useState<PklFileInfo | null>(null);
  const [pdpPaths, setPdpPaths] = useState<Record<number, PdpPathInfo>>({});
  const [dlpTrajs, setDlpTrajs] = useState<DlpTrajectoryInfo[]>([]);
  const [highlightTrajIndex, setHighlightTrajIndex] = useState<number | null>(null);
  const [highlightPathIndex, setHighlightPathIndex] = useState<number | null>(null);
  const [egoTrajLength, setEgoTrajLength] = useState<number[]>([]);
  const [egoTrajVel, setEgoTrajVel] = useState<number[]>([]);
  const [selectedObjectIds, setSelectedObjectIds] = useState<string[]>([]);

  const [loading, setLoading] = useState({
    upload: false,
    visualization: false,
    trajectories: false,
  });

  // 处理TXT文件上传
  const handleFileUpload = (file: File) => {
    setLoading((prev) => ({ ...prev, upload: true }));

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const lines = content.split('\n').filter(line => line.trim() !== '');

        const pklFileList: PklFileInfo[] = lines.map((line, index) => {
          const trimmedLine = line.trim();
          const fileName = trimmedLine.split('/').pop() || trimmedLine;
          return {
            path: trimmedLine,
            name: fileName,
            index: index,
          };
        });

        setPklFiles(pklFileList);
        setSelectedPkl(null);
        setPdpPaths({});
        setDlpTrajs([]);
        message.success(`成功加载 ${pklFileList.length} 个PKL文件路径`);
      } catch (error) {
        console.error('解析文件失败:', error);
        message.error('解析TXT文件失败');
      } finally {
        setLoading((prev) => ({ ...prev, upload: false }));
      }
    };

    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  // 清空文件列表
  const handleClearFiles = () => {
    setPklFiles([]);
    setSelectedPkl(null);
    setPdpPaths({});
    setDlpTrajs([]);
    setHighlightTrajIndex(null);
    setHighlightPathIndex(null);
    setEgoTrajLength([]);
    setEgoTrajVel([]);
    setSelectedObjectIds([]);
  };

  // 选择PKL文件并加载数据
  const handlePklSelect = async (pkl: PklFileInfo) => {
    setSelectedPkl(pkl);
    setHighlightTrajIndex(null);
    setHighlightPathIndex(0);
    setEgoTrajLength([]);
    setEgoTrajVel([]);
    setSelectedObjectIds([]);
    setLoading((prev) => ({ ...prev, trajectories: true }));

    try {
      // 调用API直接读取PKL文件
      const response = await axios.post('/api/visualization/load-pkl', {
        pkl_path: pkl.path,
      });

      if (response.data.success) {
        const data: VisualizationData = response.data;

        // 处理路径数据
        if (data.paths && data.paths.length > 0) {
          const pathsDict: Record<number, PdpPathInfo> = {};
          data.paths.forEach((path: PdpPathInfo) => {
            pathsDict[path.index] = path;
          });
          setPdpPaths(pathsDict);
        } else {
          setPdpPaths({});
        }

        // 处理轨迹数据
        const trajectories: DlpTrajectoryInfo[] = data.trajs || [];
        setDlpTrajs(trajectories);

        // 设置ego轨迹数据
        if (data.ego_traj_length) {
          setEgoTrajLength(data.ego_traj_length);
        }
        if (data.ego_traj_vel) {
          setEgoTrajVel(data.ego_traj_vel);
        }

        // 设置选中的对象ID
        if (data.selected_object_ids) {
          setSelectedObjectIds(data.selected_object_ids);
        } else {
          setSelectedObjectIds([]);
        }

        message.success('PKL数据加载成功');
      } else {
        message.error(response.data.error || '加载PKL数据失败');
      }
    } catch (error) {
      console.error('加载PKL数据失败:', error);
      message.error('加载PKL数据失败');
    } finally {
      setLoading((prev) => ({ ...prev, trajectories: false }));
    }
  };

  // 高亮某条轨迹
  const handleHighlightTraj = (trajIndex: number) => {
    setHighlightTrajIndex(trajIndex === highlightTrajIndex ? null : trajIndex);
  };

  // 图表颜色配置
  const chartColors = [
    "#FF6B6B",
    "#4ECDC4",
    "#45B7D1",
    "#96CEB4",
    "#FFEAA7",
    "#DDA0DD",
    "#98D8C8",
    "#F7DC6F",
    "#BB8FCE",
    "#85C1E9",
    "#F8C471",
    "#82E0AA",
    "#F1948A",
    "#85C1E9",
    "#F8C471",
    "#82E0AA",
    "#F1948A",
    "#85C1E9",
    "#F8C471",
    "#82E0AA",
    "#EF476F",
    "#FFD23F",
    "#06FFA5",
    "#118AB2",
    "#06D6A0",
  ];

  // 生成速度曲线图配置
  const getVelocityChartOption = () => {
    if (!dlpTrajs || dlpTrajs.length === 0) {
      return {
        title: { text: "速度曲线", left: "center" },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value", name: "速度 (m/s)" },
        series: [],
      };
    }

    const maxLength = Math.max(...dlpTrajs.map((traj) => traj.vel.length));
    const xAxisData = Array.from({ length: maxLength }, (_, i) => i.toString());

    const series = dlpTrajs.map((traj, index) => ({
      name: `轨迹 ${traj.index}`,
      type: "line",
      data: traj.vel,
      lineStyle: {
        color: chartColors[index % chartColors.length],
        width: highlightTrajIndex === traj.index ? 3 : 1,
      },
      emphasis: {
        lineStyle: {
          width: 3,
        },
      },
    }));

    return {
      title: { text: "速度曲线", left: "center" },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
        },
      },
      legend: {
        data: dlpTrajs.map((traj) => `轨迹 ${traj.index}`),
        bottom: 0,
        type: "scroll",
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: xAxisData,
        name: "时间步",
      },
      yAxis: {
        type: "value",
        name: "速度 (m/s)",
      },
      series,
    };
  };

  // 生成加速度曲线图配置
  const getAccelerationChartOption = () => {
    if (!dlpTrajs || dlpTrajs.length === 0) {
      return {
        title: { text: "加速度曲线", left: "center" },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value", name: "加速度 (m/s²)" },
        series: [],
      };
    }

    const maxLength = Math.max(...dlpTrajs.map((traj) => traj.acc.length));
    const xAxisData = Array.from({ length: maxLength }, (_, i) => i.toString());

    const series = dlpTrajs.map((traj, index) => ({
      name: `轨迹 ${traj.index}`,
      type: "line",
      data: traj.acc,
      lineStyle: {
        color: chartColors[index % chartColors.length],
        width: highlightTrajIndex === traj.index ? 3 : 1,
      },
      emphasis: {
        lineStyle: {
          width: 3,
        },
      },
    }));

    return {
      title: { text: "加速度曲线", left: "center" },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
        },
      },
      legend: {
        data: dlpTrajs.map((traj) => `轨迹 ${traj.index}`),
        bottom: 0,
        type: "scroll",
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: xAxisData,
        name: "时间步",
      },
      yAxis: {
        type: "value",
        name: "加速度 (m/s²)",
      },
      series,
    };
  };

  // 生成距离曲线图配置
  const getDistanceChartOption = () => {
    if (!dlpTrajs || dlpTrajs.length === 0) {
      return {
        title: { text: "距离曲线", left: "center" },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value", name: "距离 (m)" },
        series: [],
      };
    }

    const maxLength = Math.max(...dlpTrajs.map((traj) => traj.s.length));
    const xAxisData = Array.from({ length: maxLength }, (_, i) => i.toString());

    const series = dlpTrajs.map((traj, index) => ({
      name: `轨迹 ${traj.index}`,
      type: "line",
      data: traj.s,
      lineStyle: {
        color: chartColors[index % chartColors.length],
        width: highlightTrajIndex === traj.index ? 3 : 1,
      },
      emphasis: {
        lineStyle: {
          width: 3,
        },
      },
    }));

    return {
      title: { text: "距离曲线", left: "center" },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
        },
      },
      legend: {
        data: dlpTrajs.map((traj) => `轨迹 ${traj.index}`),
        bottom: 0,
        type: "scroll",
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: xAxisData,
        name: "时间步",
      },
      yAxis: {
        type: "value",
        name: "距离 (m)",
      },
      series,
    };
  };

  return (
    <div
      className="pdp-path-annotation-layout"
      style={{ display: "flex", height: "calc(100vh - 50px)" }}
    >      {/* 左侧PKL文件列表面板 */}
      <ResizableSider
        width={leftSiderWidth}
        minWidth={300}
        maxWidth={600}
        onResize={setLeftSiderWidth}
        position="left"
        className="pkl-list-sider"
      >
        <div className="pkl-list-container">
          <div className="pkl-list-header">
            <Space direction="vertical" style={{ width: "100%" }}>
              <div style={{ textAlign: "center" }}>
                <Text strong>PKL文件可视化</Text>
              </div>

              <Upload
                accept=".txt"
                beforeUpload={handleFileUpload}
                showUploadList={false}
                disabled={loading.upload}
              >
                <Button
                  icon={<UploadOutlined />}
                  loading={loading.upload}
                  block
                >
                  上传TXT文件
                </Button>
              </Upload>

              {pklFiles.length > 0 && (
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClearFiles}
                  block
                  type="dashed"
                >
                  清空列表
                </Button>
              )}

              <div style={{ fontSize: "12px", color: "#666", textAlign: "center" }}>
                {pklFiles.length > 0 ? `已加载 ${pklFiles.length} 个PKL文件` : "请上传包含PKL路径的TXT文件"}
              </div>
            </Space>
          </div>

          <div className="pkl-list-content">
            {loading.upload ? (
              <div style={{ textAlign: "center", padding: "20px" }}>
                <Spin tip="解析文件中..." />
              </div>
            ) : pklFiles.length === 0 ? (
              <div style={{ textAlign: "center", padding: "20px", color: "#999" }}>
                <FileTextOutlined style={{ fontSize: "48px", marginBottom: "16px" }} />
                <div>暂无PKL文件</div>
                <div style={{ fontSize: "12px", marginTop: "8px" }}>
                  请上传包含PKL文件路径的TXT文件
                </div>
              </div>
            ) : (
              <List
                dataSource={pklFiles}
                renderItem={(item) => (
                  <List.Item style={{ padding: "8px 16px" }}>
                    <Card
                      size="small"
                      hoverable
                      onClick={() => handlePklSelect(item)}
                      style={{
                        width: "100%",
                        cursor: "pointer",
                        border: selectedPkl?.index === item.index ? "2px solid #1890ff" : "1px solid #d9d9d9",
                        backgroundColor: selectedPkl?.index === item.index ? "#f6ffed" : "#fff",
                      }}
                    >
                      <div>
                        <Text strong style={{ fontSize: "12px" }}>
                          {item.name}
                        </Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: "10px" }}>
                          {item.path}
                        </Text>
                      </div>
                    </Card>
                  </List.Item>
                )}
              />
            )}
          </div>
        </div>
      </ResizableSider>

      {/* 中间可视化区域 */}
      <div
        className="visualization-content"
        style={{
          flex: 1,
          background: "#fff",
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {selectedPkl ? (
          <div style={{ width: "100%", height: "100%", padding: "2px" }}>
            {loading.trajectories ? (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <Spin tip="加载PKL数据..." />
              </div>
            ) : dlpTrajs.length === 0 ? (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                  color: "#999",
                }}
              >
                <Text>未找到轨迹数据</Text>
              </div>
            ) : (
              <>
                {/* 三个图表 */}
                <Row gutter={[16, 16]} style={{ height: "30%" }}>
                  <Col span={8} style={{ height: "100%" }}>
                    <Card
                      size="small"
                      style={{ height: "100%" }}
                      styles={{
                        body: {
                          height: "calc(100% - 20px)",
                          padding: "2px",
                        },
                      }}
                    >
                      <ReactECharts
                        ref={velocityChartRef}
                        option={getVelocityChartOption()}
                        style={{ height: "100%", width: "100%" }}
                      />
                    </Card>
                  </Col>
                  <Col span={8} style={{ height: "100%" }}>
                    <Card
                      size="small"
                      style={{ height: "100%" }}
                      styles={{
                        body: {
                          height: "calc(100% - 20px)",
                          padding: "2px",
                        },
                      }}
                    >
                      <ReactECharts
                        option={getAccelerationChartOption()}
                        style={{ height: "100%", width: "100%" }}
                      />
                    </Card>
                  </Col>
                  <Col span={8} style={{ height: "100%" }}>
                    <Card
                      size="small"
                      style={{ height: "100%" }}
                      styles={{
                        body: {
                          height: "calc(100% - 20px)",
                          padding: "2px",
                        },
                      }}
                    >
                      <ReactECharts
                        ref={distanceChartRef}
                        option={getDistanceChartOption()}
                        style={{ height: "100%", width: "100%" }}
                      />
                    </Card>
                  </Col>
                </Row>

                {/* PickleVisualizer */}
                <div style={{ height: "70%", marginTop: "1px" }}>
                  <PickleVisualizer
                    evaluationCase={{
                      id: selectedPkl.index,
                      pkl_name: selectedPkl.name,
                      pkl_dir: "",
                      key_obs_id: 0,
                      path_range: [],
                      dirty_data: false,
                    }}
                    highlightPathIndex={highlightPathIndex}
                    pdpPaths={pdpPaths}
                    dlpTrajs={dlpTrajs}
                    highlightTrajIndex={highlightTrajIndex}
                    height="55vh"
                    selectedObjectIds={selectedObjectIds}
                  />
                </div>
              </>
            )}
          </div>
        ) : (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
              color: "#999",
            }}
          >
            <Text>请从左侧选择一个PKL文件</Text>
          </div>
        )}
      </div>
    </div>
  );
};

export default Visualization;