import React, { useState, useEffect } from "react";
import {
  Avatar,
  Card,
  Button,
  Table,
  Space,
  message,
  Modal,
  Form,
  Input,
  Upload,
  Pagination,
  Popconfirm,
  Tag,
  Tooltip,
  Typography,
  Row,
  Col,
  Statistic,
  Drawer,
  Spin,
  Select,
  Radio,
} from "antd";
import {
  PlusOutlined,
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  FolderOutlined,
  FileTextOutlined,
  InboxOutlined,
  EditOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import type { UploadProps } from "antd";
import axios from "axios";
import { BagSet, BagSetDetail } from "../types";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { useRequest } from "ahooks";

const { Title, Text } = Typography;
const { Dragger } = Upload;
const { Option } = Select;

const BagSetManagement: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [bagSets, setBagSets] = useState<BagSet[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState("");

  // 模态框状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedBagSet, setSelectedBagSet] = useState<BagSet | null>(null);
  const [bagSetDetail, setBagSetDetail] = useState<BagSetDetail | null>(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  // 在其他useState和useForm之后添加
  const [uploadForm] = Form.useForm();

  // 表单
  const [createForm] = Form.useForm();

  // 抽屉状态
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedBagSetForDrawer, setSelectedBagSetForDrawer] =
    useState<BagSet | null>(null);

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
    setSelectedBagSetForDrawer(null);
  };

  // 加载bag集列表
  const loadBagSets = async (page = 1, pageSize = 10, search = "") => {
    setLoading(true);
    try {
      const response = await axios.get("/api/bag-sets", {
        params: {
          page,
          per_page: pageSize,
          search,
        },
      });

      if (response.data.success) {
        setBagSets(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.per_page,
          total: response.data.total,
        });
      } else {
        message.error("加载bag集列表失败");
      }
    } catch (error) {
      console.error("Failed to load bag sets:", error);
      message.error("加载bag集列表出错");
    } finally {
      setLoading(false);
    }
  };

  // 创建bag集
  const handleCreateBagSet = async (values: any) => {
    try {
      const response = await axios.post("/api/bag-sets", {
        set_name: values.set_name,
        creator_name: user?.username || "unknown",
        description: values.description,
      });

      if (response.data.success) {
        message.success("bag集创建成功");
        setCreateModalVisible(false);
        createForm.resetFields();
        loadBagSets(pagination.current, pagination.pageSize, searchKeyword);
      } else {
        message.error(response.data.message || "创建失败");
      }
    } catch (error: any) {
      console.error("Failed to create bag set:", error);
      message.error(error.response?.data?.detail || "创建bag集失败");
    }
  };

  // 删除bag集
  const handleDeleteBagSet = async (bagSetId: number) => {
    try {
      const response = await axios.delete(`/api/bag-sets/${bagSetId}`);
      if (response.data.success) {
        message.success("bag集删除成功");
        loadBagSets(pagination.current, pagination.pageSize, searchKeyword);
      } else {
        message.error("删除失败");
      }
    } catch (error: any) {
      console.error("Failed to delete bag set:", error);
      message.error(error.response?.data?.detail || "删除bag集失败");
    }
  };

  // 查看bag集详情
  const handleViewDetail = async (bagSet: BagSet) => {
    setSelectedBagSet(bagSet);
    setDetailModalVisible(true);

    try {
      const response = await axios.get(`/api/bag-sets/${bagSet.id}`);
      if (response.data.success) {
        setBagSetDetail(response.data);
      } else {
        message.error("加载详情失败");
      }
    } catch (error) {
      console.error("Failed to load bag set detail:", error);
      message.error("加载详情出错");
    }
  };

  // 跳转到标注页面
  const handleAnnotate = (bagSet: BagSet) => {
    navigate(`/pdp-lane-scene-annotation/bag-set/${bagSet.id}`);
  };

  // 上传文件配置
  const uploadProps: UploadProps = {
    name: "file",
    multiple: false,
    accept: ".txt",
    beforeUpload: () => false, // 阻止自动上传
    onChange: (info) => {
      // 处理文件选择
    },
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    console.log("handleUpload called with file:", file);
    if (!selectedBagSet) {
      message.error("请先选择bag集");
      return;
    }

    setUploadLoading(true);
    const formData = new FormData();
    formData.append("file", file);

    console.log("Uploading to:", `/api/bag-sets/${selectedBagSet.id}/upload`);

    try {
      console.log(
        "Making request to:",
        `/api/bag-sets/${selectedBagSet.id}/upload`,
      );
      console.log("FormData:", formData);

      const response = await axios.post(
        `/api/bag-sets/${selectedBagSet.id}/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        },
      );

      console.log("Upload response:", response.data);

      if (response.data.success) {
        message.success(response.data.message);
        setUploadModalVisible(false);
        uploadForm.resetFields(); // 重置表单
        loadBagSets(pagination.current, pagination.pageSize, searchKeyword);

        // 显示详细信息（如果有的话）
        if (
          response.data.details &&
          response.data.details.errors &&
          response.data.details.errors.length > 0
        ) {
          const errorMessages = response.data.details.errors
            .slice(0, 5)
            .join("\n");
          Modal.warning({
            title: "部分上传失败",
            content: (
              <div>
                <div>{response.data.details.error_count} 个bag上传失败:</div>
                <pre style={{ whiteSpace: "pre-wrap", fontSize: "12px" }}>
                  {errorMessages}
                </pre>
                {response.data.details.errors.length > 5 && (
                  <div>... 还有更多错误</div>
                )}
              </div>
            ),
            width: 600,
          });
        }
      } else {
        message.error("上传失败");
      }
    } catch (error: any) {
      console.error("Failed to upload file:", error);
      message.error(error.response?.data?.detail || "上传失败");
    } finally {
      setUploadLoading(false);
    }
  };

  // 表格列定义
  const columns: ColumnsType<BagSet> = [
    {
      title: "bag集名称",
      dataIndex: "set_name",
      key: "set_name",
      width: 160,
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: "创建人",
      width: 160,
      dataIndex: "creator_name",
      key: "creator_name",
    },
    {
      title: "描述",
      width: 160,
      dataIndex: "description",
      key: "description",
      ellipsis: true,
      render: (text) => text || "-",
    },
    {
      title: "统计",
      key: "stats",
      width: 160,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Tag icon={<FolderOutlined />} color="blue">
            {record.bag_count} bags
          </Tag>
          <Tag icon={<FileTextOutlined />} color="green">
            {record.total_pkl_count} pkls
          </Tag>
        </Space>
      ),
    },
    {
      title: "创建时间",
      width: 120,
      dataIndex: "created_at",
      key: "created_at",
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="进入标注">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleAnnotate(record)}
            >
              标注
            </Button>
          </Tooltip>

          <Tooltip title="分配人员">
            <Button
              size="small"
              icon={<TeamOutlined />}
              onClick={() => {
                setSelectedBagSetForDrawer(record);
                setDrawerVisible(true);
              }}
            >
              分配
            </Button>
          </Tooltip>

          <Tooltip title="查看详情">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>

          <Tooltip title="上传文件">
            <Button
              size="small"
              icon={<UploadOutlined />}
              onClick={() => {
                setSelectedBagSet(record);
                setUploadModalVisible(true);
              }}
            />
          </Tooltip>

          <Popconfirm
            title="确定删除？"
            description="删除后将无法恢复"
            onConfirm={() => handleDeleteBagSet(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button size="small" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 页面加载时获取数据
  useEffect(() => {
    loadBagSets();
  }, []);

  // 处理搜索
  const handleSearch = () => {
    loadBagSets(1, pagination.pageSize, searchKeyword);
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    loadBagSets(page, pageSize, searchKeyword);
  };

  return (
    <div className="h-[calc(100vh-64px)] p-4 bg-[#f0f2f5]">
      <div style={{ maxWidth: "1200px", margin: "0 auto", width: "100%" }}>
        <Card>
          <div style={{ marginBottom: "24px" }}>
            <Title level={2}>BAG 集管理</Title>
            <Text type="secondary">
              管理bag集合，每个bag集包含多个bag，每个bag对应多个pkl文件
            </Text>
          </div>

          {/* 操作栏 */}
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col flex="auto">
              <Space>
                <Input.Search
                  placeholder="搜索bag集名称、创建人或描述"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onSearch={handleSearch}
                  style={{ width: 300 }}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setCreateModalVisible(true)}
                >
                  创建bag集
                </Button>
              </Space>
            </Col>
          </Row>

          {/* 统计信息 */}
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={8}>
              <Statistic
                title="总bag集数"
                value={pagination.total}
                prefix={<FolderOutlined />}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="总bag数"
                value={bagSets.reduce((sum, item) => sum + item.bag_count, 0)}
                prefix={<FolderOutlined />}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="总pkl数"
                value={bagSets.reduce(
                  (sum, item) => sum + item.total_pkl_count,
                  0,
                )}
                prefix={<FileTextOutlined />}
              />
            </Col>
          </Row>

          {/* 表格 */}
          <Table
            columns={columns}
            dataSource={bagSets}
            rowKey="id"
            loading={loading}
            pagination={false}
            rowClassName={(_, index) =>
              index % 2 === 0 ? "table-row-light" : "table-row-dark"
            }
          />

          {/* 分页 */}
          <div style={{ marginTop: "16px", textAlign: "right" }}>
            <Pagination
              current={pagination.current}
              pageSize={pagination.pageSize}
              total={pagination.total}
              onChange={handleTableChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        </Card>
      </div>

      {/* 屉组件 */}
      {selectedBagSetForDrawer && (
        <BagSetDrawer
          visible={drawerVisible}
          onClose={handleDrawerClose}
          bagSet={selectedBagSetForDrawer}
        />
      )}

      {/* 创建bag集模态框 */}
      <Modal
        title="创建bag集"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
      >
        <Form form={createForm} layout="vertical" onFinish={handleCreateBagSet}>
          <Form.Item
            name="set_name"
            label="bag集名称"
            rules={[{ required: true, message: "请输入bag集名称" }]}
          >
            <Input placeholder="请输入bag集名称" />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <Input.TextArea placeholder="请输入bag集描述（可选）" rows={3} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button
                onClick={() => {
                  setCreateModalVisible(false);
                  createForm.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={`上传bag文件到: ${selectedBagSet?.set_name}`}
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          // 重置上传状态
          if (uploadForm) {
            uploadForm.resetFields();
          }
        }}
        footer={null}
        width={600}
      >
        <div style={{ marginBottom: "16px" }}>
          <Text type="secondary">
            请上传一个txt文件，每行包含一个bag文件的路径。每个bag文件应该是一个txt文件，包含该bag下所有pkl文件的路径。
          </Text>
        </div>

        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={(values) => {
            console.log("Form values:", values);
            if (values.file && values.file.length > 0) {
              const file = values.file[0].originFileObj || values.file[0];
              handleUpload(file);
            } else {
              message.warning("请先选择文件");
            }
          }}
        >
          <Form.Item
            name="file"
            valuePropName="fileList"
            getValueFromEvent={(e) => {
              console.log("Upload event:", e);
              if (Array.isArray(e)) {
                return e;
              }
              return e?.fileList;
            }}
            rules={[{ required: true, message: "请选择文件" }]}
          >
            <Dragger
              beforeUpload={() => false} // 阻止自动上传
              accept=".txt"
              maxCount={1}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持单个txt文件上传。文件格式：每行一个bag文件路径
              </p>
            </Dragger>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={uploadLoading}>
                {uploadLoading ? "上传中..." : "确认上传"}
              </Button>
              <Button
                onClick={() => setUploadModalVisible(false)}
                disabled={uploadLoading}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <div>
          <Text strong>文件格式示例：</Text>
          <pre
            style={{
              background: "#f5f5f5",
              padding: "8px",
              borderRadius: "4px",
              fontSize: "12px",
              marginTop: "8px",
            }}
          >
            {`/path/to/bag1.txt
/path/to/bag2.txt
/path/to/bag3.txt`}
          </pre>
          <Text type="secondary" style={{ fontSize: "12px" }}>
            每个bag文件内容格式：每行一个pkl文件路径
          </Text>
        </div>
      </Modal>

      {/* bag集详情模态框 */}
      <Modal
        title={`bag集详情: ${selectedBagSet?.set_name}`}
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setBagSetDetail(null);
        }}
        footer={null}
        width={800}
      >
        {selectedBagSet && bagSetDetail && (
          <div>
            <Row gutter={16} style={{ marginBottom: "16px" }}>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="总bag数"
                    value={selectedBagSet.bag_count}
                    prefix={<FolderOutlined />}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="总pkl数"
                    value={selectedBagSet.total_pkl_count}
                    prefix={<FileTextOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            <div style={{ marginBottom: "16px" }}>
              <Title level={4}>bag列表</Title>
            </div>

            <Table
              size="small"
              dataSource={bagSetDetail.bags}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              columns={[
                {
                  title: "bag名称",
                  dataIndex: "bag_name",
                  key: "bag_name",
                },
                {
                  title: "bag路径",
                  dataIndex: "bag_path",
                  key: "bag_path",
                  ellipsis: true,
                  render: (text) => (
                    <Tooltip title={text}>
                      <Text code style={{ fontSize: "12px" }}>
                        {text}
                      </Text>
                    </Tooltip>
                  ),
                },
                {
                  title: "pkl数量",
                  dataIndex: "pkl_count",
                  key: "pkl_count",
                  render: (count) => <Tag color="blue">{count} pkls</Tag>,
                },
                {
                  title: "创建时间",
                  dataIndex: "created_at",
                  key: "created_at",
                  render: (text) => new Date(text).toLocaleString(),
                },
              ]}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

// * -------------------------------------------------------------------------- drawer

/**
 * 抽屉组件 props 类型
 */
interface BagSetDrawerProps {
  visible: boolean;
  onClose: () => void;
  bagSet: BagSet;
}

/**
 * 加载 bag 列表参数类型
 */
interface LoadBagListParams {
  page?: number;
  pageSize?: number;
  sort?: string;
  assignFilter?: number;
}

/**
 * bag 列表项类型
 */
interface BagItem {
  id: number;
  bag_name: string;
  bag_path: string;
  created_at: string;
  pkl_count: number;

  uturn?: number;
  left_turn?: number;
  right_turn?: number;
  straight?: number;

  assigner_id?: number;
}

interface UserItem {
  id: number;
  username: string;
  employee_id: string;
  role: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * 默认分页大小
 */
const DEFAULT_PAGE_SIZE = 10;

// * ------------------------------------------------------ component

/**
 * 侧边抽屉组件 - 用于分配 bag 给某个 user
 */
const BagSetDrawer: React.FC<BagSetDrawerProps> = (props) => {
  const { visible, onClose, bagSet } = props;
  const bagSetId = bagSet.id;

  // 抽屉内部状态
  const [bagList, setBagList] = useState<BagItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  });

  // 选中状态
  const [selectedBags, setSelectedBags] = useState<BagItem[]>([]);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(
    null,
  );

  // 用户相关状态
  const [users, setUsers] = useState<UserItem[]>([]);

  // 转向类型
  const [sortFilter, setSortFilter] = useState<string>("");

  // 分配状态 0:未分配, 1:已分配, undefined:全部
  const [isAssigned, setIsAssigned] = useState<0 | 1 | undefined>(undefined);

  /**
   * 处理 bag 多选
   */
  const onBagSelectChange = (_: React.Key[], newSelectedRows: BagItem[]) => {
    setSelectedBags((prevSelected) => {
      // 获取当前页的所有 bag id
      const currentPageIds = bagList.map((bag) => bag.id);

      // 保留其他页面的选中项
      const otherPagesSelected = prevSelected.filter(
        (bag) => !currentPageIds.includes(bag.id),
      );

      // 合并当前页的选中项
      return [...otherPagesSelected, ...newSelectedRows];
    });
  };

  /**
   * 加载 user 列表
   */
  const { run: loadUsers, loading: loadingUsers } = useRequest(
    () => axios.get("/api/validity-check/users"),
    {
      cacheKey: "/api/validity-check/users",
      cacheTime: 5 * 60 * 1000, // 5分钟缓存
      onSuccess: (response) => {
        setUsers(response.data.users);
      },
      onError: (error) => {
        console.error("Failed to load users:", error);
        message.error("加载用户列表出错");
      },
    },
  );

  /**
   * 加载 bag 列表
   */
  const { run: loadBagList, loading: loadingBagList } = useRequest(
    async (params: LoadBagListParams) => {
      const {
        page = 1,
        pageSize = DEFAULT_PAGE_SIZE,
        sort,
        assignFilter,
      } = params;

      const requestParams: Record<string, any> = {
        page,
        per_page: pageSize,
        order: "desc",
        ...(sort && { sort_by: sort }),
        ...(assignFilter !== undefined && { assign_filter: assignFilter }),
      };

      const response = await axios.get(`/api/bag-sets/${bagSetId}`, {
        params: requestParams,
      });

      if (!response.data.success) {
        throw new Error("加载bag列表失败");
      }

      return response.data;
    },
    {
      manual: true,

      onSuccess: (data) => {
        setBagList(data.bags || []);
        setPagination({
          current: data.page || 1,
          pageSize: data.per_page || DEFAULT_PAGE_SIZE,
          total: data.total_bags || 0,
        });
      },

      onError: (error) => {
        console.error("Failed to load bag list:", error);
        message.error("加载bag列表出错");
      },
    },
  );

  /**
   * 刷新 bag 列表
   */
  const refreshBagList = (overrides: Partial<LoadBagListParams> = {}) => {
    loadBagList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      sort: sortFilter,
      assignFilter: isAssigned,
      ...overrides,
    });
  };

  /**
   * 分配 bag 给某个 user
   */
  const { runAsync: assignBags, loading: assignBagsLoading } = useRequest(
    () =>
      axios.post(`/api/bags/assign`, {
        assigner_id: selectedEmployeeId,
        bag_set_id: bagSetId,
        bags: selectedBags.map((bag) => String(bag.bag_name)),
      }),
    {
      manual: true,
      onSuccess: () => {
        message.success(`成功为 ${selectedBags.length} 个bag分配人员`);
        // 成功后自动清理状态并刷新
        setSelectedEmployeeId(null);
        setSelectedBags([]);
        refreshBagList(); // 刷新当前页
      },
      onError: () => {
        message.error("分配人员失败");
      },
    },
  );

  // 处理人员分配
  const handleAssignUsers = async () => {
    if (selectedBags.length === 0) {
      message.error("请先选择要分配的bag");
      return;
    }

    if (selectedEmployeeId === null) {
      message.error("请选择要分配的人员");
      return;
    }

    await assignBags();
  };

  /**
   * 处理分页变化
   */
  const handlePageChange = (page: number) => {
    refreshBagList({ page });
  };

  /**
   * 处理排序变化
   */
  const handleSortFilterChange = (sort: string) => {
    setSortFilter(sort);
    refreshBagList({ sort, page: 1 }); // 重置到第一页
  };

  /**
   * 处理分配状态变化
   */
  const handleAssignFilterChange = (assignFilter: 0 | 1 | undefined) => {
    setIsAssigned(assignFilter);
    refreshBagList({ assignFilter, page: 1 }); // 重置到第一页
  };

  /**
   * 状态重置
   */
  const resetDrawerState = () => {
    setBagList([]);
    setSelectedBags([]);
    setSelectedEmployeeId(null);
    setSortFilter("");
    setIsAssigned(undefined);
    setPagination({ current: 1, pageSize: DEFAULT_PAGE_SIZE, total: 0 });
  };

  /**
   * 处理抽屉关闭
   */
  const handleClose = () => {
    resetDrawerState();
    onClose();
  };

  // 表格列配置
  const bagColumns = [
    {
      title: "Bag名称",
      dataIndex: "bag_name",
      key: "bag_name",
      width: 200,
      render: (text: string) => <Text strong>{text}</Text>,
    },
    {
      title: "PKL数量",
      dataIndex: "pkl_count",
      key: "pkl_count",
      width: 100,
      render: (count: number) => <Tag color="#108ee9">{count || 0} PKLs</Tag>,
    },
    {
      title: "统计信息",
      key: "stats",
      render: (_: any, record: any) => (
        <div className="flex items-center gap-1">
          <Tag color="volcano">U: {record.uturn || 0}</Tag>
          <Tag color="yellow">L: {record.left_turn || 0}</Tag>
          <Tag color="green">R: {record.right_turn || 0}</Tag>
          <Tag color="orange">S: {record.straight || 0}</Tag>
        </div>
      ),
    },
    {
      title: "所属人员",
      key: "stats",
      render: (_: any, record: any) => {
        const userId = record.assigner_id;
        const userName = users.find(
          (user) => user.employee_id === userId,
        )?.username;
        if (!userName) {
          return "-";
        }
        return (
          <div className="flex items-center gap-1">
            <Tooltip title={userName}>
              <Avatar>{userName?.charAt(0)?.toUpperCase()}</Avatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  // 当抽屉打开且有选中的bagSet时，加载数据
  useEffect(() => {
    if (visible) {
      loadUsers();
      loadBagList({});
    }
  }, [visible]);

  return (
    <Drawer
      width={800}
      open={visible}
      placement="right"
      onClose={handleClose}
      title={`Bag 列表 - ${bagSet.set_name}`}
    >
      <div className="mb-4 p-4 bg-blue-50 rounded-lg flex gap-4">
        {/* 转向类型筛选 - 单选 */}
        <div className="flex flex-1 gap-2 items-center">
          <Text strong>场景类型排序</Text>
          <Radio.Group
            size="small"
            value={sortFilter}
            optionType="button"
            buttonStyle="solid"
            onChange={(e) => handleSortFilterChange(e.target.value)}
          >
            <Radio value="">全部</Radio>
            <Radio value="uturn">掉头U</Radio>
            <Radio value="left_turn">左转Left</Radio>
            <Radio value="right_turn">右转Right</Radio>
            <Radio value="straight">直行Straight</Radio>
          </Radio.Group>
        </div>

        {/* 分配状态筛选 - 单选 */}
        <div className="flex gap-2 items-center">
          <Text strong>分配状态筛选</Text>
          <Radio.Group
            size="small"
            optionType="button"
            buttonStyle="solid"
            value={isAssigned}
            onChange={(e) =>
              handleAssignFilterChange(
                e.target.value === "" ? undefined : (e.target.value as 0 | 1),
              )
            }
          >
            <Radio value={undefined}>全部</Radio>
            <Radio value={1}>已分配</Radio>
            <Radio value={0}>未分配</Radio>
          </Radio.Group>
        </div>
      </div>

      <div className="mb-4 p-3 bg-gray-50 rounded-lg flex gap-4">
        {/* 人员分配表单 */}
        <div className="flex flex-1 gap-2 items-center">
          <Select
            showSearch
            loading={loadingUsers}
            className="flex-1 min-w-[200px]"
            placeholder={loadingUsers ? "加载用户中..." : "请选择标注人员"}
            filterOption={(input, option) =>
              ((option?.children as unknown as string) ?? "")
                ?.toLowerCase()
                .includes(input.toLowerCase())
            }
            notFoundContent={
              loadingUsers ? <Spin size="small" /> : "暂无用户数据"
            }
            value={selectedEmployeeId}
            onChange={setSelectedEmployeeId}
          >
            {users.map((user) => (
              <Option value={user.employee_id} key={user.employee_id}>
                {`${user.username}（${user.employee_id}）`}
              </Option>
            ))}
          </Select>

          <Button
            type="primary"
            loading={assignBagsLoading}
            disabled={
              selectedBags.length === 0 ||
              loadingUsers ||
              selectedEmployeeId === null
            }
            icon={<TeamOutlined />}
            onClick={handleAssignUsers}
          >
            分配 Bag 给选中用户 ({selectedBags.length})
          </Button>
        </div>

        {/* 批量操作栏 */}
        <div className="flex-1 rounded-lg flex items-center">
          {selectedBags.length > 0 && (
            <div className="flex items-center gap-2">
              <Text>已选择 {selectedBags.length} 个bag</Text>
              <Button onClick={() => setSelectedBags([])}>取消选择</Button>
            </div>
          )}
        </div>

        {/* 状态提示 */}
        {loadingUsers && (
          <div className="mt-2 text-xs text-gray-500">正在加载用户列表...</div>
        )}

        {!loadingUsers && users.length === 0 && (
          <div className="mt-2 text-xs text-red-500">
            未加载到用户数据，请检查网络连接
          </div>
        )}
      </div>

      {/* Bag表格 */}
      <Table
        rowKey="id"
        size="small"
        scroll={{ x: 600 }}
        pagination={false}
        columns={bagColumns}
        dataSource={bagList}
        loading={loadingBagList}
        rowSelection={{
          selectedRowKeys: selectedBags.map((bag) => bag.id),
          onChange: onBagSelectChange,
        }}
      />

      {/* 分页 */}
      {pagination.total > pagination.pageSize && (
        <div style={{ textAlign: "center", marginTop: "16px" }}>
          <Pagination
            size="small"
            showSizeChanger={false}
            onChange={handlePageChange}
            total={pagination.total}
            current={pagination.current}
            pageSize={pagination.pageSize}
            showTotal={(total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
          />
        </div>
      )}
    </Drawer>
  );
};

export default BagSetManagement;
