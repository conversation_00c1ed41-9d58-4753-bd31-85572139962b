import React, { useState, useEffect } from "react";
import {
  Layout,
  Card,
  Button,
  Table,
  Space,
  message,
  Modal,
  Form,
  Input,
  Upload,
  Pagination,
  Popconfirm,
  Tag,
  Tooltip,
  Typography,
  Row,
  Col,
  Statistic,
} from "antd";
import {
  PlusOutlined,
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  FolderOutlined,
  FileTextOutlined,
  InboxOutlined,
  EditOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import type { UploadProps } from "antd";
import axios from "axios";
import { BagSet, BagSetResponse, BagSetDetail } from "../types";
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from "react-router-dom";

const { Title, Text } = Typography;
const { Dragger } = Upload;

const BagSetManagement: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [bagSets, setBagSets] = useState<BagSet[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchKeyword, setSearchKeyword] = useState("");

  // 模态框状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedBagSet, setSelectedBagSet] = useState<BagSet | null>(null);
  const [bagSetDetail, setBagSetDetail] = useState<BagSetDetail | null>(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  // 在其他useState和useForm之后添加
const [uploadForm] = Form.useForm();

  // 表单
  const [createForm] = Form.useForm();

  // 加载bag集列表
  const loadBagSets = async (page = 1, pageSize = 10, search = "") => {
    setLoading(true);
    try {
      const response = await axios.get("/api/bag-sets", {
        params: {
          page,
          per_page: pageSize,
          search,
        },
      });

      if (response.data.success) {
        setBagSets(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.per_page,
          total: response.data.total,
        });
      } else {
        message.error("加载bag集列表失败");
      }
    } catch (error) {
      console.error("Failed to load bag sets:", error);
      message.error("加载bag集列表出错");
    } finally {
      setLoading(false);
    }
  };

  // 创建bag集
  const handleCreateBagSet = async (values: any) => {
    try {
      const response = await axios.post("/api/bag-sets", {
        set_name: values.set_name,
        creator_name: user?.username || "unknown",
        description: values.description,
      });

      if (response.data.success) {
        message.success("bag集创建成功");
        setCreateModalVisible(false);
        createForm.resetFields();
        loadBagSets(pagination.current, pagination.pageSize, searchKeyword);
      } else {
        message.error(response.data.message || "创建失败");
      }
    } catch (error: any) {
      console.error("Failed to create bag set:", error);
      message.error(error.response?.data?.detail || "创建bag集失败");
    }
  };

  // 删除bag集
  const handleDeleteBagSet = async (bagSetId: number) => {
    try {
      const response = await axios.delete(`/api/bag-sets/${bagSetId}`);
      if (response.data.success) {
        message.success("bag集删除成功");
        loadBagSets(pagination.current, pagination.pageSize, searchKeyword);
      } else {
        message.error("删除失败");
      }
    } catch (error: any) {
      console.error("Failed to delete bag set:", error);
      message.error(error.response?.data?.detail || "删除bag集失败");
    }
  };

  // 查看bag集详情
  const handleViewDetail = async (bagSet: BagSet) => {
    setSelectedBagSet(bagSet);
    setDetailModalVisible(true);

    try {
      const response = await axios.get(`/api/bag-sets/${bagSet.id}`);
      if (response.data.success) {
        setBagSetDetail(response.data);
      } else {
        message.error("加载详情失败");
      }
    } catch (error) {
      console.error("Failed to load bag set detail:", error);
      message.error("加载详情出错");
    }
  };

  // 跳转到标注页面
  const handleAnnotate = (bagSet: BagSet) => {
    navigate(`/pdp-lane-scene-annotation/bag-set/${bagSet.id}`);
  };

  // 上传文件配置
  const uploadProps: UploadProps = {
    name: "file",
    multiple: false,
    accept: ".txt",
    beforeUpload: () => false, // 阻止自动上传
    onChange: (info) => {
      // 处理文件选择
    },
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    console.log("handleUpload called with file:", file);
    if (!selectedBagSet) {
      message.error("请先选择bag集");
      return;
    }

    setUploadLoading(true);
    const formData = new FormData();
    formData.append("file", file);

    console.log("Uploading to:", `/api/bag-sets/${selectedBagSet.id}/upload`);

    try {
      console.log("Making request to:", `/api/bag-sets/${selectedBagSet.id}/upload`);
      console.log("FormData:", formData);

      const response = await axios.post(
        `/api/bag-sets/${selectedBagSet.id}/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      console.log("Upload response:", response.data);

      if (response.data.success) {
        message.success(response.data.message);
        setUploadModalVisible(false);
        uploadForm.resetFields(); // 重置表单
        loadBagSets(pagination.current, pagination.pageSize, searchKeyword);
        
        // 显示详细信息（如果有的话）
        if (response.data.details && response.data.details.errors && response.data.details.errors.length > 0) {
          const errorMessages = response.data.details.errors.slice(0, 5).join('\n');
          Modal.warning({
            title: '部分上传失败',
            content: (
              <div>
                <div>{response.data.details.error_count} 个bag上传失败:</div>
                <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>{errorMessages}</pre>
                {response.data.details.errors.length > 5 && <div>... 还有更多错误</div>}
              </div>
            ),
            width: 600
          });
        }
      } else {
        message.error("上传失败");
      }
    } catch (error: any) {
      console.error("Failed to upload file:", error);
      message.error(error.response?.data?.detail || "上传失败");
    } finally {
      setUploadLoading(false);
    }
  };

  // 表格列定义
  const columns: ColumnsType<BagSet> = [
    {
      title: "bag集名称",
      dataIndex: "set_name",
      key: "set_name",
      render: (text) => <Text strong>{text}</Text>,
    },
    {
      title: "创建人",
      dataIndex: "creator_name",
      key: "creator_name",
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
      render: (text) => text || "-",
    },
    {
      title: "统计",
      key: "stats",
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Tag icon={<FolderOutlined />} color="blue">
            {record.bag_count} bags
          </Tag>
          <Tag icon={<FileTextOutlined />} color="green">
            {record.total_pkl_count} pkls
          </Tag>
        </Space>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      render: (_, record) => (
        <Space>
          <Tooltip title="标注">
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => handleAnnotate(record)}
            >
              标注
            </Button>
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="上传bag文件">
            <Button
              type="text"
              icon={<UploadOutlined />}
              onClick={() => {
                setSelectedBagSet(record);
                setUploadModalVisible(true);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个bag集吗？"
            description="删除后将无法恢复，包括所有相关的bag和pkl记录。"
            onConfirm={() => handleDeleteBagSet(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 测试API连接
  const testApiConnection = async () => {
    try {
      console.log("Testing API connection...");
      const response = await axios.get("/api/bag-sets?page=1&per_page=1");
      console.log("API test response:", response.data);
      message.success("API连接正常");
    } catch (error: any) {
      console.error("API test failed:", error);
      message.error(`API连接失败: ${error.message}`);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    loadBagSets();
  }, []);

  // 处理搜索
  const handleSearch = () => {
    loadBagSets(1, pagination.pageSize, searchKeyword);
  };

  // 处理分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    loadBagSets(page, pageSize, searchKeyword);
  };

  return (
    <Layout style={{ padding: "24px", background: "#f0f2f5" }}>
      <div style={{ maxWidth: "1200px", margin: "0 auto", width: "100%" }}>
        <Card>
          <div style={{ marginBottom: "24px" }}>
            <Title level={2}>bag集管理</Title>
            <Text type="secondary">
              管理bag集合，每个bag集包含多个bag，每个bag对应多个pkl文件
            </Text>
          </div>

          {/* 操作栏 */}
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col flex="auto">
              <Space>
                <Input.Search
                  placeholder="搜索bag集名称、创建人或描述"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  onSearch={handleSearch}
                  style={{ width: 300 }}
                />
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setCreateModalVisible(true)}
                >
                  创建bag集
                </Button>
                {/* <Button onClick={testApiConnection}>
                  测试API
                </Button> */}
              </Space>
            </Col>
          </Row>

          {/* 统计信息 */}
          <Row gutter={16} style={{ marginBottom: "16px" }}>
            <Col span={8}>
              <Statistic
                title="总bag集数"
                value={pagination.total}
                prefix={<FolderOutlined />}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="总bag数"
                value={bagSets.reduce((sum, item) => sum + item.bag_count, 0)}
                prefix={<FolderOutlined />}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="总pkl数"
                value={bagSets.reduce((sum, item) => sum + item.total_pkl_count, 0)}
                prefix={<FileTextOutlined />}
              />
            </Col>
          </Row>

          {/* 表格 */}
          <Table
            columns={columns}
            dataSource={bagSets}
            rowKey="id"
            loading={loading}
            pagination={false}
          />

          {/* 分页 */}
          <div style={{ marginTop: "16px", textAlign: "right" }}>
            <Pagination
              current={pagination.current}
              pageSize={pagination.pageSize}
              total={pagination.total}
              onChange={handleTableChange}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        </Card>
      </div>

      {/* 创建bag集模态框 */}
      <Modal
        title="创建bag集"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          createForm.resetFields();
        }}
        footer={null}
      >
        <Form form={createForm} layout="vertical" onFinish={handleCreateBagSet}>
          <Form.Item
            name="set_name"
            label="bag集名称"
            rules={[{ required: true, message: "请输入bag集名称" }]}
          >
            <Input placeholder="请输入bag集名称" />
          </Form.Item>
          <Form.Item name="description" label="描述">
            <Input.TextArea
              placeholder="请输入bag集描述（可选）"
              rows={3}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button
                onClick={() => {
                  setCreateModalVisible(false);
                  createForm.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
  title={`上传bag文件到: ${selectedBagSet?.set_name}`}
  open={uploadModalVisible}
  onCancel={() => {
    setUploadModalVisible(false);
    // 重置上传状态
    if (uploadForm) {
      uploadForm.resetFields();
    }
  }}
  footer={null}
  width={600}
>
  <div style={{ marginBottom: "16px" }}>
    <Text type="secondary">
      请上传一个txt文件，每行包含一个bag文件的路径。每个bag文件应该是一个txt文件，包含该bag下所有pkl文件的路径。
    </Text>
  </div>
  
  <Form
    form={uploadForm}
    layout="vertical"
    onFinish={(values) => {
      console.log("Form values:", values);
      if (values.file && values.file.length > 0) {
        const file = values.file[0].originFileObj || values.file[0];
        handleUpload(file);
      } else {
        message.warning("请先选择文件");
      }
    }}
  >
    <Form.Item
      name="file"
      valuePropName="fileList"
      getValueFromEvent={(e) => {
        console.log("Upload event:", e);
        if (Array.isArray(e)) {
          return e;
        }
        return e?.fileList;
      }}
      rules={[{ required: true, message: "请选择文件" }]}
    >
      <Dragger
        beforeUpload={() => false} // 阻止自动上传
        accept=".txt"
        maxCount={1}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p className="ant-upload-hint">
          支持单个txt文件上传。文件格式：每行一个bag文件路径
        </p>
      </Dragger>
    </Form.Item>
    
    <Form.Item>
      <Space>
        <Button type="primary" htmlType="submit" loading={uploadLoading}>
          {uploadLoading ? "上传中..." : "确认上传"}
        </Button>
        <Button onClick={() => setUploadModalVisible(false)} disabled={uploadLoading}>
          取消
        </Button>
      </Space>
    </Form.Item>
  </Form>
  
  <div>
    <Text strong>文件格式示例：</Text>
    <pre style={{
      background: "#f5f5f5",
      padding: "8px",
      borderRadius: "4px",
      fontSize: "12px",
      marginTop: "8px"
    }}>
{`/path/to/bag1.txt
/path/to/bag2.txt
/path/to/bag3.txt`}
    </pre>
    <Text type="secondary" style={{ fontSize: "12px" }}>
      每个bag文件内容格式：每行一个pkl文件路径
    </Text>
  </div>
</Modal>

      {/* bag集详情模态框 */}
      <Modal
        title={`bag集详情: ${selectedBagSet?.set_name}`}
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setBagSetDetail(null);
        }}
        footer={null}
        width={800}
      >
        {bagSetDetail && (
          <div>
            <Row gutter={16} style={{ marginBottom: "16px" }}>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="总bag数"
                    value={bagSetDetail.total_bags}
                    prefix={<FolderOutlined />}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small">
                  <Statistic
                    title="总pkl数"
                    value={bagSetDetail.total_pkls}
                    prefix={<FileTextOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            <div style={{ marginBottom: "16px" }}>
              <Title level={4}>bag列表</Title>
            </div>

            <Table
              size="small"
              dataSource={bagSetDetail.bags}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              columns={[
                {
                  title: "bag名称",
                  dataIndex: "bag_name",
                  key: "bag_name",
                },
                {
                  title: "bag路径",
                  dataIndex: "bag_path",
                  key: "bag_path",
                  ellipsis: true,
                  render: (text) => (
                    <Tooltip title={text}>
                      <Text code style={{ fontSize: "12px" }}>
                        {text}
                      </Text>
                    </Tooltip>
                  ),
                },
                {
                  title: "pkl数量",
                  dataIndex: "pkl_count",
                  key: "pkl_count",
                  render: (count) => (
                    <Tag color="blue">{count} pkls</Tag>
                  ),
                },
                {
                  title: "创建时间",
                  dataIndex: "created_at",
                  key: "created_at",
                  render: (text) => new Date(text).toLocaleString(),
                },
              ]}
            />
          </div>
        )}
      </Modal>
    </Layout>
  );
};

export default BagSetManagement;
