/* src/components/CreateEvaluationTask.css */
.create-task-container {
    height: 100%;
    position: relative;
    width: 100%;

}

.panel-toggle {
    position: absolute;
    left: 0;
    top: 20px;
    z-index: 10;
}

.toggle-button {
    height: 40px;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-creation-sider {
    height: 100%;
    border-right: 1px solid #f0f0f0;
    overflow-y: auto;
}

.sider-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.panel-title {
    font-size: 20px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.config-area {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20px;
}

.config-selector h3 {
    margin-bottom: 16px;
}

.config-list {
    max-height: 400px;
    overflow-y: auto;
}

.config-item {
    /* padding: 12px; */
    border: 1px solid #eee;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: normal;
}

.config-item:hover {
    border-color: #1890ff;
    background-color: #f8f8f8;
}

.config-item.selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.config-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.config-name {
    font-weight: 500;
}

.config-model {
    font-size: 12px;
    color: #666;
}

.config-time {
    font-size: 12px;
    color: #999;
}

.task-creation {
    /* padding-top: 15px; */
    border-top: 1px solid #f0f0f0;
}

.selection-summary {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.create-button {
    width: 100%;
}

.sets-content {
    padding: 20px;
    overflow-y: auto;
    height: 100%;
}

.loading-indicator, .empty-message {
    padding: 30px;
    text-align: center;
    color: #999;
}

.evaluation-set-selection {
    height: 100%;
    overflow-y: auto;
    padding: 0 16px;
}

.evaluation-set-selection .panel-title {
    margin-bottom: 16px;
    padding-top: 16px;
}

/* 选择指示器样式 */
.selection-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.selection-indicator .ant-badge {
    margin-right: 8px;
}