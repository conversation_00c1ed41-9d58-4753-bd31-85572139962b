/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef } from "react";
import {
  Layout,
  List,
  Card,
  Button,
  Input,
  Pagination,
  Tag,
  Space,
  message,
  Spin,
  Radio,
  Typography,
  Row,
  Col,
} from "antd";
import {
  CopyOutlined,
  SearchOutlined,
  ExportOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
  CommentOutlined,
  WarningOutlined,
  ClearOutlined,
} from "@ant-design/icons";
import axios from "axios";
import { useParams } from "react-router-dom";
import ResizableSider from "../components/ResizableSider";
import ReactECharts from "echarts-for-react";
import PickleVisualizer from "../components/PickleVisualizer";
import "./PdpPathAnnotation.css"; // 复用相同的样式
import {
  EvaluationCase,
  EvaluationSet,
  PdpPathInfo,
  DlpTrajectoryInfo,
} from "../types";
import { useAuth } from "../contexts/AuthContext";

const { Text } = Typography;
const { Sider, Content } = Layout;

interface DlpAnnotationStats {
  total: number;
  annotated: number;
  level0: number; // 标注值为 "0"
  level1: number; // 标注值为 "1"
  level2: number; // 标注值为 "2"
  level3: number; // 标注值为 "3"
  level4: number; // 标注值为 "4"
  level5: number; // 标注值为 "5"
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success("路径已复制到剪贴板");
  } catch (err) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand("copy");
    document.body.removeChild(textArea);
    message.success("已复制到剪贴板");
  }
};

const DlpPathAnnotation: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { id } = useParams<{ id: string }>();
  // 添加 refs 用于手动触发图表更新
  const velocityChartRef = useRef<any>(null);
  const accelerationChartRef = useRef<any>(null);
  const distanceChartRef = useRef<any>(null);
  useEffect(() => {
    if (!isAuthenticated) {
      message.error("请先登录后再进行标注操作");
      return;
    }
  }, [isAuthenticated]);

  // 状态管理
  const [leftSiderWidth, setLeftSiderWidth] = useState(400);
  const [rightSiderWidth, setRightSiderWidth] = useState(300);

  const [annotationStats, setAnnotationStats] = useState<DlpAnnotationStats>({
    total: 0,
    annotated: 0,
    level0: 0,
    level1: 0,
    level2: 0,
    level3: 0,
    level4: 0,
    level5: 0,
  });

  const [evaluationSet, setEvaluationSet] = useState<EvaluationSet | null>(
    null
  );
  const [pklList, setPklList] = useState<EvaluationCase[]>([]);
  const [selectedPkl, setSelectedPkl] = useState<EvaluationCase | null>(null);
  const [pdpPaths, setPdpPaths] = useState<Record<number, PdpPathInfo>>({});
  const [dlpTrajs, setDlpTrajs] = useState<DlpTrajectoryInfo[]>([]);
  const [highlightTrajIndex, setHighlightTrajIndex] = useState<number | null>(
    null
  );
  const [highlightPathIndex, setHighlightPathIndex] = useState<number | null>(
    null
  );

  const [loading, setLoading] = useState({
    pklList: false,
    paths: false,
    visualization: false,
    trajectories: false,
    annotation: false,
    markDirty: false,
    checkPkl: false,
    savingTag: false,
    checkAnnotation: false,
  });

  const [pklPagination, setPklPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const [isDirtyData, setIsDirtyData] = useState(false);
  const [showFullyAnnotatedOnly, setShowFullyAnnotatedOnly] = useState(false);
  const [showCheckDlpAnnotation, setShowCheckDlpAnnotation] = useState(false);
  const [checkStatusFilter, setCheckStatusFilter] = useState<
    "all" | "checked" | "unchecked"
  >("all");

  const [bagName, setBagName] = useState("");
  const [timeNs, setTimeNs] = useState("");
  const [timeRange, setTimeRange] = useState("");

  const [pathInPickle, setHasPdpPaths] = useState(true); // 是否有PDP路径数据

  // 添加状态来存储最新的标注信息
  const [latestAnnotationInfo, setLatestAnnotationInfo] = useState<{
    employee_id: string;
    updated_at: string;
  } | null>(null);

  // 在现有状态声明中添加
  const [egoTrajLength, setEgoTrajLength] = useState<number[]>([]);
  const [egoTrajVel, setEgoTrajVel] = useState<number[]>([]);
  // 添加用于保存选中对象ID的状态
  const [selectedObjectIds, setSelectedObjectIds] = useState<string[]>([]);
  // 添加用于保存PKL标签的状态
  const [pklTag, setPklTag] = useState<string>("");
  // 添加员工ID过滤状态
  const [employeeId, setEmployeeId] = useState("");

  const [tagFilter, setTagFilter] = useState("");

  // 修改loadPklList函数，去掉search参数
  const loadPklList = async (
    page = pklPagination.current,
    pageSize = pklPagination.pageSize,
    search = "", // 固定为空字符串，不再使用
    fullyAnnotatedOnly = showFullyAnnotatedOnly,
    checkStatus = checkStatusFilter,
    bag_name = bagName,
    time_ns = timeNs,
    time_range = timeRange,
    employee_id = employeeId,
    tag_filter = tagFilter
  ) => {
    if (!id) return;

    setLoading((prev) => ({ ...prev, pklList: true }));
    try {
      const params: any = {
        page,
        per_page: pageSize,
        search: "", // 固定为空字符串
        tag_filter,
        dlp_fully_annotated_only: fullyAnnotatedOnly,
        check_status: checkStatus,
      };

      // 添加员工ID过滤参数
      if (employee_id && employee_id.trim()) {
        params.employee_id = employee_id.trim();
      }

      // 只有当bag_name、时间戳和时间范围都不为空时，才添加这些参数
      // if (bag_name && time_ns && time_range) {
      if (bag_name) {
        params.bag_name = bag_name;
        params.time_ns = time_ns;
        params.time_range = time_range;
      }

      const response = await axios.get(`/api/evaluation_sets/${id}`, {
        params,
      });

      if (response.data.success) {
        const evaluationSetData: EvaluationSet = {
          id: response.data.evaluation_set.id,
          set_name: response.data.evaluation_set.name,
          creator_name: response.data.evaluation_set.creator_name || "",
          description: response.data.evaluation_set.description,
          cases: response.data.cases || [],
          case_count: response.data.case_count || 0,
          created_at: response.data.evaluation_set.created_at,
        };
        setEvaluationSet(evaluationSetData);
        setPklList(response.data.cases || []);
        setPklPagination({
          current: response.data.page || page,
          pageSize: response.data.per_page || pageSize,
          total: response.data.case_count || 0,
        });
      } else {
        message.error(response.data.error || "加载PKL列表失败");
      }
    } catch (error) {
      console.error("Failed to load PKL list:", error);
      message.error("加载PKL列表出错");
    } finally {
      setLoading((prev) => ({ ...prev, pklList: false }));
    }
  };
  const checkPklList = async (
    page = pklPagination.current,
    pageSize = pklPagination.pageSize,
    search = "", // 固定为空字符串，不再使用
    fullyAnnotatedOnly = showFullyAnnotatedOnly,
    checkStatus = checkStatusFilter,
    bag_name = bagName,
    time_ns = timeNs,
    time_range = timeRange,
    employee_id = employeeId,
    tag_filter = tagFilter
  ) => {
    if (!id) return;
    // 设置loading状态
    setLoading((prev) => ({ ...prev, checkAnnotation: true }));
    try {
      const params: any = {
        page,
        per_page: pageSize,
        search: "", // 固定为空字符串
        tag_filter,
        dlp_fully_annotated_only: fullyAnnotatedOnly,
        check_status: checkStatus,
      };

      // 添加员工ID过滤参数
      if (employee_id && employee_id.trim()) {
        params.employee_id = employee_id.trim();
      }

      // 只有当bag_name、时间戳和时间范围都不为空时，才添加这些参数
      if (bag_name && time_ns && time_range) {
        params.bag_name = bag_name;
        params.time_ns = time_ns;
        params.time_range = time_range;
      }
      if (bag_name) {
        params.bag_name = bag_name;
      }
      // caculate time spend for get
      const startTime = performance.now();
      const response = await axios.get(
        `/api/annotation/check-dlp-annotations/${id}`,
        {
          params,
        }
      );
      console.log(
        "time spend for check(s):",
        (performance.now() - startTime) / 1000
      );
      if (response.data.success) {
        // const evaluationSetData: EvaluationSet = {
        //   id: response.data.evaluation_set.id,
        //   set_name: response.data.evaluation_set.name,
        //   creator_name: response.data.evaluation_set.creator_name || "",
        //   description: response.data.evaluation_set.description,
        //   cases: response.data.cases || [],
        //   case_count: response.data.case_count || 0,
        //   created_at: response.data.evaluation_set.created_at,
        // };
        // setEvaluationSet(evaluationSetData);
        setPklList(response.data.cases || []);
        setPklPagination({
          current: response.data.page || page,
          pageSize: response.data.per_page || pageSize,
          total: response.data.case_count || 0,
        });
      } else {
        message.error(response.data.error || "加载PKL列表失败");
      }
    } catch (error) {
      console.error("Failed to load PKL list:", error);
      message.error("加载PKL列表出错");
    } finally {
      setLoading((prev) => ({ ...prev, checkAnnotation: false }));
    }
  };
  // 导出标注数据
  const handleExportAnnotations = async () => {
    if (!id) {
      message.error("缺少评测集ID");
      return;
    }

    try {
      const exportUrl = `/api/annotation/export-dlp-annotations/${id}`;
      window.open(exportUrl, "_blank");
    } catch (error) {
      console.error("Export failed:", error);
      message.error("导出失败，请稍后再试");
    }
  };
  const handleCheckDlpAnnotations = async () => {
    if (!id) {
      message.error("缺少评测集ID");
      return;
    }
    const newCheckState = !showCheckDlpAnnotation;
    setShowCheckDlpAnnotation(newCheckState);
    if (newCheckState) {
      checkPklList();
    } else {
      loadPklList();
    }
  };
  // 切换PKL检查状态
  const handleTogglePklCheck = async (
    pkl: EvaluationCase,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();
    if (!id) {
      message.error("缺少评测集ID");
      return;
    }

    setLoading((prev) => ({ ...prev, checkPkl: true }));

    try {
      const isCurrentlyChecked = pkl.is_checked;
      const endpoint = isCurrentlyChecked
        ? `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`
        : `/api/evaluation_sets/${id}/check-pkl/${pkl.id}`;

      const method = isCurrentlyChecked ? "delete" : "post";

      const response = await axios[method](endpoint);

      if (response.data.success) {
        // 更新PKL列表中的检查状态
        const updatedPklList = pklList.map((item) => {
          if (item.id === pkl.id) {
            return {
              ...item,
              is_checked: !isCurrentlyChecked,
              checked_at: isCurrentlyChecked
                ? undefined
                : new Date().toISOString(),
              checked_by: isCurrentlyChecked ? undefined : user?.employee_id,
            };
          }
          return item;
        });

        setPklList(updatedPklList);

        // 如果当前选中的PKL就是被操作的PKL，也要更新selectedPkl状态
        if (selectedPkl?.id === pkl.id) {
          setSelectedPkl({
            ...selectedPkl,
            is_checked: !isCurrentlyChecked,
            checked_at: isCurrentlyChecked
              ? undefined
              : new Date().toISOString(),
            checked_by: isCurrentlyChecked ? undefined : user?.employee_id,
          });
        }

        message.success(
          isCurrentlyChecked ? "PKL检查标记已取消" : "PKL已标记为已检查"
        );
      } else {
        message.error("操作失败");
      }
    } catch (error) {
      console.error("Failed to toggle pkl check:", error);
      message.error("操作出错");
    } finally {
      setLoading((prev) => ({ ...prev, checkPkl: false }));
    }
  };

  // 初始加载
  useEffect(() => {
    if (id) {
      loadPklList();
    }
  }, [id]);

  // 当选择PKL文件时加载DLP轨迹
  const handlePklSelect = async (pkl: EvaluationCase) => {
    setSelectedPkl(pkl);
    setHighlightTrajIndex(null);
    setHighlightPathIndex(0);
    setLatestAnnotationInfo(null);
    setEgoTrajLength([]);
    setEgoTrajVel([]);
    setSelectedObjectIds([]);
    setPklTag(""); // 重置标签
    setLoading((prev) => ({ ...prev, trajectories: true, paths: true }));

    try {
      const response = await axios.get(`/api/annotation/dlp-paths/${pkl.id}`, {
        params: {
          evaluation_set_id: id,
        },
      });

      if (response.data.success) {
        if (response.data.paths && response.data.paths.length > 0) {
          const pathsDict: Record<number, PdpPathInfo> = {};
          response.data.paths.forEach((path: PdpPathInfo) => {
            pathsDict[path.index] = path;
          });
          setPdpPaths(pathsDict);
          setHasPdpPaths(true);
        } else {
          setHasPdpPaths(false);
          setPdpPaths({});
        }

        const trajectories: DlpTrajectoryInfo[] = response.data.trajs || [];
        setDlpTrajs(trajectories);
        setIsDirtyData(response.data.is_dirty || false);

        // 设置最新的标注信息
        if (response.data.latest_annotation_info) {
          setLatestAnnotationInfo(response.data.latest_annotation_info);
        }
        // 设置选中的对象ID
        if (response.data.selected_object_ids) {
          setSelectedObjectIds(response.data.selected_object_ids);
        } else {
          setSelectedObjectIds([]);
        }
        // 设置标签
        if (response.data.tag) {
          setPklTag(response.data.tag);
        } else {
          setPklTag("");
        }
        // 计算标注统计信息
        const total = trajectories.length;
        let annotated = 0;
        let level0 = 0,
          level1 = 0,
          level2 = 0,
          level3 = 0,
          level4 = 0,
          level5 = 0;

        trajectories.forEach((traj) => {
          if (traj.annotation) {
            annotated++;
            switch (traj.annotation.annotation) {
              case "0":
                level0++;
                break;
              case "1":
                level1++;
                break;
              case "2":
                level2++;
                break;
              case "3":
                level3++;
                break;
              case "4":
                level4++;
                break;
              case "5":
                level5++;
                break;
            }
          }
        });

        setAnnotationStats({
          total,
          annotated,
          level0,
          level1,
          level2,
          level3,
          level4,
          level5,
        });

        // 添加处理ego轨迹长度数据
        if (response.data.ego_traj_length) {
          setEgoTrajLength(response.data.ego_traj_length);
        } else {
          setEgoTrajLength([]);
        }

        // 添加处理ego轨迹速度数据
        if (response.data.ego_traj_vel) {
          setEgoTrajVel(response.data.ego_traj_vel);
        } else {
          setEgoTrajVel([]);
        }

        // 保存future_objects_info到全局变量中供图表使用
        if (response.data.future_objects_info) {
          (window as any).futureObjectsInfo = response.data.future_objects_info;
        } else {
          (window as any).futureObjectsInfo = {};
        }
      } else {
        setHasPdpPaths(false);
        setPdpPaths({});
        setDlpTrajs([]);
        setPklTag(""); // 重置标签
        setAnnotationStats({
          total: 0,
          annotated: 0,
          level0: 0,
          level1: 0,
          level2: 0,
          level3: 0,
          level4: 0,
          level5: 0,
        });
        message.error("加载DLP轨迹失败");
      }
    } catch (error) {
      console.error("Failed to load DLP trajectories:", error);
      message.error("加载轨迹数据出错");
    } finally {
      setLoading((prev) => ({ ...prev, trajectories: false, paths: false }));
    }
  };

  // 高亮某条轨迹
  const handleHighlightTraj = (trajIndex: number) => {
    setHighlightTrajIndex(trajIndex === highlightTrajIndex ? null : trajIndex);
  };

  // 提交标注
  const handleAnnotate = async (trajIndex: number, annotation: string) => {
    if (!selectedPkl) {
      message.error("请先选择PKL文件");
      return;
    }

    // 检查是否需要删除标注（点击了当前已选择的按钮）
    const currentAnnotation = dlpTrajs[trajIndex]?.annotation?.annotation;
    const isDeleteAction = currentAnnotation === annotation;

    setLoading((prev) => ({ ...prev, annotation: true }));
    try {
      const response = await axios.post("/api/annotation/dlp-paths", {
        pkl_id: selectedPkl.id,
        traj_index: trajIndex,
        annotation,
        delete_annotation: isDeleteAction,
        evaluation_set_id: id,
        employee_id: user?.employee_id || null,
      });

      if (response.data.success) {
        // 更新轨迹列表中的标注状态
        const updatedTrajs = [...dlpTrajs];
        const oldAnnotation = updatedTrajs[trajIndex].annotation?.annotation;

        if (isDeleteAction) {
          updatedTrajs[trajIndex] = {
            ...updatedTrajs[trajIndex],
            annotation: undefined,
          };
          message.success("标注已删除");
        } else {
          updatedTrajs[trajIndex] = {
            ...updatedTrajs[trajIndex],
            annotation: {
              annotation,
              employee_id: user?.employee_id || undefined,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          };
          message.success("标注保存成功");
        }

        setDlpTrajs(updatedTrajs);

        // 更新统计信息
        setAnnotationStats((prev) => {
          const newStats = { ...prev };

          if (isDeleteAction && oldAnnotation) {
            newStats.annotated--;
            switch (oldAnnotation) {
              case "0":
                newStats.level0--;
                break;
              case "1":
                newStats.level1--;
                break;
              case "2":
                newStats.level2--;
                break;
              case "3":
                newStats.level3--;
                break;
              case "4":
                newStats.level4--;
                break;
              case "5":
                newStats.level5--;
                break;
            }
          } else if (!oldAnnotation && !isDeleteAction) {
            newStats.annotated++;
            switch (annotation) {
              case "0":
                newStats.level0++;
                break;
              case "1":
                newStats.level1++;
                break;
              case "2":
                newStats.level2++;
                break;
              case "3":
                newStats.level3++;
                break;
              case "4":
                newStats.level4++;
                break;
              case "5":
                newStats.level5++;
                break;
            }
          } else if (oldAnnotation !== annotation && !isDeleteAction) {
            switch (oldAnnotation) {
              case "0":
                newStats.level0--;
                break;
              case "1":
                newStats.level1--;
                break;
              case "2":
                newStats.level2--;
                break;
              case "3":
                newStats.level3--;
                break;
              case "4":
                newStats.level4--;
                break;
              case "5":
                newStats.level5--;
                break;
            }
            switch (annotation) {
              case "0":
                newStats.level0++;
                break;
              case "1":
                newStats.level1++;
                break;
              case "2":
                newStats.level2++;
                break;
              case "3":
                newStats.level3++;
                break;
              case "4":
                newStats.level4++;
                break;
              case "5":
                newStats.level5++;
                break;
            }
          }

          return newStats;
        });
      } else {
        message.error("操作失败");
      }
    } catch (error) {
      console.error("Failed to save/delete annotation:", error);
      message.error("操作出错");
    } finally {
      setLoading((prev) => ({ ...prev, annotation: false }));
    }
  };

  // 处理标记为脏数据
  const handleMarkAsDirty = async () => {
    if (!selectedPkl) {
      message.error("请先选择PKL文件");
      return;
    }

    setLoading((prev) => ({ ...prev, markDirty: true }));
    try {
      const response = await axios.post("/api/annotation/mark-dirty", {
        pkl_id: selectedPkl.id,
        is_dirty: !isDirtyData,
      });

      if (response.data.success) {
        setIsDirtyData(response.data.is_dirty);
        message.success(
          response.data.is_dirty ? "已标记为脏数据" : "已取消脏数据标记"
        );

        const updatedPklList = pklList.map((pkl) => {
          if (pkl.id === selectedPkl.id) {
            return { ...pkl, dirty_data: response.data.is_dirty };
          }
          return pkl;
        });
        setPklList(updatedPklList);
      } else {
        message.error("操作失败");
      }
    } catch (error) {
      console.error("Failed to mark as dirty data:", error);
      message.error("操作出错");
    } finally {
      setLoading((prev) => ({ ...prev, markDirty: false }));
    }
  };

  // 修改相关的处理函数
  const handlePklPageChange = (page: number, pageSize?: number) => {
    if (showCheckDlpAnnotation) {
      checkPklList(
        page,
        pageSize,
        "",
        showFullyAnnotatedOnly,
        checkStatusFilter,
        bagName,
        timeNs,
        timeRange,
        employeeId,
        tagFilter
      );
    } else {
      loadPklList(
        page,
        pageSize,
        "", // search固定为空
        showFullyAnnotatedOnly,
        checkStatusFilter,
        bagName,
        timeNs,
        timeRange,
        employeeId,
        tagFilter
      );
    }
  };

  const handleSearch = () => {
    if (showCheckDlpAnnotation) {
      checkPklList(
        1,
        pklPagination.pageSize,
        "",
        showFullyAnnotatedOnly,
        checkStatusFilter,
        bagName,
        timeNs,
        timeRange,
        employeeId,
        tagFilter
      );
    } else {
      loadPklList(
        1,
        pklPagination.pageSize,
        "", // search固定为空
        showFullyAnnotatedOnly,
        checkStatusFilter,
        bagName,
        timeNs,
        timeRange,
        employeeId,
        tagFilter
      );
    }
  };

  const handleFilterToggle = () => {
    const newFilterState = !showFullyAnnotatedOnly;
    setShowFullyAnnotatedOnly(newFilterState);
    if (showCheckDlpAnnotation) {
      checkPklList(
        1,
        pklPagination.pageSize,
        "", // search固定为空
        newFilterState,
        checkStatusFilter,
        bagName,
        timeNs,
        timeRange,
        employeeId,
        tagFilter
      );
    } else {
      loadPklList(
        1,
        pklPagination.pageSize,
        "", // search固定为空
        newFilterState,
        checkStatusFilter,
        bagName,
        timeNs,
        timeRange,
        employeeId,
        tagFilter
      );
    }
  };

  const handleCheckStatusFilter = (status: "all" | "checked" | "unchecked") => {
    setCheckStatusFilter(status);
    loadPklList(
      1,
      pklPagination.pageSize,
      "", // search固定为空
      showFullyAnnotatedOnly,
      status,
      bagName,
      timeNs,
      timeRange,
      employeeId,
      tagFilter
    );
  };

  const handleClearBagTimeFilter = () => {
    setBagName("");
    setTimeNs("");
    setTimeRange("");
    setEmployeeId(""); // 同时清空员工ID
    setTagFilter("");
    loadPklList(
      1,
      pklPagination.pageSize,
      "", // search固定为空
      showFullyAnnotatedOnly,
      checkStatusFilter,
      "",
      "",
      "",
      "",
      ""
    );
  };

  const handleSaveTag = async () => {
    if (!selectedPkl) {
      message.error("请先选择PKL文件");
      return;
    }
    setLoading((prev) => ({ ...prev, savingTag: true }));
    try {
      const response = await axios.post("/api/annotation/save-dlp-tag", {
        pkl_id: selectedPkl.id,
        tag: pklTag,
        evaluation_set_id: id,
        employee_id: user?.employee_id || null,
      });

      if (response.data.success) {
        message.success("标签保存成功");
      } else {
        message.error("标签保存失败");
      }
    } catch (error) {
      console.error("Failed to save tag:", error);
      message.error("标签保存出错");
    } finally {
      setLoading((prev) => ({ ...prev, savingTag: false }));
    }
  };

  // 定义统一的颜色表，用于不同对象在所有图表中保持一致的颜色
  const colorPalette = [
    "#FF6B6B",
    "#4ECDC4",
    "#45B7D1",
    "#FFBE0B",
    "#FB5607",
    "#8338EC",
    "#3A86FF",
    "#06D6A0",
    "#118AB2",
    "#073B4C",
    "#EF476F",
    "#FFD166",
    "#073B4C",
    "#118AB2",
    "#06D6A0",
  ];

  // 生成速度曲线图配置
  const getVelocityChartOption = () => {
    if (!dlpTrajs || dlpTrajs.length === 0) {
      return {
        title: { text: "速度曲线", left: "center" },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value", name: "速度 (m/s)" },
        series: [],
      };
    }

    // 首先找到概率最高的轨迹索引
    let maxProbIndex = -1;
    let maxProb = -1;
    
    dlpTrajs.forEach((traj, index) => {
      const prob = traj.probability || 0;
      if (prob > maxProb) {
        maxProb = prob;
        maxProbIndex = index;
      }
    });

    // 生成时间轴数据，每个点间隔0.2秒
    const timeData =
      dlpTrajs[0]?.vel.map((_, i) => ((i + 1) * 0.2).toFixed(1)) || [];

    // DLP轨迹系列
    const series: any = dlpTrajs.map((traj, index) => {
      const isMaxProbTraj = index === maxProbIndex; // 判断是否为概率最高的轨迹
      
      return {
        name: `轨迹 ${index}`,
        type: "line",
        data: traj.vel.map((v, idx) => [idx * 0.2, v]), // 修改为二维数据格式[时间, 速度]
        symbol: isMaxProbTraj ? "circle" : "none", // 概率最高的轨迹显示数据点
        symbolSize: isMaxProbTraj ? 4 : 2, // 数据点大小
        lineStyle: {
          width: highlightTrajIndex === index ? 6 : 2,
          // 保持原有颜色逻辑，不修改颜色
        },
        // 为概率最高的轨迹添加数据点样式
        itemStyle: isMaxProbTraj ? {
          borderColor: "#fff",
          borderWidth: 1
        } : undefined,
        emphasis: {
          focus: "series",
        },
      };
    });

    // 添加ego轨迹速度系列
    if (egoTrajVel && egoTrajVel.length > 0) {
      series.push({
        name: "Ego轨迹速度",
        type: "line",
        data: egoTrajVel.map((v, idx) => [idx * 0.2, v]), // 修改为二维数据格式[时间, 速度]
        symbol: "none", // 去除数据点
        lineStyle: {
          width: 1, // 使用更细的线条
          color: "#000000", // 黑色
          type: "dashed", // 虚线样式
        },
        emphasis: {
          focus: "series",
        },
      });
    }

    // 添加选中对象的速度曲线
    if (selectedObjectIds && selectedObjectIds.length > 0) {
      // 从后端返回的future_objects_info中提取数据
      selectedObjectIds.forEach((objId, idx) => {
        const objIndex = parseInt(objId.split("_")[1]); // 从id中提取索引
        const objInfo = (window as any).futureObjectsInfo?.[objIndex];
        if (objInfo && objInfo.timestamp && objInfo.projected_vel) {
          series.push({
            name: `Obj ${objIndex} 速度`,
            type: "line",
            data: objInfo.projected_vel.map((v: number, i: number) => [
              objInfo.timestamp[i],
              v,
            ]),
            symbol: "none",
            lineStyle: {
              width: 2,
              color: colorPalette[objIndex % colorPalette.length], // 使用统一颜色表
            },
            emphasis: {
              focus: "series",
            },
          });
        }
      });
    }

    // 更新图例
    const legendData = dlpTrajs.map((_, index) => `轨迹 ${index}`);
    if (egoTrajVel && egoTrajVel.length > 0) {
      legendData.push("Ego轨迹速度");
    }

    // 添加选中对象的图例
    if (selectedObjectIds && selectedObjectIds.length > 0) {
      selectedObjectIds.forEach((objId) => {
        const objIndex = parseInt(objId.split("_")[1]);
        legendData.push(`Obj ${objIndex} 速度`);
      });
    }

    return {
      title: { text: "速度曲线", left: "center", textStyle: { fontSize: 14 } },
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "cross" },
      },
      legend: {
        data: legendData,
        top: 15,
        type: "scroll",
      },
      grid: {
        left: "10%",
        right: "10%",
        bottom: "15%",
        top: "25%",
      },
      xAxis: {
        type: "value", // 修改为value类型以支持时间轴
        name: "时间 (s)",
      },
      yAxis: {
        type: "value",
        name: "速度 (m/s)",
      },
      series,
    };
  };

  // 生成加速度曲线图配置
  const getAccelerationChartOption = () => {
    if (!dlpTrajs || dlpTrajs.length === 0) {
      return {
        title: { text: "加速度曲线", left: "center" },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value", name: "加速度 (m/s²)" },
        series: [],
      };
    }

    // 生成时间轴数据，每个点间隔0.2秒

    const series = dlpTrajs.map((traj, index) => ({
      name: `轨迹 ${index}`,
      type: "line",
      data: traj.acc.map((a, idx) => [idx * 0.2, a]),
      symbol: "none", // 去除数据点
      lineStyle: {
        width: highlightTrajIndex === index ? 6 : 2,
        // color: highlightTrajIndex === index ? "#ff4d4f" : undefined,
      },
      emphasis: {
        focus: "series",
      },
    }));

    return {
      title: {
        text: "加速度曲线",
        left: "center",
        textStyle: { fontSize: 14 },
      },
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "cross" },
      },
      legend: {
        data: dlpTrajs.map((_, index) => `轨迹 ${index}`),
        top: 15,
        type: "scroll",
      },
      grid: {
        left: "10%",
        right: "10%",
        bottom: "15%",
        top: "25%",
      },
      xAxis: {
        type: "value",
        name: "时间步",
      },
      yAxis: {
        type: "value",
        name: "加速度 (m/s²)",
      },
      series,
    };
  };

  // 生成距离曲线图配置
  const getDistanceChartOption = () => {
    if (!dlpTrajs || dlpTrajs.length === 0) {
      return {
        title: { text: "距离曲线", left: "center" },
        xAxis: { type: "category", data: [] },
        yAxis: { type: "value", name: "距离 (m)" },
        series: [],
      };
    }

    // 生成时间轴数据，每个点间隔0.2秒
    // DLP轨迹系列
    const series: any = dlpTrajs.map((traj, index) => ({
      name: `轨迹 ${index}`,
      type: "line",
      data: traj.s.map((s, idx) => [idx * 0.2, s]),
      symbol: "none", // 去除数据点
      lineStyle: {
        width: highlightTrajIndex === index ? 6 : 2,
        // color: highlightTrajIndex === index ? "#ff4d4f" : undefined,
      },
      emphasis: {
        focus: "series",
      },
    }));

    // 添加ego轨迹长度系列
    if (egoTrajLength && egoTrajLength.length > 0) {
      series.push({
        name: "Ego轨迹长度",
        type: "line",
        data: egoTrajLength.map((s, idx) => [idx * 0.2, s]),
        symbol: "none", // 去除数据点
        lineStyle: {
          width: 1, // 使用更细的线条
          color: "#000000", // 黑色
          type: "dashed", // 虚线样式
        },
        emphasis: {
          focus: "series",
        },
      });
    }

    // 添加选中对象的距离曲线
    if (selectedObjectIds && selectedObjectIds.length > 0) {
      // 从后端返回的future_objects_info中提取数据
      selectedObjectIds.forEach((objId, idx) => {
        const objIndex = parseInt(objId.split("_")[1]); // 从id中提取索引
        const objInfo = (window as any).futureObjectsInfo?.[objIndex];
        if (objInfo && objInfo.timestamp && objInfo.projected_s) {
          series.push({
            name: `Obj ${objIndex} 距离`,
            type: "line",
            data: objInfo.projected_s.map((s: number, i: number) => [
              objInfo.timestamp[i],
              s,
            ]),
            symbol: "none",
            lineStyle: {
              width: 2,
              color: colorPalette[objIndex % colorPalette.length], // 使用统一颜色表
            },
            emphasis: {
              focus: "series",
            },
          });
        }
      });
    }

    // 更新图例
    const legendData = dlpTrajs.map((_, index) => `轨迹 ${index}`);
    if (egoTrajLength && egoTrajLength.length > 0) {
      legendData.push("Ego轨迹长度");
    }

    // 添加选中对象的图例
    if (selectedObjectIds && selectedObjectIds.length > 0) {
      selectedObjectIds.forEach((objId) => {
        const objIndex = parseInt(objId.split("_")[1]);
        legendData.push(`Obj ${objIndex} 距离`);
      });
    }

    return {
      title: { text: "距离曲线", left: "center", textStyle: { fontSize: 14 } },
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "cross" },
      },
      legend: {
        data: legendData,
        top: 15,
        type: "scroll",
      },
      grid: {
        left: "10%",
        right: "10%",
        bottom: "15%",
        top: "25%",
      },
      xAxis: {
        type: "value",
        name: "时间步",
      },
      yAxis: {
        type: "value",
        name: "距离 (m)",
      },
      series,
    };
  };

  const changeIds = async (ids: string[]) => {
    console.log("ids", ids);
    setSelectedObjectIds(ids);

    // 如果有选中的 PKL 文件，保存到后端
    if (selectedPkl) {
      try {
        // console.log("selectedPkl", selectedPkl.id);
        await axios.post("/api/annotation/dlp-selected-objects", {
          pkl_id: selectedPkl.id,
          selected_object_ids: ids,
          evaluation_set_id: id,
          employee_id: user?.employee_id || null,
        });
        // 手动触发图表重新渲染
        setTimeout(() => {
          if (velocityChartRef.current) {
            velocityChartRef.current
              .getEchartsInstance()
              .setOption(getVelocityChartOption(), true);
          }
          if (distanceChartRef.current) {
            distanceChartRef.current
              .getEchartsInstance()
              .setOption(getDistanceChartOption(), true);
          }
        }, 0);
      } catch (error) {
        console.error("保存选中对象失败:", error);
        message.error("保存选中对象失败");
      }
    }
  };

  return (
    <div
      className="pdp-path-annotation-layout"
      style={{ display: "flex", height: "calc(100vh - 50px)" }}
    >
      {/* 左侧PKL列表 */}
      <ResizableSider
        width={leftSiderWidth}
        minWidth={400}
        maxWidth={600}
        onResize={setLeftSiderWidth}
        position="left"
        className="pkl-list-sider"
      >
        <div className="pkl-list-header" style={{ flexShrink: 0 }}>
          <Space direction="vertical" style={{ width: "100%" }} size="small">
            {evaluationSet && (
              <div
                style={{
                  marginBottom: "4px",
                  padding: "4px 6px",
                  background: "#f0f0f0",
                  borderRadius: "3px",
                  border: "1px solid #d9d9d9",
                }}
              >
                <Text
                  strong
                  style={{
                    fontSize: "12px",
                    color: "#1890ff",
                    lineHeight: "1.2",
                  }}
                >
                  {evaluationSet.set_name}
                </Text>
              </div>
            )}
            <Space direction="horizontal" style={{ width: "100%" }}>
              <Input
                placeholder="输入员工ID进行筛选"
                prefix={<SearchOutlined />}
                value={employeeId}
                onChange={(e) => setEmployeeId(e.target.value)}
                onPressEnter={handleSearch}
                suffix={
                  employeeId && (
                    <Button
                      type="text"
                      size="small"
                      icon={<ClearOutlined />}
                      onClick={() => {
                        setEmployeeId("");
                        if (showCheckDlpAnnotation) {
                          checkPklList(
                            1,
                            pklPagination.pageSize,
                            "", // search固定为空
                            showFullyAnnotatedOnly,
                            checkStatusFilter,
                            bagName,
                            timeNs,
                            timeRange,
                            "",
                            tagFilter
                          );
                        } else {
                          loadPklList(
                            1,
                            pklPagination.pageSize,
                            "", // search固定为空
                            showFullyAnnotatedOnly,
                            checkStatusFilter,
                            bagName,
                            timeNs,
                            timeRange,
                            "",
                            tagFilter
                          );
                        }
                      }}
                      style={{ padding: 0, minWidth: "auto" }}
                    />
                  )
                }
              />
              {/* <div style={{ fontSize: "10px", color: "#999", marginBottom: "8px" }}>
              只显示该员工最后修改的PKL（留空显示全部）
            </div> */}
              <Input
                placeholder="过滤（与：& 或：|）"
                prefix={<CommentOutlined />}
                value={tagFilter}
                onChange={(e) => setTagFilter(e.target.value)}
                onPressEnter={handleSearch}
                style={{ flex: 1 }}
                suffix={
                  tagFilter && (
                    <Button
                      type="text"
                      size="small"
                      icon={<ClearOutlined />}
                      onClick={() => {
                        setTagFilter("");
                        // 清空后立即触发搜索
                        loadPklList(
                          1,
                          pklPagination.pageSize,
                          "", // search固定为空
                          showFullyAnnotatedOnly,
                          checkStatusFilter,
                          bagName,
                          timeNs,
                          timeRange,
                          employeeId,
                          "" // 清空tag_filter
                        );
                      }}
                      style={{ padding: 0, minWidth: "auto" }}
                    />
                  )
                }
              />
            </Space>
            <div
              style={{
                border: "1px solid #d9d9d9",
                padding: "6px",
                borderRadius: "4px",
              }}
            >
              <Text strong style={{ fontSize: "11px", color: "#666" }}>
                bag_name时间戳过滤:
              </Text>
              <Input
                placeholder="bag_name"
                value={bagName}
                onChange={(e) => setBagName(e.target.value)}
                style={{ marginTop: "6px" }}
              />
              <Input
                placeholder="时间戳(纳秒)"
                value={timeNs}
                onChange={(e) => setTimeNs(e.target.value)}
                style={{ marginTop: "6px" }}
              />
              <Input
                placeholder="时间范围(秒)"
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                style={{ marginTop: "6px" }}
              />
              <Space
                style={{
                  marginTop: "6px",
                  width: "100%",
                  display: "flex",
                  flexFlow: "row-reverse",
                }}
              >
                <Button
                  type="primary"
                  onClick={handleSearch}
                  style={{ flex: 1 }}
                >
                  应用过滤
                </Button>
                <Button onClick={handleClearBagTimeFilter} style={{ flex: 1 }}>
                  清空
                </Button>
              </Space>
            </div>

            {/* 其他过滤按钮保持不变 */}
            <Space style={{ marginTop: "4px" }}>
              <Button
                type={showFullyAnnotatedOnly ? "primary" : "default"}
                icon={<CheckCircleOutlined />}
                onClick={handleFilterToggle}
              >
                {showFullyAnnotatedOnly ? "显示全部" : "仅显示完全标注"}
              </Button>
              <Radio.Group
                value={checkStatusFilter}
                onChange={(e) => handleCheckStatusFilter(e.target.value)}
                buttonStyle="solid"
              >
                <Radio.Button value="all">全部</Radio.Button>
                <Radio.Button value="checked">已检查</Radio.Button>
                <Radio.Button value="unchecked">未检查</Radio.Button>
              </Radio.Group>
            </Space>
            <Space style={{ marginTop: "4px" }}>
              <Button
                type="default"
                icon={<ExportOutlined />}
                onClick={handleExportAnnotations}
                style={{ width: "100%", marginTop: "4px" }}
              >
                导出DLP标注数据
              </Button>
              <Button
                type={showCheckDlpAnnotation ? "primary" : "default"}
                loading={loading.checkAnnotation}
                icon={<QuestionCircleOutlined />}
                onClick={handleCheckDlpAnnotations}
                style={{ width: "100%", marginTop: "4px" }}
              >
                检查DLP标注数据
              </Button>
            </Space>
          </Space>
        </div>

        <div
          className="pkl-list-main"
          style={{
            position: "relative",
            height: "calc(100vh - 485px)", // 保持原有高度
            display: "flex",
            flexDirection: "column",
            padding: "0 16px",
          }}
        >
          {/* List组件容器 */}
          <div
            style={{
              flex: 1,
              overflowY: "auto",
              paddingBottom: "50px", // 为分页组件预留空间
            }}
          >
            <List
              loading={loading.pklList}
              dataSource={pklList}
              renderItem={(item) => (
                <List.Item
                  className={
                    selectedPkl?.id === item.id
                      ? "pkl-item selected"
                      : "pkl-item"
                  }
                  onClick={() => handlePklSelect(item)}
                  style={{ display: "flex", alignItems: "flex-start" }}
                >
                  <div
                    className="pkl-item-content"
                    style={{ flex: 1, minWidth: 0 }}
                  >
                    <div
                      className="pkl-name"
                      style={{
                        fontSize: "11px",
                        lineHeight: "1.3",
                        wordBreak: "break-all",
                        whiteSpace: "normal",
                        maxWidth: "100%",
                        marginBottom: "4px",
                      }}
                    >
                      {item.pkl_name}
                      {item.dirty_data && (
                        <Tag
                          color="red"
                          style={{ marginLeft: 5, fontSize: "10px" }}
                        >
                          <WarningOutlined /> 脏数据
                        </Tag>
                      )}
                      {item.is_checked && (
                        <Tag
                          color="green"
                          style={{ marginLeft: 5, fontSize: "10px" }}
                        >
                          <CheckOutlined /> 已检查
                        </Tag>
                      )}
                    </div>
                  </div>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: 4,
                      marginLeft: 8,
                    }}
                  >
                    <Button
                      type={item.is_checked ? "primary" : "default"}
                      icon={<CheckOutlined />}
                      size="small"
                      loading={loading.checkPkl}
                      onClick={(e) => handleTogglePklCheck(item, e)}
                      title={item.is_checked ? "取消检查标记" : "标记为已检查"}
                      style={{
                        flexShrink: 0,
                        backgroundColor: item.is_checked
                          ? "#52c41a"
                          : undefined,
                        borderColor: item.is_checked ? "#52c41a" : undefined,
                      }}
                    />
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        const fullPath = `${item.pkl_dir}/${item.pkl_name}`;
                        copyToClipboard(fullPath);
                      }}
                      title="复制文件路径"
                      style={{ flexShrink: 0 }}
                    />
                  </div>
                </List.Item>
              )}
            />
          </div>

          {/* 分页组件 - 使用绝对定位固定在底部 */}
          <div
            className="pkl-pagination"
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
              height: "40px",
              padding: "6px 16px",
              borderTop: "1px solid #f0f0f0",
              backgroundColor: "#fafafa",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              zIndex: 10,
            }}
          >
            <Pagination
              current={pklPagination.current}
              pageSize={pklPagination.pageSize}
              total={pklPagination.total}
              onChange={handlePklPageChange}
              showSizeChanger={false}
              size="small"
              simple
            />
          </div>
        </div>
      </ResizableSider>

      {/* 中间可视化区域 */}
      <div
        className="visualization-content"
        style={{
          flex: 1,
          background: "#fff",
          borderRight: "1px solid #f0f0f0",
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {selectedPkl ? (
          <div style={{ width: "100%", height: "100%", padding: "2px" }}>
            {/* 显示最新的标注信息 */}
            {latestAnnotationInfo && (
              <div
                style={{
                  padding: "2px",
                  background: "#f6ffed",
                  borderBottom: "1px solid #f0f0f0",
                  fontSize: "12px",
                  marginBottom: "2px",
                }}
              >
                <Text strong>最新标注信息：</Text>
                <Text> 标注员 {latestAnnotationInfo.employee_id} </Text>
                <Text>
                  于{" "}
                  {new Date(latestAnnotationInfo.updated_at).toLocaleString(
                    "zh-CN"
                  )}{" "}
                  更新
                </Text>
              </div>
            )}

            {loading.trajectories ? (
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "100%",
                }}
              >
                <Spin tip="加载轨迹数据..." />
              </div>
            ) : (
              <>
                <Row gutter={[16, 16]} style={{ height: "30%" }}>
                  <Col span={8} style={{ height: "100%" }}>
                    <Card
                      size="small"
                      style={{ height: "100%" }}
                      styles={{
                        body: {
                          height: "calc(100% - 20px)",
                          padding: "2px",
                        },
                      }}
                    >
                      <ReactECharts
                        ref={velocityChartRef}
                        option={getVelocityChartOption()}
                        style={{ height: "100%", width: "100%" }}
                      />
                    </Card>
                  </Col>
                  <Col span={8} style={{ height: "100%" }}>
                    <Card
                      size="small"
                      style={{ height: "100%" }}
                      styles={{
                        body: {
                          height: "calc(100% - 20px)",
                          padding: "2px",
                        },
                      }}
                    >
                      <ReactECharts
                        option={getAccelerationChartOption()}
                        style={{ height: "100%", width: "100%" }}
                      />
                    </Card>
                  </Col>
                  <Col span={8} style={{ height: "100%" }}>
                    <Card
                      size="small"
                      style={{ height: "100%" }}
                      styles={{
                        body: {
                          height: "calc(100% - 20px)",
                          padding: "2px",
                        },
                      }}
                    >
                      <ReactECharts
                        ref={distanceChartRef}
                        option={getDistanceChartOption()}
                        style={{ height: "100%", width: "100%" }}
                      />
                    </Card>
                  </Col>
                </Row>
                <div style={{ height: "70%", marginTop: "1px" }}>
                  <PickleVisualizer
                    evaluationCase={selectedPkl}
                    highlightPathIndex={highlightPathIndex}
                    pdpPaths={pdpPaths}
                    dlpTrajs={dlpTrajs}
                    highlightTrajIndex={highlightTrajIndex}
                    height="55vh"
                    changeIds={changeIds}
                    selectedObjectIds={selectedObjectIds}
                  />
                </div>
              </>
            )}
          </div>
        ) : (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100%",
              color: "#999",
            }}
          >
            <Text>请从左侧选择一个PKL文件</Text>
          </div>
        )}
      </div>

      {/* 右侧标注面板 */}
      <ResizableSider
        width={rightSiderWidth}
        minWidth={250}
        maxWidth={500}
        onResize={setRightSiderWidth}
        position="right"
        className="annotation-sider"
      >
        <div className="annotation-panel">
          <div className="annotation-header">
            {selectedPkl && (
              <div className="bad-data-button-container">
                <Button
                  danger={!isDirtyData}
                  type={isDirtyData ? "default" : "primary"}
                  icon={<WarningOutlined />}
                  onClick={handleMarkAsDirty}
                  loading={loading.markDirty}
                  size="small"
                >
                  {isDirtyData ? "取消脏数据标记" : "标记为脏数据"}
                </Button>
              </div>
            )}

            {/* 标签编辑区域 - 移到顶部 */}
            {selectedPkl && (
              <div
                style={{
                  padding: "8px",
                  background: "#f9f9f9",
                  borderRadius: "4px",
                  border: "1px solid #d9d9d9",
                  marginBottom: "8px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "6px",
                  }}
                >
                  <Text strong style={{ fontSize: "11px", marginRight: "8px" }}>
                    PKL标签:
                  </Text>
                  <Button
                    type="primary"
                    size="small"
                    onClick={handleSaveTag}
                    loading={loading.savingTag}
                    style={{ marginLeft: "auto" }}
                  >
                    保存
                  </Button>
                </div>
                <Input.TextArea
                  value={pklTag}
                  onChange={(e) => setPklTag(e.target.value)}
                  placeholder="输入标签内容..."
                  rows={2}
                  style={{ fontSize: "11px" }}
                />
              </div>
            )}

            {selectedPkl && !loading.trajectories && dlpTrajs.length > 0 && (
              <div className="annotation-stats">
                {/* 压缩后的统计信息 - 更紧凑的布局 */}
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(3, 1fr)", // 三列
                    gap: "2px", // 进一步减小间距
                    marginBottom: "6px", // 减小下边距
                  }}
                >
                  {[
                    {
                      level: "0",
                      count: annotationStats.level0,
                      color: "#722ed1",
                    },
                    {
                      level: "1",
                      count: annotationStats.level1,
                      color: "#1890ff",
                    },
                    {
                      level: "2",
                      count: annotationStats.level2,
                      color: "#13c2c2",
                    },
                    {
                      level: "3",
                      count: annotationStats.level3,
                      color: "#52c41a",
                    },
                    {
                      level: "4",
                      count: annotationStats.level4,
                      color: "#fa8c16",
                    },
                    {
                      level: "5",
                      count: annotationStats.level5,
                      color: "#f5222d",
                    },
                  ].map(({ level, count, color }) => (
                    <div
                      key={level}
                      style={{
                        textAlign: "center",
                        padding: "2px", // 进一步减小内边距
                        background: "transparent",
                        color: color,
                        borderRadius: "2px",
                        fontSize: "10px", // 减小字体
                        border: `1px solid ${color}`,
                      }}
                    >
                      {level}: <strong>{count}</strong>
                    </div>
                  ))}
                </div>

                {/* 总体统计 - 更紧凑 */}
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    padding: "4px", // 进一步减小内边距
                    background: "#f5f5f5",
                    borderRadius: "2px",
                    fontSize: "10px", // 减小字体
                  }}
                >
                  <span>
                    总计: <strong>{annotationStats.total}</strong>
                  </span>
                  <span>
                    已标注: <strong>{annotationStats.annotated}</strong>
                  </span>
                </div>
              </div>
            )}
          </div>

          {!selectedPkl ? (
            <div className="empty-annotation">
              <Text>请先选择一个PKL文件</Text>
            </div>
          ) : loading.trajectories ? (
            <div className="loading-paths">
              <Spin tip="加载轨迹数据..." />
            </div>
          ) : dlpTrajs.length === 0 ? (
            <div className="no-paths">
              <Text>未找到DLP轨迹数据</Text>
            </div>
          ) : (
            <div
              className="paths-list"
              style={{
                height: "calc(100% - 160px)", // 调整高度以适应新布局
                overflowY: "auto",
              }}
            >
              {dlpTrajs.map((traj) => (
                <div className="path-card-container" key={traj.index}>
                  <Card
                    className={
                      highlightTrajIndex === traj.index
                        ? "path-card highlighted"
                        : "path-card"
                    }
                    size="small"
                    onClick={() => handleHighlightTraj(traj.index)}
                    hoverable
                    style={{ marginBottom: "6px" }} // 减小间距
                  >
                    <div className="path-info">
                      <div
                        className="path-title"
                        style={{ textAlign: "center", marginBottom: "6px" }}
                      >
                        {highlightTrajIndex === traj.index ? (
                          <Tag color="blue" style={{ fontSize: "11px" }}>
                            轨迹 {traj.index}
                          </Tag>
                        ) : (
                          <span style={{ fontSize: "11px" }}>
                            轨迹 {traj.index}
                          </span>
                        )}
                      </div>
                      <div
                        className="path-actions"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div
                          style={{
                            display: "grid",
                            gridTemplateColumns: "1fr 1fr 1fr", // 三列
                            gridTemplateRows: "1fr 1fr", // 两行
                            gap: "4px", // 减小间距
                          }}
                        >
                          {["0", "1", "2", "3", "4", "5"].map((level) => (
                            <Button
                              key={level}
                              type={
                                traj.annotation?.annotation === level
                                  ? "primary"
                                  : "default"
                              }
                              size="small" // 使用小尺寸按钮
                              style={{
                                height: "28px", // 减小高度
                                fontSize: "12px", // 减小字体
                                fontWeight: "bold",
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAnnotate(traj.index, level);
                              }}
                            >
                              {level}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>
              ))}
            </div>
          )}
        </div>
      </ResizableSider>
    </div>
  );
};

export default DlpPathAnnotation;
