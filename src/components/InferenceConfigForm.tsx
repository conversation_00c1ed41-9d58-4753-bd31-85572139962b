// src/components/InferenceConfigForm.tsx
import React, { useState } from 'react';
import axios from 'axios';
import { Form, Input, Button, Alert, Spin } from 'antd';
import { InfoCircleOutlined, UploadOutlined } from '@ant-design/icons';

interface InferenceConfigFormProps {
    onConfigCreated: (configId: number) => void;
}

// 辅助函数：截断长路径
const shortenPath = (path: string): string => {
    if (!path || path.length <= 40) return path;
    const parts = path.split('/');
    if (parts.length <= 3) return path;

    return `/${parts[1]}/.../${parts[parts.length - 1]}`;
};

const InferenceConfigForm: React.FC<InferenceConfigFormProps> = ({ onConfigCreated }) => {
    const [form] = Form.useForm();
    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState('');

    const handleSubmit = async (values: any) => {
        if (!values.jsonFilePath || !values.pthFilePath) {
            setError('请输入JSON配置文件和PTH模型文件的路径');
            return;
        }

        setUploading(true);
        setError('');

        try {
            const formData = new FormData();
            formData.append('json_file_path', values.jsonFilePath);
            formData.append('pth_file_path', values.pthFilePath);

            const response = await axios.post('/api/inference_config', formData);

            if (response.data.success) {
                onConfigCreated(response.data.id);
                form.resetFields();
            } else {
                setError(response.data.error || '上传失败');
            }
        } catch (error: any) {
            console.error('上传失败:', error);
            setError(error.response?.data?.detail || '上传文件路径失败，请重试');
        } finally {
            setUploading(false);
        }
    };

    return (
        <div className="inference-config-form">
            <h3>上传新推理配置</h3>

            {error && (
                <Alert
                    message={error}
                    type="error"
                    showIcon
                    className="error-alert"
                />
            )}

            <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                disabled={uploading}
            >
                <Form.Item
                    label="JSON配置文件路径"
                    name="jsonFilePath"
                    tooltip={{ title: '服务器上JSON文件的完整路径', icon: <InfoCircleOutlined /> }}
                    rules={[{ required: true, message: '请输入JSON文件路径' }]}
                >
                    <Input placeholder="/path/to/config.json" />
                </Form.Item>

                <Form.Item
                    label="PTH模型文件路径"
                    name="pthFilePath"
                    tooltip={{ title: '服务器上PTH文件的完整路径', icon: <InfoCircleOutlined /> }}
                    rules={[{ required: true, message: '请输入PTH模型文件路径' }]}
                >
                    <Input placeholder="/path/to/model.pth" />
                </Form.Item>

                <Form.Item>
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={uploading}
                        icon={<UploadOutlined />}
                        block
                    >
                        提交配置
                    </Button>
                </Form.Item>
            </Form>

            <div className="form-tips">
                提示: 请确保输入的是服务器上可访问的文件路径
            </div>
        </div>
    );
};

export default InferenceConfigForm;