import React from 'react';
import { Radio, Checkbox, Spin, List, Typography, Divider, Tag, Pagination, Button, message, Space } from 'antd';
import { useNavigate } from 'react-router-dom';
import './InferenceConfigSelector.css';

const { Title, Text } = Typography;

interface InferenceConfig {
    id: number;
    json_name: string;
    pth_name: string;
    pth_upload_time: string;
}

interface InferenceConfigSelectorProps {
    configs: InferenceConfig[];
    selectedConfig: number | null; // 单选模式下的选中配置ID
    selectedConfigs?: number[]; // 多选模式下的选中配置ID数组
    onConfigSelect: (configId: number) => void; // 单选模式的回调
    onConfigsSelect?: (configIds: number[]) => void; // 多选模式的回调
    loading: boolean;
    mode?: 'single' | 'multiple'; // 选择模式: 单选 or 多选
    pagination?: { // 分页信息
        current: number;
        pageSize: number;
        total: number;
        onChange: (page: number, pageSize: number) => void;
    };
    evaluationSetId?: string; // 添加评测集ID属性用于构建跳转路径
}

const InferenceConfigSelector: React.FC<InferenceConfigSelectorProps> = ({
    configs,
    selectedConfig,
    selectedConfigs = [],
    onConfigSelect,
    onConfigsSelect,
    loading,
    mode = 'single',
    pagination,
    evaluationSetId,
}) => {
    const navigate = useNavigate();

    if (loading) {
        return (
            <div className="config-selector-loading">
                <Spin size="small" />
                <span>加载推理配置中...</span>
            </div>
        );
    }

    // 按上传时间对配置进行分组和排序
    const groupedConfigs = configs.reduce((acc, config) => {
        const dateOnly = config.pth_upload_time.split('T')[0];
        if (!acc[dateOnly]) {
            acc[dateOnly] = [];
        }
        acc[dateOnly].push(config);
        return acc;
    }, {} as Record<string, InferenceConfig[]>);

    // 将日期按降序排序（最新的日期在最前面）
    const sortedDates = Object.keys(groupedConfigs).sort((a, b) =>
        new Date(b).getTime() - new Date(a).getTime()
    );

    // 处理多选模式下的选中项变化
    const handleMultipleSelect = (configId: number, checked: boolean) => {
        if (!onConfigsSelect) return;

        const newSelectedConfigs = checked
            ? [...selectedConfigs, configId]
            : selectedConfigs.filter(id => id !== configId);

        onConfigsSelect(newSelectedConfigs);
    };

    // 处理定性对比按钮点击
    const handleQuantitativeCompare = () => {
        if (selectedConfigs.length < 2) {
            message.warning('请至少选择2个推理配置进行定性对比');
            return;
        }

        navigate(`/evaluation_sets/${evaluationSetId}/quantitative_compare`, {
            state: { configIds: selectedConfigs }
        });
    };

    // 处理图片对比按钮点击
    const handleImageCompare = () => {
        if (selectedConfigs.length < 2) {
            message.warning('请至少选择2个推理配置进行图片对比');
            return;
        }

        if (selectedConfigs.length > 4) {
            message.warning('图片对比最多只能选择4个推理配置');
            return;
        }

        navigate(`/evaluation_sets/${evaluationSetId}/image_compare`, {
            state: { configIds: selectedConfigs }
        });
    };

    return (
        <div className="inference-config-selector">
            <Title level={4}>推理配置选择</Title>
            <Text type="secondary">
                {mode === 'single' ? '选择一个推理配置以查看评测指标' : '选择多个推理配置进行比较'}
            </Text>

            {configs.length === 0 ? (
                <div className="config-empty-state">
                    <Text>暂无可用的推理配置</Text>
                </div>
            ) : (
                    <>
                        {mode === 'single' ? (
                            <Radio.Group
                                onChange={(e) => onConfigSelect(e.target.value)}
                                value={selectedConfig}
                                className="config-radio-group"
                            >
                            {renderConfigList()}
                        </Radio.Group>
                    ) : (
                        <>
                            <div className="config-checkbox-group">
                                {renderConfigList(true)}
                            </div>

                            {/* 添加对比按钮 */}
                            <div className="comparison-buttons">
                                <Space>
                                    <Button
                                        type="primary"
                                        onClick={handleQuantitativeCompare}
                                        disabled={selectedConfigs.length < 2}
                                    >
                                        定性对比
                                    </Button>
                                    <Button
                                        type="primary"
                                        onClick={handleImageCompare}
                                        disabled={selectedConfigs.length < 2 || selectedConfigs.length > 4}
                                    >
                                        图片对比（最多4个）
                                    </Button>
                                </Space>
                            </div>
                        </>
                    )}

                    {pagination && (
                        <div className="config-pagination">
                            <Pagination
                                current={pagination.current}
                                pageSize={pagination.pageSize}
                                total={pagination.total}
                                onChange={pagination.onChange}
                                showSizeChanger={true}
                                showTotal={(total) => `共 ${total} 项`}
                            />
                        </div>
                    )}
                </>
            )}
        </div>
    );

    function renderConfigList(isMultiple = false) {
        return sortedDates.map(date => (
            <React.Fragment key={date}>
                <Divider orientation="left">
                    <Tag color="blue">{date}</Tag>
                </Divider>
                <List
                    size="small"
                    dataSource={groupedConfigs[date]}
                    renderItem={config => (
                        <List.Item className="config-list-item">
                            {isMultiple ? (
                                <Checkbox
                                    checked={selectedConfigs.includes(config.id)}
                                    onChange={(e) => handleMultipleSelect(config.id, e.target.checked)}
                                    className="config-checkbox"
                                >
                                    <ConfigInfo config={config} />
                                </Checkbox>
                            ) : (
                                    <Radio value={config.id} className="config-radio">
                                    <ConfigInfo config={config} />
                                </Radio>
                            )}
                        </List.Item>
                    )}
                />
            </React.Fragment>
        ));
    }

    function ConfigInfo({ config }: { config: InferenceConfig }) {
        return (
            <div className="config-info">
                <div className="config-name">
                    <Text strong ellipsis title={config.json_name}>
                        {config.json_name}
                    </Text>
                </div>
                <div className="config-model">
                    <Text type="secondary" ellipsis title={config.pth_name}>
                        模型：{config.pth_name}
                    </Text>
                </div>
                <div className="config-time">
                    <Text type="secondary">
                        {new Date(config.pth_upload_time).toLocaleTimeString('zh-CN')}
                    </Text>
                </div>
            </div>
        );
    }
};

export default InferenceConfigSelector;