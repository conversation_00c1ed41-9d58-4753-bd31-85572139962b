import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Drawer, Card, Progress, Button, Row, Col, Statistic, Alert, Divider, message } from 'antd';
import { CheckCircleOutlined, SyncOutlined, ExclamationCircleOutlined, RightOutlined, LeftOutlined } from '@ant-design/icons';
import axios from 'axios';
import './TaskProgressDrawer.css';

interface TaskProgress {
    taskId: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    message: string;
    progress: number;
    inferenceProgress: number;
    evaluationProgress: number;
    totalSets: number;
    completedSets: number;
    failedSets: number;
    results: any[];
    error?: string;
}

interface TaskProgressDrawerProps {
    taskId: string | null;
    configId?: number | null;
    setIds?: number[];
    visible: boolean;
    onClose: () => void;
    onTaskCompleted?: () => void;
}

const TaskProgressDrawer: React.FC<TaskProgressDrawerProps> = ({
    taskId,
    configId,
    setIds,
    visible,
    onClose,
    onTaskCompleted
}) => {
    const navigate = useNavigate();
    const [collapsed, setCollapsed] = useState(false);

    const [taskProgress, setTaskProgress] = useState<TaskProgress>({
        taskId: taskId || '',
        status: 'pending',
        message: '正在初始化评测任务...',
        progress: 0,
        inferenceProgress: 0,
        evaluationProgress: 0,
        totalSets: setIds?.length || 0,
        completedSets: 0,
        failedSets: 0,
        results: []
    });

    const [polling, setPolling] = useState(true);

    // 轮询任务状态
    useEffect(() => {
        if (!taskId || !visible) {
            return;
        }

        const pollTaskStatus = async () => {
            try {
                const response = await axios.get(`/api/task_status/${taskId}`);
                const { status, progress, results, message, inferenceProgress, evaluationProgress } = response.data;

                // 计算完成和失败的评测集数量
                const completedSets = results?.filter(r => r.success && r.evaluation?.success)?.length || 0;
                const failedSets = results?.filter(r => r.success === false || (r.evaluation !== undefined && r.evaluation.success === false))?.length || 0;
                const totalSets = completedSets + failedSets;

                setTaskProgress({
                    taskId,
                    status,
                    message,
                    progress: progress || 0,
                    inferenceProgress: inferenceProgress || 0,
                    evaluationProgress: evaluationProgress || 0,
                    totalSets: totalSets,
                    completedSets,
                    failedSets,
                    results: results || []
                });

                // 如果任务完成或失败，停止轮询
                if (status === 'completed' || status === 'failed') {
                    setPolling(false);

                    // 如果任务完成，通知父组件
                    if (status === 'completed' && onTaskCompleted) {
                        onTaskCompleted();
                    }
                }
            } catch (error: any) {
                console.error('获取任务状态失败:', error);
                setTaskProgress(prev => ({
                    ...prev,
                    message: '获取任务进度失败',
                    error: error.toString()
                }));
            }
        };

        // 初始查询
        pollTaskStatus();

        // 设置轮询间隔
        const intervalId = setInterval(pollTaskStatus, 3000);

        // 清理函数
        return () => {
            clearInterval(intervalId);
        };
    }, [taskId, setIds, visible, onTaskCompleted]);

    const handleViewResults = () => {
        if (setIds && setIds.length > 0) {
            navigate(`/evaluation-sets/${setIds[0]}`);
        }
    };

    const handleCancelTask = async () => {
        try {
            await axios.post(`/api/cancel_task/${taskId}`);
            setPolling(false);
            setTaskProgress(prev => ({
                ...prev,
                status: 'failed',
                message: '任务已取消'
            }));
            message.success('任务已取消');
        } catch (error) {
            console.error('取消任务失败:', error);
            message.error('取消任务失败');
        }
    };

    const getStatusIcon = () => {
        switch (taskProgress.status) {
            case 'completed':
                return <CheckCircleOutlined style={{ fontSize: 32, color: '#52c41a' }} />;
            case 'running':
                return <SyncOutlined spin style={{ fontSize: 32, color: '#1890ff' }} />;
            case 'failed':
                return <ExclamationCircleOutlined style={{ fontSize: 32, color: '#ff4d4f' }} />;
            default:
                return <SyncOutlined spin style={{ fontSize: 32, color: '#faad14' }} />;
        }
    };

    const toggleCollapse = () => {
        setCollapsed(!collapsed);
    };

    return (
        <Drawer
            title={
                <div className="drawer-title">
                    {getStatusIcon()}
                    <span style={{ marginLeft: 10 }}>评测任务进度</span>
                    <span className={`status-badge ${taskProgress.status}`}>
                        {taskProgress.status === 'running' && '处理中'}
                        {taskProgress.status === 'completed' && '已完成'}
                        {taskProgress.status === 'failed' && '失败'}
                        {taskProgress.status === 'pending' && '等待中'}
                    </span>
                </div>
            }
            placement="right"
            closable={true}
            onClose={onClose}
            visible={visible}
            width={collapsed ? 80 : 400}
            mask={false}
            className="task-progress-drawer"
            headerStyle={{ padding: collapsed ? '10px 10px' : '16px 24px' }}
            bodyStyle={{ padding: collapsed ? '10px' : '24px' }}
            extra={
                <Button
                    type="text"
                    onClick={toggleCollapse}
                    icon={collapsed ? <RightOutlined /> : <LeftOutlined />}
                />
            }
        >
            {!collapsed && (
                <>
                    {taskProgress.error && (
                        <Alert
                            message="错误"
                            description={taskProgress.error}
                            type="error"
                            showIcon
                            className="error-alert"
                            style={{ marginBottom: 16 }}
                        />
                    )}

                    <div className="progress-message">{taskProgress.message}</div>

                    <div className="progress-section">
                        <h4>总体进度</h4>
                        <Progress
                            percent={taskProgress.progress}
                            status={taskProgress.status === 'failed' ? 'exception' : undefined}
                        />
                    </div>

                    <Divider style={{ margin: '12px 0' }} />

                    <div className="progress-details">
                        <Row gutter={16}>
                            <Col span={12}>
                                <Card bordered={false} size="small" className="stat-card">
                                    <Statistic
                                        title="推理进度"
                                        value={taskProgress.inferenceProgress}
                                        suffix="%"
                                        precision={1}
                                        // size="small"
                                    />
                                    <Progress
                                        percent={taskProgress.inferenceProgress}
                                        size="small"
                                        showInfo={false}
                                    />
                                </Card>
                            </Col>
                            <Col span={12}>
                                <Card bordered={false} size="small" className="stat-card">
                                    <Statistic
                                        title="评测进度"
                                        value={taskProgress.evaluationProgress}
                                        suffix="%"
                                        precision={1}
                                        // size="small"
                                    />
                                    <Progress
                                        percent={taskProgress.evaluationProgress}
                                        size="small"
                                        showInfo={false}
                                    />
                                </Card>
                            </Col>
                        </Row>
                        <Card bordered={false} size="small" className="stat-card" style={{ marginTop: 10 }}>
                            <Statistic
                                title="评测集"
                                value={`${taskProgress.completedSets}/${taskProgress.totalSets}`}
                                valueStyle={{ color: '#3f8600' }}
                                // size="small"
                            />
                            {taskProgress.failedSets > 0 && (
                                <div className="error-stat">失败: {taskProgress.failedSets}</div>
                            )}
                        </Card>
                    </div>

                    <div className="action-buttons">
                        <Button
                            type="primary"
                            size="small"
                            onClick={handleViewResults}
                            disabled={taskProgress.status !== 'completed' || taskProgress.completedSets === 0}
                        >
                            查看结果
                        </Button>

                        {taskProgress.status === 'running' && (
                            <Button danger size="small" onClick={handleCancelTask}>
                                取消任务
                            </Button>
                        )}
                    </div>
                </>
            )}
            {collapsed && (
                <div className="collapsed-content">
                    <Progress
                        type="circle"
                        percent={taskProgress.progress}
                        width={40}
                        status={
                            taskProgress.status === 'failed'
                                ? 'exception'
                                : taskProgress.status === 'completed'
                                    ? 'success'
                                    : 'active'
                        }
                    />
                </div>
            )}
        </Drawer>
    );
};

export default TaskProgressDrawer;