// src/components/LoginForm.tsx
import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Divider } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, IdcardOutlined } from '@ant-design/icons';
import axios from 'axios';

interface LoginFormProps {
    onLoginSuccess: (user: any, token: string) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onLoginSuccess }) => {
    const [loading, setLoading] = useState(false);
    const [isRegister, setIsRegister] = useState(false);

    const handleSubmit = async (values: any) => {
        setLoading(true);
        try {
            const endpoint = isRegister ? '/api/auth/register' : '/api/auth/login';
            const response = await axios.post(endpoint, values);

            if (isRegister) {
                message.success('注册成功，请登录');
                setIsRegister(false);
            } else {
                localStorage.setItem('access_token', response.data.access_token);
                localStorage.setItem('user', JSON.stringify(response.data.user));
                message.success('登录成功');
                onLoginSuccess(response.data.user, response.data.access_token);
            }
        } catch (error: any) {
            message.error(error.response?.data?.detail || '操作失败');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="login-container">
            <Card title={isRegister ? "用户注册" : "用户登录"} className="login-card">
                <Form onFinish={handleSubmit} layout="vertical">
                    <Form.Item
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                    >
                        <Input
                            prefix={<UserOutlined />}
                            placeholder="用户名"
                            size="large"
                        />
                    </Form.Item>

                    {isRegister && (
                        <>
                            <Form.Item
                                name="employee_id"
                                rules={[{ required: true, message: '请输入工号' }]}
                            >
                                <Input
                                    prefix={<IdcardOutlined />}
                                    placeholder="工号"
                                    size="large"
                                />
                            </Form.Item>
                        </>
                    )}

                    <Form.Item
                        name="password"
                        rules={[{ required: true, message: '请输入密码' }]}
                    >
                        <Input.Password
                            prefix={<LockOutlined />}
                            placeholder="密码"
                            size="large"
                        />
                    </Form.Item>

                    <Form.Item>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            size="large"
                            block
                        >
                            {isRegister ? '注册' : '登录'}
                        </Button>
                    </Form.Item>
                </Form>

                <Divider />

                <div className="auth-switch">
                    <Button
                        type="link"
                        onClick={() => setIsRegister(!isRegister)}
                    >
                        {isRegister ? '已有账号？立即登录' : '没有账号？立即注册'}
                    </Button>
                </div>
            </Card>
        </div>
    );
};

export default LoginForm;