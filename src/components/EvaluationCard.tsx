// components/EvaluationCard.tsx
import React from 'react';

interface EvaluationCardProps {
    pklName: string;
    tag: string;
    image: string;
    onClick: () => void;
}

const EvaluationCard: React.FC<EvaluationCardProps> = ({ pklName, tag, image, onClick }) => {
    return (
        <article
            className="border rounded-2xl shadow-md p-4 w-full max-w-xs hover:shadow-lg cursor-pointer transition-shadow duration-200"
            onClick={onClick}
        >
            <div className="overflow-hidden rounded-xl mb-3">
                <img
                    src={image}
                    alt={`${pklName} 预览图`}
                    className="w-full h-32 object-cover hover:scale-105 transition-transform duration-300"
                />
            </div>
            <header>
                <h3 className="text-lg font-bold truncate">{pklName}</h3>
                <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-600">标签:</span>
                    <span className="ml-1 px-2 py-0.5 bg-gray-100 rounded-full text-xs">{tag}</span>
                </div>
            </header>
        </article>
    );
};

export default EvaluationCard;
