// EvaluationSetManager.jsx
import React, { useState, useEffect } from "react";
import {
  Table,
  Input,
  Button,
  Pagination,
  Card,
  Spin,
  message,
  Dropdown,
  Menu,
  Space,
} from "antd";
import {
  SearchOutlined,
  PlusOutlined,
  DownOutlined,
  ExportOutlined,
} from "@ant-design/icons";
import axios from "axios";
import "./EvaluationSetManager.css";
import { EvaluationSet } from "../types";
import EvaluationSetEditor from "./EvaluationSetEditor";
import ExportDlpAnnotationsModal from "./ExportDlpAnnotationsModal";

interface EvaluationSetManagerProps {
  onCreateNewSet: () => void;
  disabled?: boolean;
  extraColumns?: any[];
  onRowSelect?: (record: EvaluationSet) => void;
  selectedIds?: number[];
  selectionMode?: boolean; // 是否为选择模式
  showCreateButton?: boolean; // 是否显示创建按钮
}

const EvaluationSetManager: React.FC<EvaluationSetManagerProps> = ({
  onCreateNewSet,
  disabled,
  extraColumns = [],
  onRowSelect,
  selectedIds = [],
  selectionMode,
  showCreateButton = true,
}) => {
  // 状态管理
  const [evaluationSets, setEvaluationSets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    pages: 1,
  });
  const [filters, setFilters] = useState({
    name: "",
    creator: "",
  });
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentEditingSet, setCurrentEditingSet] =
    useState<EvaluationSet | null>(null);
  const [exportModalVisible, setExportModalVisible] = useState(false);

  const editSet = (record: EvaluationSet) => {
    setCurrentEditingSet(record);
    setEditModalVisible(true);
  };

  // 关闭修改弹窗
  const handleEditModalClose = () => {
    setEditModalVisible(false);
    setCurrentEditingSet(null);
  };

  // 修改完成后的回调
  const handleEditSuccess = () => {
    setEditModalVisible(false);
    setCurrentEditingSet(null);
    fetchEvaluationSets(pagination.current); // 刷新列表
  };

  // 加载评测集数据
  const fetchEvaluationSets = async (
    page = 1,
    nameFilter = filters.name,
    creatorFilter = filters.creator
  ) => {
    setLoading(true);
    try {
      const response = await axios.get("/api/evaluation_sets", {
        params: {
          page,
          per_page: pagination.pageSize,
          name: nameFilter || undefined,
          creator: creatorFilter || undefined,
        },
      });

      if (response.data.success) {
        setEvaluationSets(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.per_page,
          total: response.data.total,
          pages: response.data.pages,
        });
      } else {
        message.error("获取评测集失败: " + response.data.error);
      }
    } catch (error) {
      message.error("请求出错");
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和筛选条件变化时重新获取数据
  useEffect(() => {
    fetchEvaluationSets(1);
  }, []);

  // 处理分页变化
  const handlePageChange = (page: number) => {
    fetchEvaluationSets(page, filters.name, filters.creator);
  };

  // 处理筛选条件变化
  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
  };

  // 应用筛选
  const applyFilters = () => {
    fetchEvaluationSets(1, filters.name, filters.creator);
  };

  // 重置筛选
  const resetFilters = () => {
    setFilters({ name: "", creator: "" });
    fetchEvaluationSets(1, "", "");
  };

  // 创建标注下拉菜单
  const createAnnotationMenu = (record: EvaluationSet) => (
    <Menu>
      <Menu.Item
        key="pdp"
        onClick={(e) => {
          e.domEvent.stopPropagation();
          pdpAnnotateSet(record.id);
        }}
      >
        PDP路径标注
      </Menu.Item>

      <Menu.Item
        key="pair"
        onClick={(e) => {
          e.domEvent.stopPropagation();
          pairAnnotateSet(record.id);
        }}
      >
        路径Pair标注
      </Menu.Item>
      {/* <Menu.Item
        key="lane-scene"
        onClick={(e) => {
          e.domEvent.stopPropagation();
          laneSceneAnnotateSet(record.id);
        }}
      >
        车道级场景标注
      </Menu.Item> */}
      <Menu.Item
        key="dlp"
        onClick={(e) => {
          e.domEvent.stopPropagation();
          dlpAnnotateSet(record.id);
        }}
      >
        DLP路径标注
      </Menu.Item>
    </Menu>
  );

  // 表格列定义
  const baseColumns = [
    {
      title: "评测集名称",
      dataIndex: "set_name",
      key: "set_name",
      width: 200,
    },
    {
      title: "创建人",
      dataIndex: "creator_name",
      key: "creator_name",
      width: 120,
    },
    {
      title: "案例数量",
      dataIndex: "case_count",
      key: "case_count",
      width: 200,
      render: (_, record) => record.case_count || "计算中...",
    },
    {
      title: "操作",
      key: "action",
      width: 250,
      render: (_, record: EvaluationSet) => (
        <div className="action-buttons">
          <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              viewDetails(record.id);
            }}
          >
            查看
          </Button>

          <Dropdown overlay={createAnnotationMenu(record)} trigger={["click"]}>
            <Button type="link" onClick={(e) => e.stopPropagation()}>
              标注 <DownOutlined />
            </Button>
          </Dropdown>

          <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              viewPairStatistics(record.id);
            }}
          >
            统计
          </Button>

          <Button
            type="link"
            onClick={(e) => {
              e.stopPropagation();
              editSet(record);
            }}
          >
            修改
          </Button>

          <Button
            type="link"
            danger
            onClick={(e) => {
              e.stopPropagation();
              deleteSet(record.id);
            }}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  // 合并基础列和额外列
  const columns = [...extraColumns, ...baseColumns];

  // 查看详情
  const viewDetails = (id: number) => {
    window.location.href = `/evaluation-sets/${id}`;
  };

  // PDP路径标注
  const pdpAnnotateSet = (id: number) => {
    window.location.href = `/pdp-annotation/${id}`;
  };
  const dlpAnnotateSet = (id: number) => {
    window.location.href = `/dlp-annotation/${id}`;
  };
  // 车道级场景标注 - 新添加的函数
  const laneSceneAnnotateSet = (id: number) => {
    window.location.href = `/pdp-lane-scene-annotation/${id}`;
  };

  // 路径Pair标注
  const pairAnnotateSet = (id: number) => {
    window.location.href = `/path-pair-annotation/${id}`;
  };

  // const pdpClipAnnotateSet = (id: number) => {
  //     window.location.href = `/pdp-clip-annotation/${id}`;
  // };

  // 查看Pair标注统计
  const viewPairStatistics = (id: number) => {
    window.location.href = `/path-pair-statistics/${id}`;
  };

  // 删除评测集
  const deleteSet = async (id: number) => {
    if (window.confirm("确定要删除这个评测集吗？此操作不可撤销。")) {
      setLoading(true);
      try {
        const response = await axios.delete(`/api/evaluation_sets/${id}`);
        if (response.data.success) {
          message.success("评测集删除成功");
          fetchEvaluationSets(pagination.current);
        } else {
          message.error("删除失败: " + response.data.error);
        }
      } catch (error) {
        message.error("请求出错");
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="evaluation-set-container">
      <Card
        title="评测集管理"
        extra={
          <Space>
            <Button
              type="default"
              icon={<ExportOutlined />}
              onClick={() => setExportModalVisible(true)}
            >
              批量导出DLP标注
            </Button>
            {showCreateButton && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={onCreateNewSet}
                disabled={disabled}
              >
                创建评测集
              </Button>
            )}
          </Space>
        }
      >
        <div className="filter-section">
          <Input
            placeholder="按评测集名称筛选"
            value={filters.name}
            onChange={(e) => handleFilterChange("name", e.target.value)}
            style={{ width: 200, marginRight: 16 }}
            allowClear
          />
          <Input
            placeholder="按创建人筛选"
            value={filters.creator}
            onChange={(e) => handleFilterChange("creator", e.target.value)}
            style={{ width: 200, marginRight: 16 }}
            allowClear
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={applyFilters}
          >
            筛选
          </Button>
          <Button onClick={resetFilters} style={{ marginLeft: 8 }}>
            重置
          </Button>
        </div>

        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={evaluationSets}
            rowKey="id"
            pagination={false}
            style={{ marginTop: 16 }}
            locale={{ emptyText: "暂无数据" }}
            rowClassName={(record: EvaluationSet) => {
              return selectedIds.includes(record.id) ? "selected-row" : "";
            }}
            onRow={(record) => ({
              onClick: () => {
                if (onRowSelect) {
                  onRowSelect(record);
                }
              },
              style: {
                cursor: onRowSelect ? "pointer" : "default",
              },
            })}
          />

          {pagination.total > 0 && (
            <div className="pagination-container">
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                showSizeChanger={false}
                showTotal={(total) => `共 ${total} 条记录`}
              />
            </div>
          )}
        </Spin>
      </Card>
      {editModalVisible && currentEditingSet && (
        <EvaluationSetEditor
          visible={editModalVisible}
          evaluationSet={currentEditingSet}
          onClose={handleEditModalClose}
          onSuccess={handleEditSuccess}
        />
      )}

      <ExportDlpAnnotationsModal
        visible={exportModalVisible}
        onClose={() => setExportModalVisible(false)}
      />
    </div>
  );
};

export default EvaluationSetManager;
