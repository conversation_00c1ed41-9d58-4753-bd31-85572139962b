import React from 'react';
import {
    Panel,
    PanelResizeHandle
} from 'react-resizable-panels';
import './ResizableSider.css';

interface ResizableSiderProps {
    width: number;
    minWidth?: number;
    maxWidth?: number;
    position: 'left' | 'right';
    onResize: (width: number) => void;
    className?: string;
    children: React.ReactNode;
}

const ResizableSider: React.FC<ResizableSiderProps> = ({
    width,
    minWidth = 200,
    maxWidth = 600,
    position,
    onResize,
    className = '',
    children
}) => {
    // 计算初始百分比
    const initialSize = Math.min(Math.max(width, minWidth), maxWidth);

    // 调整大小时计算新宽度并调用onResize
    const handleResize = (size: number) => {
        // 将百分比转换为像素
        const containerWidth = document.querySelector('.pdp-path-annotation-layout')?.clientWidth || 1200;
        const actualWidth = Math.round((size / 100) * containerWidth);

        // 确保在范围内
        const constrainedWidth = Math.min(Math.max(actualWidth, minWidth), maxWidth);
        onResize(constrainedWidth);
    };

    return (
        <div
            className={`resizable-sider ${position} ${className}`}
            style={{ minWidth: `${minWidth}px`, maxWidth: `${maxWidth}px`, width: `${width}px` }}
        >
            <div className="resizable-handle-container">
                {position === 'left' ? (
                    <div
                        className="resizable-handle resizable-handle-right"
                        onMouseDown={(e) => {
                            // 实现自定义拖动逻辑
                            const startX = e.clientX;
                            const startWidth = width;

                            const handleMouseMove = (moveEvent: MouseEvent) => {
                                const delta = moveEvent.clientX - startX;
                                const newWidth = position === 'left'
                                    ? startWidth + delta
                                    : startWidth - delta;

                                const constrainedWidth = Math.min(Math.max(newWidth, minWidth), maxWidth);
                                onResize(constrainedWidth);
                            };

                            const handleMouseUp = () => {
                                document.removeEventListener('mousemove', handleMouseMove);
                                document.removeEventListener('mouseup', handleMouseUp);
                            };

                            document.addEventListener('mousemove', handleMouseMove);
                            document.addEventListener('mouseup', handleMouseUp);
                        }}
                    />
                ) : (
                    <div
                        className="resizable-handle resizable-handle-left"
                        onMouseDown={(e) => {
                            const startX = e.clientX;
                            const startWidth = width;

                            const handleMouseMove = (moveEvent: MouseEvent) => {
                                const delta = moveEvent.clientX - startX;
                                const newWidth = startWidth - delta;
                                const constrainedWidth = Math.min(Math.max(newWidth, minWidth), maxWidth);
                                onResize(constrainedWidth);
                            };

                            const handleMouseUp = () => {
                                document.removeEventListener('mousemove', handleMouseMove);
                                document.removeEventListener('mouseup', handleMouseUp);
                            };

                            document.addEventListener('mousemove', handleMouseMove);
                            document.addEventListener('mouseup', handleMouseUp);
                        }}
                    />
                )}
            </div>
            <div className="resizable-sider-content">
                {children}
            </div>
        </div>
    );
};

export default ResizableSider;