import React, { useState } from 'react';
import { Card, Spin, Statistic, Row, Col, List, Tag, Typography, Pagination, Tabs } from 'antd';
import { CaretRightOutlined } from '@ant-design/icons';
import './EvaluationResults.css';
import { EvaluationCase, EvaluationSet, EvaluationMetrics } from '../types';
const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface EvaluationResultsProps {
    evaluationSet: EvaluationSet | null;
    metrics: EvaluationMetrics | null;
    caseMetrics: EvaluationMetrics | null; // 单个案例的评测指标
    loading: {
        metrics: boolean;
        caseMetrics: boolean;
    };
    selectedCase: EvaluationCase | null;
    onCaseSelect: (caseItem: EvaluationCase) => void;
    onPageChange: (page: number, pageSize: number) => void;
    currentPage: number;
    pageSize: number;
    totalCount?: number;
    activeTab: string; // 当前激活的标签页
    onTabChange: (tab: string) => void; // 标签页切换处理函数
    activeCaseTab: string; // 当前案例的Tab状态
    onCaseTabChange: (tab: string) => void; // 当前案例Tab变更函数
}

// 场景标签颜色映射
const tagColorMap: Record<string, string> = {
    'EFFICIENCY_LCR': 'blue',
    'LEFT_TURN': 'green',
    'RIGHT_TURN': 'cyan',
    'NUDGE_STATIC_OBS': 'orange',
    'NUDGE_SLOW_OBS': 'gold',
    'ENV_CONSTRAINT': 'purple'
};

// 渲染指标统计组件
const renderMetrics = (metrics: EvaluationMetrics | null, loading: boolean, activeTab: string, onTabChange: (key: string) => void) => {
    if (loading) {
        return (
            <div className="metrics-loading">
                <Spin size="small" />
                <Text>加载评测指标中...</Text>
            </div>
        );
    }

    if (!metrics) {
        return <Text type="secondary">请选择推理配置以查看评测指标</Text>;
    }

    return (
        <Tabs activeKey={activeTab} onChange={onTabChange}>
            <TabPane tab="Top1" key="top1">
                <Row gutter={[16, 16]}>
                    <Col span={8}>
                        <Statistic title="ADE@40" value={metrics.ade_40?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@40" value={metrics.fde_40?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="ADE@200" value={metrics.ade_200?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@200" value={metrics.fde_200?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="ADE@4s" value={metrics.ade_4s?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@4s" value={metrics.fde_4s?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={24}>
                        <Statistic
                            title="静态碰撞率"
                            value={metrics.static_collision?.toFixed(3) || '0.000'}
                            suffix="%"
                            valueStyle={{ color: (metrics.static_collision || 0) > 10 ? '#cf1322' : '#3f8600' }}
                        />
                    </Col>
                </Row>
            </TabPane>
            <TabPane tab="Top3" key="top3">
                <Row gutter={[16, 16]}>
                    <Col span={8}>
                        <Statistic title="ADE@40" value={metrics.ade_40?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@40" value={metrics.fde_40?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="ADE@200" value={metrics.ade_200?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@200" value={metrics.fde_200?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="ADE@4s" value={metrics.ade_4s?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@4s" value={metrics.fde_4s?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={24}>
                        <Statistic
                            title="静态碰撞率"
                            value={metrics.static_collision?.toFixed(3) || '0.000'}
                            suffix="%"
                            valueStyle={{ color: (metrics.static_collision || 0) > 10 ? '#cf1322' : '#3f8600' }}
                        />
                    </Col>
                </Row>
            </TabPane>
            <TabPane tab="Top6" key="top6">
                <Row gutter={[16, 16]}>
                    <Col span={8}>
                        <Statistic title="ADE@40" value={metrics.ade_40?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@40" value={metrics.fde_40?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="ADE@200" value={metrics.ade_200?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@200" value={metrics.fde_200?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="ADE@4s" value={metrics.ade_4s?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={8}>
                        <Statistic title="FDE@4s" value={metrics.fde_4s?.toFixed(3) || '0.000'} suffix="m" />
                    </Col>
                    <Col span={24}>
                        <Statistic
                            title="静态碰撞率"
                            value={metrics.static_collision?.toFixed(3) || '0.000'}
                            suffix="%"
                            valueStyle={{ color: (metrics.static_collision || 0) > 10 ? '#cf1322' : '#3f8600' }}
                        />
                    </Col>
                </Row>
            </TabPane>
        </Tabs>
    );
};

const EvaluationResults: React.FC<EvaluationResultsProps> = ({
    evaluationSet,
    metrics,
    caseMetrics,
    loading,
    selectedCase,
    onCaseSelect,
    onPageChange,
    currentPage,
    pageSize,
    totalCount,
    activeTab,
    onTabChange,
    activeCaseTab,
    onCaseTabChange
}) => {
    const totalCaseCount = totalCount !== undefined ? totalCount : (evaluationSet?.cases?.length || 0);

    if (!evaluationSet) {
        return (
            <div className="evaluation-results-empty">
                <Text>无法加载评测集数据</Text>
            </div>
        );
    }

    return (
        <div className="evaluation-results">
            <div className="evaluation-set-header">
                <Title level={4}>{evaluationSet.set_name}</Title>
                <Text type="secondary">创建者: {evaluationSet.creator_name}</Text>
                <Text type="secondary">案例数量: {totalCaseCount}个</Text>
            </div>

            {evaluationSet.description && (
                <div className="evaluation-set-description">
                    <Text>{evaluationSet.description}</Text>
                </div>
            )}

            <Row gutter={[16, 16]}>
                <Col span={12}>
                    <Card
                        title="评测集平均指标"
                        className="metrics-card"
                        extra={
                            <Tabs
                                activeKey={activeTab}
                                onChange={(key) => onTabChange(key)}
                                size="small"
                                tabBarStyle={{ margin: 0 }}
                            >
                            </Tabs>
                        }
                        tabBarExtraContent={null}
                    >
                        {renderMetrics(metrics, loading.metrics, activeTab, onTabChange)}
                    </Card>
                </Col>
                <Col span={12}>
                    <Card
                        title="当前案例指标"
                        className="metrics-card"
                        extra={
                            <Tabs
                                activeKey={activeCaseTab}
                                onChange={(key) => onCaseTabChange(key)}
                                size="small"
                                tabBarStyle={{ margin: 0 }}
                            >
                            </Tabs>
                        }
                    >
                        {selectedCase ? (
                            renderMetrics(caseMetrics, loading.caseMetrics, activeCaseTab, onCaseTabChange)
                        ) : (
                            <Text type="secondary">请选择一个评测案例以查看指标</Text>
                        )}
                    </Card>
                </Col>
            </Row>

            {/* 评测案例列表 */}
            <Card title="评测案例" className="cases-list-card">
                <List
                    size="small"
                    dataSource={evaluationSet.cases || []}
                    renderItem={item => (
                        <List.Item
                            className={`case-item ${selectedCase?.id === item.id ? 'selected' : ''}`}
                            onClick={() => onCaseSelect(item)}
                        >
                            <div className="case-item-content">
                                <div className="case-item-name">
                                    {selectedCase?.id === item.id && <CaretRightOutlined className="play-icon" />}
                                    <Text ellipsis title={item.pkl_name}>
                                        {item.pkl_name}
                                    </Text>
                                </div>
                            </div>
                        </List.Item>
                    )}
                />
            </Card>

            {/* 分页控件 */}
            <div className="pagination-container">
                <Pagination
                    current={currentPage}
                    pageSize={pageSize}
                    total={totalCaseCount}
                    onChange={onPageChange}
                    showSizeChanger={false}
                    showTotal={(total) => `共 ${total} 条`}
                />
            </div>
        </div>
    );
};

export default EvaluationResults;