.inference-config-selector {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-radio-group {
  flex: 1;
  overflow: auto;
  margin-top: 16px;
}

.config-list-item {
  padding: 8px 0;
}

.config-radio {
  width: 100%;
  display: block;
}

.config-info {
  margin-left: 8px;
}

.config-name {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-model {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.config-time {
  font-size: 12px;
}

.config-selector-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex-direction: column;
}

.config-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(0, 0, 0, 0.45);
}