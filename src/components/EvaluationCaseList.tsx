// 重构后的 EvaluationCaselist.tsx
import React, { useState, useEffect } from 'react';
import { Table, Input, Button, Pagination, Card, Spin, message, Tag, Modal } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import axios from 'axios';
import SceneFilterBar from './SceneFilterBar';
import './EvaluationCaseList.css';
import { EvaluationCase, EvaluationCaseResponse } from '../types/index'; // 假设你有一个类型定义文件


interface EvaluationCaseListProps {
    onSelectEvaluationSet?: (evaluationSet: EvaluationCase) => void;
    creationMode?: boolean;
}

const EvaluationCaselist: React.FC<EvaluationCaseListProps> = ({
    onSelectEvaluationSet,
    creationMode = false
}) => {
    const viewDetails = (record: EvaluationCase) => {
        // 调用父组件传递的回调函数
        if (onSelectEvaluationSet) {
            onSelectEvaluationSet(record);
        }
    };
    const [evaluationSets, setEvaluationCases] = useState<EvaluationCase[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 12,
        total: 0,
        pages: 1,
    });

    // 加载评测集数据
    const fetchEvaluationCases = async (page = 1, search = searchTerm) => {
        setLoading(true);
        try {
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            params.append('page', page.toString());
            params.append('per_page', pagination.pageSize.toString());

            const response = await axios.get<EvaluationCaseResponse>(`/api/evaluation_case_pool?${params}`);

            setEvaluationCases(response.data.data);
            setPagination({
                current: response.data.page,
                pageSize: response.data.per_page,
                total: response.data.total,
                pages: response.data.pages,
            });
        } catch (error) {
            console.error('Failed to fetch evaluation sets:', error);
            message.error('获取评测集数据失败，请刷新重试');
        } finally {
            setLoading(false);
        }
    };

    // 首次加载和筛选条件变化时重新获取数据
    useEffect(() => {
        fetchEvaluationCases(1, searchTerm);
    }, []);


    // 处理搜索
    const handleSearch = () => {
        fetchEvaluationCases(1, searchTerm);
    };

    // 处理分页变化
    const handlePageChange = (page: number) => {
        fetchEvaluationCases(page, searchTerm);
    };

    // 启用拖动功能
    const handleDragStart = (record: EvaluationCase) => (e: React.DragEvent<HTMLElement>) => {
        e.dataTransfer.setData('application/json', JSON.stringify(record));
        e.dataTransfer.effectAllowed = 'copy';
    };

    // 表格列定义 - 删除"查看详情"列
    const columns = [
        {
            title: 'PKL名称',
            dataIndex: 'pkl_name',
            key: 'pkl_name',
            ellipsis: true,
        },
    ];

    return (
        <div className="evaluation-set-container">
            <Card title={creationMode ? "选择评测项" : "pkl列表"} >
                <div className="filter-section" >
                    <div className="search-section">
                        <Input
                            placeholder="搜索pkl文件名..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            style={{ width: 200, marginRight: 8 }}
                            allowClear
                        />
                        <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                            搜索
                        </Button>
                    </div>
                </div>

                <Spin spinning={loading}>
                    <Table
                        columns={columns}
                        dataSource={evaluationSets}
                        rowKey="id"
                        pagination={false}
                        style={{ marginTop: 16 }}
                        locale={{ emptyText: searchTerm ? `没有找到包含 "${searchTerm}" 的评测集` : "暂无评测集数据" }}
                        onRow={(record) => ({
                            onClick: () => viewDetails(record),  // 点击整行查看详情
                            draggable: creationMode,
                            onDragStart: creationMode ? handleDragStart(record) : undefined,
                            className: creationMode ? 'draggable-row' : 'clickable-row'  // 添加可点击样式
                        })}
                    />

                    {pagination.total > 0 && (
                        <div className="pagination-container" style={{ marginTop: 16, textAlign: 'right' }}>
                            <Pagination
                                current={pagination.current}
                                pageSize={pagination.pageSize}
                                total={pagination.total}
                                onChange={handlePageChange}
                                showSizeChanger={false}
                                showTotal={(total) => `共 ${total} 条记录`}
                            />
                        </div>
                    )}
                </Spin>

                {creationMode && (
                    <div className="drag-tip">
                        <p>提示：拖动列表项到右侧候选框添加到评测集</p>
                    </div>
                )}
            </Card>
        </div>
    );
};

export default EvaluationCaselist;