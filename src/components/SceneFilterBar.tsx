// components/SceneFilterBar.tsx
import React from 'react';
import { Button } from 'antd';
interface SceneFilterBarProps {
    selected: string;
    onSelect: (tag: string) => void;
}

const SceneFilterBar: React.FC<SceneFilterBarProps> = ({ selected, onSelect }) => {
    const tags = ['ALL', 'EFFICIENCY_LCR', 'LEFT_TURN', 'RIGHT_TURN'];

    return (
        <div className="flex gap-4 mb-4">
            {tags.map(tag => (
                <Button
                    key={tag}
                    className={`px-4 py-2 rounded-full border ${selected === tag ? 'bg-blue-500 text-white' : 'bg-gray-200 text-black'}`}
                    onClick={() => onSelect(tag)}
                >
                    {tag}
                </Button>
            ))}
        </div>
    );
};

export default SceneFilterBar;
