/* src/components/EvaluationSetSelector.css */
.evaluation-set-selector {
    height: 100%;
}

.section-title {
    font-size: 20px;
    font-weight: 500;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.selection-count {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    font-size: 14px;
}

.creator-groups {
    margin-top: 20px;
}

.creator-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    padding-left: 10px;
    border-left: 3px solid #1890ff;
}

.sets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 30px;
}

.set-card {
    position: relative;
    height: 160px;
    transition: all 0.3s;
}

.set-card.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.selection-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #1890ff;
    font-size: 18px;
}

.set-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.set-description {
    color: #666;
    font-size: 14px;
    height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.set-meta {
    position: absolute;
    bottom: 12px;
    left: 12px;
}

.loading-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}