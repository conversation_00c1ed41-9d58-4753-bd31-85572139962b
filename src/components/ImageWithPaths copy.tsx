// src/components/ImageWithPaths.tsx
import React, { useRef, useEffect } from 'react';
import { PdpPathInfo } from '../types';

interface ImageWithPathsProps {
    imageData: string;
    pdpPaths: Record<number, PdpPathInfo>;
    highlightPathIndex: number | null;
    ego2img: number[][] | null;
    egoYaw: number | null; // 修改为可选类型
    style?: React.CSSProperties;
}

const ImageWithPaths: React.FC<ImageWithPathsProps> = ({ 
    imageData, 
    pdpPaths, 
    highlightPathIndex,
    ego2img,
    egoYaw,
    style 
}) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const imageRef = useRef<HTMLImageElement>(null);

    // 坐标转换函数
    const transformPoint = (point: number[]): number[] => {
        if (!ego2img || !point || point.length < 2 || egoYaw === null) {
            return point;
        }

        try {
            // 旋转变换
            let x = point[0] * Math.cos(egoYaw) - point[1] * Math.sin(egoYaw);
            let y = point[0] * Math.sin(egoYaw) + point[1] * Math.cos(egoYaw);
            const z = 0;
            const w = 1;

            // 矩阵乘法：ego2img * [x, y, z, 1]^T
            const transformed = [
                ego2img[0][0] * x + ego2img[0][1] * y + ego2img[0][2] * z + ego2img[0][3] * w,
                ego2img[1][0] * x + ego2img[1][1] * y + ego2img[1][2] * z + ego2img[1][3] * w,
                ego2img[2][0] * x + ego2img[2][1] * y + ego2img[2][2] * z + ego2img[2][3] * w,
                ego2img[3][0] * x + ego2img[3][1] * y + ego2img[3][2] * z + ego2img[3][3] * w
            ];

            // 透视投影
            if (transformed[2] !== 0) {
                const imageX = transformed[0] / transformed[2];
                const imageY = transformed[1] / transformed[2];
                return [imageX, imageY];
            } else {
                return point;
            }
        } catch (error) {
            return point;
        }
    };

    useEffect(() => {
        const canvas = canvasRef.current;
        const image = imageRef.current;
        
        if (!canvas || !image) {
            return;
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            return;
        }

        const drawPaths = () => {
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景图像
            ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
            
            // 绘制路径的函数
            const drawSinglePath = (path: PdpPathInfo, isHighlighted: boolean): boolean => {
                if (!path.visualization_points || path.visualization_points.length === 0) {
                    return false;
                }
                
                // 设置路径样式 - 支持自定义颜色
                let strokeColor = '#1890ff'; // 默认蓝色
                let lineWidth = 2; // 默认线宽

                if (path.color) {
                    strokeColor = path.color; // 使用自定义颜色
                }
                if (path.lineWidth) {
                    lineWidth = path.lineWidth; // 使用自定义线宽
                }

                // 高亮路径使用红色覆盖
                if (isHighlighted) {
                    strokeColor = '#ff4d4f';
                    lineWidth = Math.max(lineWidth, 4); // 高亮时至少4像素宽
                }

                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = lineWidth;
                ctx.globalAlpha = isHighlighted ? 1.0 : 0.8; // 稍微调整透明度以便区分
                
                // 插值函数：将前4个点扩展为16个点
                const interpolateFirst4Points = (points: number[][]): number[][] => {
                    if (points.length < 4) {
                        return points;
                    }
                    
                    const interpolatedPoints: number[][] = [];
                    
                    // 对前3个线段进行插值，每个线段分成5段
                    for (let i = 0; i < 3; i++) {
                        const startPoint = points[i];
                        const endPoint = points[i + 1];
                        
                        for (let j = 0; j < 5; j++) {
                            const t = j / 5;
                            const interpolatedPoint = [
                                startPoint[0] + t * (endPoint[0] - startPoint[0]),
                                startPoint[1] + t * (endPoint[1] - startPoint[1]),
                                startPoint.length > 2 && endPoint.length > 2 ? 
                                    (startPoint[2] + t * (endPoint[2] - startPoint[2])) : 0
                            ];
                            interpolatedPoints.push(interpolatedPoint);
                        }
                    }
                    
                    // 添加第4个原始点作为第16个插值点
                    interpolatedPoints.push(points[3]);
                    
                    return interpolatedPoints;
                };
                
                // 创建处理后的点数组
                let processedPoints: number[][] = [];
                
                if (path.visualization_points.length >= 4) {
                    const first4Points = path.visualization_points.slice(0, 4);
                    const interpolated = interpolateFirst4Points(first4Points);
                    
                    processedPoints = [
                        ...interpolated,
                        ...path.visualization_points.slice(4)
                    ];
                } else {
                    processedPoints = path.visualization_points;
                }
                
                // 绘制路径
                ctx.beginPath();
                let validPointsCount = 0;
                
                processedPoints.forEach((point: number[], index: number) => { // 也修改这里的类型
                    let transformedPoint = transformPoint(point);
                    
                    let x = transformedPoint[0];
                    let y = transformedPoint[1];
                    
                    // 检查转换后的坐标是否在图像范围内
                    if (x >= 0 && x <= canvas.width && y >= 0 && y <= canvas.height) {
                        validPointsCount++;
                        
                        if (index === 0 || validPointsCount === 1) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                });
                
                if (validPointsCount > 0) {
                    ctx.stroke();
                    return true;
                }
                return false;
            };
            
            // 第一阶段：绘制所有非高亮路径
            let drawnPathsCount = 0;
            let highlightedPath: PdpPathInfo | null = null;

            Object.values(pdpPaths).forEach((path: PdpPathInfo) => {
                const isHighlighted = highlightPathIndex === path.index;
                
                if (isHighlighted) {
                    highlightedPath = path;
                } else {
                    if (drawSinglePath(path, false)) {
                        drawnPathsCount++;
                    }
                }
            });
            
            // 第二阶段：最后绘制高亮路径
            if (highlightedPath) {
                const currentPath: PdpPathInfo = highlightedPath; // 重新声明变量
                
                if (drawSinglePath(currentPath, true)) {
                    drawnPathsCount++;
                }
                
                // 绘制高亮路径的起点和终点标记
                if (currentPath.visualization_points && currentPath.visualization_points.length > 0) {
                    const startPointForMarker = currentPath.visualization_points[0];
                    
                    const startPoint = transformPoint(startPointForMarker);
                    const endPoint = transformPoint(currentPath.visualization_points[currentPath.visualization_points.length - 1]);
                    
                    // 绘制起点（绿色圆圈）
                    if (startPoint[0] >= 0 && startPoint[0] <= canvas.width && 
                        startPoint[1] >= 0 && startPoint[1] <= canvas.height) {
                        ctx.fillStyle = '#52c41a';
                        ctx.beginPath();
                        ctx.arc(startPoint[0], startPoint[1], 2, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                    
                    // 绘制终点（红色圆圈）
                    if (endPoint[0] >= 0 && endPoint[0] <= canvas.width && 
                        endPoint[1] >= 0 && endPoint[1] <= canvas.height) {
                        ctx.fillStyle = '#ff4d4f';
                        ctx.beginPath();
                        ctx.arc(endPoint[0], endPoint[1], 2, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                }
            }
            
            // 重置透明度
            ctx.globalAlpha = 1.0;
        };

        // 如果图像已经加载，直接绘制
        if (image.complete) {
            drawPaths();
        } else {
            image.onload = () => {
                drawPaths();
            };
        }
    }, [pdpPaths, highlightPathIndex, imageData, ego2img, egoYaw]);

    const handleImageLoad = () => {
        const canvas = canvasRef.current;
        const image = imageRef.current;
        
        if (canvas && image) {
            canvas.width = image.naturalWidth;
            canvas.height = image.naturalHeight;
        }
    };

    return (
        <div style={{ position: 'relative', ...style }}>
            <img
                ref={imageRef}
                src={`data:image/png;base64,${imageData}`}
                alt="E2E图像"
                onLoad={handleImageLoad}
                onError={() => {}}
                style={{ 
                    maxWidth: '100%',
                    maxHeight: '200px',
                    objectFit: 'contain',
                    display: 'none'
                }}
            />
            <canvas
                ref={canvasRef}
                style={{ 
                    maxWidth: '100%',
                    maxHeight: '200px',
                    objectFit: 'contain',
                    display: 'block'
                }}
            />
        </div>
    );
};

export default ImageWithPaths;