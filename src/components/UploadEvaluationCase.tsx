import React, { useState } from 'react';
import axios from 'axios';

interface FormData {
    pklName: string;
    pklDir: string;
    bagName: string;
    sceneTag: string;
    keyObsId: number;
    pathRangeStart: number;
    pathRangeEnd: number;
}

const UploadEvaluationSet: React.FC = () => {
    const [formData, setFormData] = useState<FormData>({
        pklName: '',
        pklDir: '',
        bagName: '',
        sceneTag: 'EFFICIENCY_LCR',
        keyObsId: 0,
        pathRangeStart: 0,
        pathRangeEnd: 10,
    });

    const [uploading, setUploading] = useState(false);
    const [csvUploading, setCsvUploading] = useState(false);
    const [message, setMessage] = useState({ type: '', content: '' });

    // 处理表单变化
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: ['keyObsId', 'pathRangeStart', 'pathRangeEnd'].includes(name)
                ? parseInt(value) || 0
                : value,
        }));
    };

    // 处理CSV文件上传
    const handleCsvUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files || e.target.files.length === 0) return;

        const file = e.target.files[0];

        // 验证文件类型
        if (!file.name.endsWith('.csv')) {
            setMessage({ type: 'error', content: '请上传CSV格式的文件' });
            return;
        }

        setCsvUploading(true);
        setMessage({ type: '', content: '' });

        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await axios.post('/api/evaluation_case_pool/upload_csv', formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });

            setMessage({
                type: 'success',
                content: `成功导入 ${response.data.count} 个评测集`,
            });

            // 清空文件选择
            e.target.value = '';
        } catch (error: any) {
            console.error('CSV上传失败:', error);
            setMessage({
                type: 'error',
                content: error.response?.data?.detail || 'CSV文件处理失败',
            });
        } finally {
            setCsvUploading(false);
        }
    };

    return (
        <div className="space-y-8">
            {/* 消息提示 */}
            {message.content && (
                <div
                    className={`p-4 rounded ${message.type === 'success'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                >
                    {message.content}
                </div>
            )}

            {/* CSV上传部分 */}
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-bold mb-4">批量上传pkl(CSV)</h2>
                <p className="text-gray-600 mb-4">
                    通过上传CSV文件批量导入pkl。CSV文件<strong>必须</strong>包含以下列：
                    <span className="font-semibold">pkl_name, pkl_dir</span>。
                    其他列如 key_obs_id, path_range_start, path_range_end 为可选项。
                </p>

                <div className="flex items-center space-x-4">
                    <label className="flex-1">
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500">
                            <input
                                type="file"
                                accept=".csv"
                                onChange={handleCsvUpload}
                                disabled={csvUploading}
                                className="hidden"
                            />
                            <div className="flex flex-col items-center">
                                <svg
                                    className="w-8 h-8 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    />
                                </svg>
                                <span className="mt-2 text-sm font-medium text-gray-900">
                                    {csvUploading ? '上传中...' : '点击选择CSV文件'}
                                </span>
                            </div>
                        </div>
                    </label>
                </div>

                <div className="mt-4">
                    <a
                        href="/sample_evaluation_set.csv"
                        download
                        className="text-blue-600 hover:underline text-sm"
                    >
                        下载示例CSV模板
                    </a>
                </div>
            </div>
        </div>
    );
};

export default UploadEvaluationSet;