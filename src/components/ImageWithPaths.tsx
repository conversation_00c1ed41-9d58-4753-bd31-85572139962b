// src/components/ImageWithPaths.tsx
import React, { useRef, useEffect } from 'react';
import { PdpPathInfo } from '../types';

interface ImageWithPathsProps {
    imageData: string;
    pdpPaths: Record<number, PdpPathInfo>;
    highlightPathIndex: number | null;
    ego2img: number[][] | null;
    egoYaw: number | null;
    style?: React.CSSProperties;
    
    // ego路径点支持
    egoPathPoints?: number[][]; 
    showEgoPath?: boolean;      
    egoPathStyle?: {           
        color?: string;
        lineWidth?: number;
        pointRadius?: number;
        pointColor?: string;
        showPoints?: boolean;   
        showLine?: boolean;     
    };
}

const ImageWithPaths: React.FC<ImageWithPathsProps> = ({ 
    imageData, 
    pdpPaths, 
    highlightPathIndex,
    ego2img,
    egoYaw,
    style,
    egoPathPoints = [],
    showEgoPath = false,
    egoPathStyle = {}
}) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const imageRef = useRef<HTMLImageElement>(null);

    // 坐标转换函数
    const transformPoint = (point: number[]): number[] => {
        if (!ego2img || !point || point.length < 2 || egoYaw === null) {
            return point;
        }

        try {
            // 旋转变换
            let x = point[0] * Math.cos(egoYaw) - point[1] * Math.sin(egoYaw);
            let y = point[0] * Math.sin(egoYaw) + point[1] * Math.cos(egoYaw);
            const z = 0;
            const w = 1;

            // 矩阵乘法：ego2img * [x, y, z, 1]^T
            const transformed = [
                ego2img[0][0] * x + ego2img[0][1] * y + ego2img[0][2] * z + ego2img[0][3] * w,
                ego2img[1][0] * x + ego2img[1][1] * y + ego2img[1][2] * z + ego2img[1][3] * w,
                ego2img[2][0] * x + ego2img[2][1] * y + ego2img[2][2] * z + ego2img[2][3] * w,
                ego2img[3][0] * x + ego2img[3][1] * y + ego2img[3][2] * z + ego2img[3][3] * w
            ];

            // 透视投影
            if (transformed[2] !== 0) {
                const imageX = transformed[0] / transformed[2];
                const imageY = transformed[1] / transformed[2];
                return [imageX, imageY];
            } else {
                return point;
            }
        } catch (error) {
            return point;
        }
    };

    useEffect(() => {
        const canvas = canvasRef.current;
        const image = imageRef.current;
        
        if (!canvas || !image) {
            return;
        }

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            return;
        }

        const drawPaths = () => {
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景图像
            ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
            
            // 绘制路径的函数
            const drawSinglePath = (path: PdpPathInfo, isHighlighted: boolean): boolean => {
                if (!path.visualization_points || path.visualization_points.length === 0) {
                    return false;
                }
                
                // 设置路径样式
                let strokeColor = '#1890ff';
                let lineWidth = 2;

                if (path.color) {
                    strokeColor = path.color;
                }
                if (path.lineWidth) {
                    lineWidth = path.lineWidth;
                }

                if (isHighlighted) {
                    strokeColor = '#ff4d4f';
                    lineWidth = Math.max(lineWidth, 4);
                }

                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = lineWidth;
                ctx.globalAlpha = isHighlighted ? 1.0 : 0.8;
                
                // 插值函数
                const interpolateFirst4Points = (points: number[][]): number[][] => {
                    if (points.length < 4) {
                        return points;
                    }
                    
                    const interpolatedPoints: number[][] = [];
                    
                    for (let i = 0; i < 3; i++) {
                        const startPoint = points[i];
                        const endPoint = points[i + 1];
                        
                        for (let j = 0; j < 5; j++) {
                            const t = j / 5;
                            const interpolatedPoint = [
                                startPoint[0] + t * (endPoint[0] - startPoint[0]),
                                startPoint[1] + t * (endPoint[1] - startPoint[1]),
                                startPoint.length > 2 && endPoint.length > 2 ? 
                                    (startPoint[2] + t * (endPoint[2] - startPoint[2])) : 0
                            ];
                            interpolatedPoints.push(interpolatedPoint);
                        }
                    }
                    
                    interpolatedPoints.push(points[3]);
                    return interpolatedPoints;
                };
                
                // 创建处理后的点数组
                let processedPoints: number[][] = [];
                
                if (path.visualization_points.length >= 4) {
                    const first4Points = path.visualization_points.slice(0, 4);
                    const interpolated = interpolateFirst4Points(first4Points);
                    
                    processedPoints = [
                        ...interpolated,
                        ...path.visualization_points.slice(4)
                    ];
                } else {
                    processedPoints = path.visualization_points;
                }
                
                // 绘制路径
                ctx.beginPath();
                let validPointsCount = 0;
                
                processedPoints.forEach((point: number[], index: number) => {
                    let transformedPoint = transformPoint(point);
                    
                    let x = transformedPoint[0];
                    let y = transformedPoint[1];
                    
                    if (x >= 0 && x <= canvas.width && y >= 0 && y <= canvas.height) {
                        validPointsCount++;
                        
                        if (index === 0 || validPointsCount === 1) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                });
                
                if (validPointsCount > 0) {
                    ctx.stroke();
                    return true;
                }
                return false;
            };
            
            // 第一阶段：绘制所有非高亮路径
            let drawnPathsCount = 0;
            let highlightedPath: PdpPathInfo | null = null;

            Object.values(pdpPaths).forEach((path: PdpPathInfo) => {
                const isHighlighted = highlightPathIndex === path.index;
                
                if (isHighlighted) {
                    highlightedPath = path;
                } else {
                    if (drawSinglePath(path, false)) {
                        drawnPathsCount++;
                    }
                }
            });
            
            // 第二阶段：最后绘制高亮路径
            if (highlightedPath) {
                const currentPath: PdpPathInfo = highlightedPath;
                
                if (drawSinglePath(currentPath, true)) {
                    drawnPathsCount++;
                }
                
                // 绘制高亮路径的起点和终点标记
                if (currentPath.visualization_points && currentPath.visualization_points.length > 0) {
                    const startPointForMarker = currentPath.visualization_points[0];
                    
                    const startPoint = transformPoint(startPointForMarker);
                    const endPoint = transformPoint(currentPath.visualization_points[currentPath.visualization_points.length - 1]);
                    
                    // 绘制起点（绿色圆圈）
                    if (startPoint[0] >= 0 && startPoint[0] <= canvas.width && 
                        startPoint[1] >= 0 && startPoint[1] <= canvas.height) {
                        ctx.fillStyle = '#52c41a';
                        ctx.beginPath();
                        ctx.arc(startPoint[0], startPoint[1], 2, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                    
                    // 绘制终点（红色圆圈）
                    if (endPoint[0] >= 0 && endPoint[0] <= canvas.width && 
                        endPoint[1] >= 0 && endPoint[1] <= canvas.height) {
                        ctx.fillStyle = '#ff4d4f';
                        ctx.beginPath();
                        ctx.arc(endPoint[0], endPoint[1], 2, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                }
            }

            // 绘制 ego 路径
            if (showEgoPath && egoPathPoints && egoPathPoints.length > 0) {
                drawEgoPath();
            }
            
            // 重置透明度
            ctx.globalAlpha = 1.0;
        };

        // 绘制 ego 路径的函数
        const drawEgoPath = () => {
            if (!egoPathPoints || egoPathPoints.length === 0) {
                return;
            }
            
            // 默认样式配置
            const style = {
                color: '#00ff00',          
                lineWidth: 3,              
                pointRadius: 3,            
                pointColor: '#ff0000',     
                showPoints: true,          
                showLine: true,            
                ...egoPathStyle            
            };
            
            ctx.globalAlpha = 0.8;
            
            // 转换所有点并过滤有效点
            const transformedPoints: number[][] = [];
            
            egoPathPoints.forEach((point) => {
                const transformedPoint = transformPoint(point);
                const x = transformedPoint[0];
                const y = transformedPoint[1];
                
                if (x >= 0 && x <= canvas.width && y >= 0 && y <= canvas.height) {
                    transformedPoints.push([x, y]);
                }
            });
            
            if (transformedPoints.length === 0) {
                return;
            }
            
            // 绘制连线
            if (style.showLine && transformedPoints.length > 1) {
                ctx.strokeStyle = style.color;
                ctx.lineWidth = style.lineWidth;
                ctx.beginPath();
                
                transformedPoints.forEach(([x, y], index) => {
                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                
                ctx.stroke();
            }
            
            // 绘制路径点
            if (style.showPoints) {
                ctx.fillStyle = style.pointColor;
                transformedPoints.forEach(([x, y]) => {
                    ctx.beginPath();
                    ctx.arc(x, y, style.pointRadius, 0, 2 * Math.PI);
                    ctx.fill();
                });
            }
        };

        // 如果图像已经加载，直接绘制
        if (image.complete) {
            drawPaths();
        } else {
            image.onload = () => {
                drawPaths();
            };
        }
    }, [pdpPaths, highlightPathIndex, imageData, ego2img, egoYaw, showEgoPath, egoPathPoints, egoPathStyle]);

    const handleImageLoad = () => {
        const canvas = canvasRef.current;
        const image = imageRef.current;
        
        if (canvas && image) {
            canvas.width = image.naturalWidth;
            canvas.height = image.naturalHeight;
        }
    };

    return (
        <div style={{ position: 'relative', ...style }}>
            <img
                ref={imageRef}
                src={`data:image/png;base64,${imageData}`}
                alt="E2E图像"
                onLoad={handleImageLoad}
                onError={() => {}}
                style={{ 
                    maxWidth: '100%',
                    maxHeight: '200px',
                    objectFit: 'contain',
                    display: 'none'
                }}
            />
            <canvas
                ref={canvasRef}
                style={{ 
                    maxWidth: '100%',
                    maxHeight: '200px',
                    objectFit: 'contain',
                    display: 'block'
                }}
            />
        </div>
    );
};

export default ImageWithPaths;