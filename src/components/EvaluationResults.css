.evaluation-results {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.evaluation-set-header {
  margin-bottom: 16px;
}

.evaluation-set-header h4 {
  margin-bottom: 4px;
}

.evaluation-set-description {
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.metrics-card {
  margin-bottom: 16px;
}

.metrics-card .ant-card-body {
  padding: 12px;
}

.metrics-tabs .ant-tabs-content {
  margin-top: 0;
}

.cases-list-card {
  flex: 1;
  overflow: auto;
}

.metrics-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  flex-direction: column;
}

.case-item {
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
}

.case-item:hover {
  background-color: #e6f7ff;
}

.case-item.selected {
  background-color: #e6f7ff;
}

.case-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.case-item-name {
  display: flex;
  align-items: center;
  max-width: 100%;
}

.play-icon {
  margin-right: 8px;
  color: #1890ff;
}

.evaluation-results-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 当前案例指标高亮显示 */
.ant-tabs-tab-active .ant-statistic-content-value {
  color: #1890ff;
}