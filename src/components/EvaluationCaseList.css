.evaluation-set-container {
    padding: 0 16px;
}

.filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.scene-filter {
    flex: 1;
}

.search-section {
    display: flex;
    justify-content: flex-end;
    margin-left: 16px;
}

.pagination-container {
    margin-top: 10px;
}
/* 在现有CSS基础上添加 */
.draggable-row {
    cursor: move;
    transition: all 0.3s;
}

.draggable-row:hover {
    background-color: #f0f8ff;
}

.drag-tip {
    margin-top: 10px;
    text-align: center;
    color: #999;
    font-style: italic;
}
/* 添加到 EvaluationCaseList.css 文件中 */
.clickable-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.clickable-row:hover {
  background-color: #f5f5f5;
}