.evaluation-creator-container {
    width: 100%;
    height: 100%;
    overflow: auto;
}

.drop-zone {
    min-height: 200px;
    border: 2px dashed #d9d9d9;
    border-radius: 4px;
    padding: 16px;
    background-color: #fafafa;
    margin-bottom: 20px;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: auto;
    max-height: 400px;
}

.drop-zone.drag-over {
    border-color: #1890ff;
    background-color: #e6f7ff;
}

.selection-container {
    margin-bottom: 20px;
}

.selection-container h3 {
    margin-bottom: 10px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}
/* 在现有CSS文件末尾添加以下样式 */
.csv-upload-container {
    margin-top: 16px;
    margin-bottom: 16px;
}

.csv-instructions {
    margin-bottom: 16px;
    color: #666;
}

.csv-instructions strong {
    color: #1890ff;
    margin: 0 4px;
}

.ant-upload-drag {
    margin-bottom: 20px;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}