.task-progress-drawer .ant-drawer-body {
  padding: 16px;
}

.drawer-title {
  display: flex;
  align-items: center;
}

.status-badge {
  margin-left: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  color: white;
}

.status-badge.running {
  background-color: #1890ff;
}

.status-badge.completed {
  background-color: #52c41a;
}

.status-badge.failed {
  background-color: #ff4d4f;
}

.status-badge.pending {
  background-color: #faad14;
}

.progress-message {
  margin-bottom: 16px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.progress-section {
  margin-bottom: 16px;
}

.progress-section h4 {
  margin-bottom: 8px;
}

.stat-card {
  box-shadow: none !important;
}

.stat-card .ant-card-body {
  padding: 12px;
}

.error-stat {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.collapsed-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}