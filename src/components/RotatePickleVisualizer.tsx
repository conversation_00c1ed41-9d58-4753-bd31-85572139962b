/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useRef, useState } from "react";
import * as THREE from "three";
import { OrbitControls } from "three/addons/controls/OrbitControls.js";
import axios from "axios";
import { Line2 } from "three/examples/jsm/lines/Line2.js";
import { LineMaterial } from "three/examples/jsm/lines/LineMaterial.js";
import { EvaluationCase, PdpPathInfo, DlpTrajectoryInfo } from "../types";
import { LineGeometry } from "three/examples/jsm/lines/LineGeometry.js";
// 评测集数据结构

// 定义组件的Props
interface PickleVisualizerProps {
  evaluationCase: EvaluationCase; // 可选，如果提供则使用该评测集的pkl路径
  onClose?: () => void; // 关闭可视化视图的回调函数
  height?: string | number; // 可视化区域高度
  pklId?: number; // 新增属性: 直接使用pkl ID
  highlightPathIndex?: number | null; // 新增属性: 高亮路径索引
  pdpPaths?: Record<number, PdpPathInfo>;
  dlpTrajs?: DlpTrajectoryInfo[];
  highlightTrajIndex?: number | null;
  showGroundTruth?: boolean; // 是否有地面真实路径
  changeIds?: any;
  selectedObjectIds?: string[];
  changeCenterlineIds?: any; // 新增：centerlines选中状态变化回调
  selectedCenterlineIds?: string[]; // 新增：选中的centerlines ID列表
}

// 定义后端返回的数据结构类型
interface VisualizationData {
  polylines: {
    id: string;
    points: number[][];
    color: string;
    thickness: number;
    type: string;
  }[];
  polygons: {
    id: string;
    vertices: number[][];
    color: string;
    opacity: number;
    type: string;
  }[];
  arrows: {
    id: string;
    start: number[];
    end: number[];
    color: string;
    headSize: number;
  }[];
  texts: {
    id: string;
    position: number[];
    content: string;
    fontSize: number;
    color: string;
  }[];
  objects: {
    id: string;
    position: number[];
    dimensions: {
      width: number;
      height: number;
      depth: number;
    };
    rotation: number[];
    color: string;
    type: string;
    opacity: number;
  }[];
  tbt?: {
    dist: number;
    maneuver: string;
    lane_action: string;
  };
  metadata: {
    filename: string;
    timestamp: string;
    ego_heading: number;
  };
  traffic_lights?: {
    color: string;
    sign: string;
  };
  future_obj_infos?: {
    id: string;
    points: number[][]; // 轨迹点数组，每个点是[x, y, z]
    timestamps: number[]; // 时间戳数组
    rotation: number[]; // 朝向角度数组
  }[];
  recommend_lanes?: {
    id: string;
    start: number[];
    end: number[];
    color: string;
    headSize: number;
  }[];
  future_obj_pred?: {
    id: string;
    points: number[][]; // 预测轨迹点数组，每个点是[x, y, z]
    timestamps: number[]; // 时间戳数组
  }[];
  centerlines_for_drivable?: {
    id: string;
    points: number[][]; // 中心线点坐标数组
    points_recommend_label: number; // 推荐标签，1为黄色，0为白色
  }[];
}

let objectList: string[] = [];
let centerlineList: string[] = [];

const RotatePickleVisualizer: React.FC<PickleVisualizerProps> = (props) => {
  const {
    evaluationCase,
    height = "calc(100vh - 200px)",
    pdpPaths,
    highlightPathIndex = null,
    showGroundTruth = true,
    dlpTrajs,
    highlightTrajIndex,
    selectedObjectIds,
    selectedCenterlineIds,
    
  } = props;

  const mountRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  // Three.js 对象引用
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const visualizationGroupRef = useRef<THREE.Group | null>(null);
  const inferenceGroupRef = useRef<THREE.Group | null>(null);
  const [tbtInfo, setTbtInfo] = useState<VisualizationData["tbt"] | null>(null);
  const [traffic_lights_Info, setTraffic_lights_Info] = useState<
    VisualizationData["traffic_lights"] | null
  >(null);
  const [hideOtherPaths, setHideOtherPaths] = useState<boolean>(false);
  const [selectTimestamp, setSelectTimestamp] = useState<number>(0);
  const [currentVisualizationData, setCurrentVisualizationData] =
    useState<VisualizationData | null>(null);
  const [showFutureTrajectories, setShowFutureTrajectories] =
    useState<boolean>(false);
  const [showPredictionTrajectories, setShowPredictionTrajectories] =
    useState<boolean>(false);
    
  const [geolocation, setGeolocation] = useState<
    | {
        x: number | string;
        y: number | string;
      }
    | undefined
  >({
    x: 0,
    y: 0,
  });

  const handleKeyDown = (event: KeyboardEvent) => {
    // 只有在有高亮路径时才处理空格键
    if (event.code === "Space" && highlightPathIndex !== null) {
      event.preventDefault(); // 防止页面滚动
      setHideOtherPaths((prev) => !prev);
    }
  };
  useEffect(() => {
    // 添加键盘事件监听器
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [highlightPathIndex]); // 依赖于highlightPathIndex，确保事件处理器能获取到最新的值

  useEffect(() => {
    if (selectedObjectIds) {
      objectList = selectedObjectIds;
    }
  }, [selectedObjectIds]);
  useEffect(() => {
    if (selectedCenterlineIds) {
      centerlineList = selectedCenterlineIds;
    }
  }, [selectedCenterlineIds]);
  // 初始化 Three.js 场景
  useEffect(() => {
    if (!mountRef.current) return;

    // 先清除可能存在的canvas
    while (mountRef.current.firstChild) {
      mountRef.current.removeChild(mountRef.current.firstChild);
    }
    const frustumSize = 100; // 定义视锥体的大小

    // 创建场景
    const scene = new THREE.Scene();
    // scene.position.y = -20;
    scene.position.set(0, -frustumSize *0.5, 0); // 场景整体向上移动
    sceneRef.current = scene;
    // scene.position.y = 0;  // 添加这行

    scene.background = new THREE.Color(0x333333);
    const width = mountRef.current.clientWidth;
    const height = mountRef.current.clientHeight;
    // scene.position.y = 0;
    // 创建相机
    // const camera = new THREE.OrthographicCamera(75, width / height, 0.1, 1000);
    // camera.position.set(0, 0, 180);
    // camera.lookAt(0, 0, 0);
    // camera.zoom = 1;
    // cameraRef.current = camera;
    const aspect = width / height; // 计算宽高比
    const camera = new THREE.OrthographicCamera(
      (-frustumSize * aspect) / 2, // left
      (frustumSize * aspect) / 2,  // right
      0,                           // top
      -frustumSize,                // bottom
      0.1,                         // near
      1000                         // far
    );
    camera.position.set(0, 0, 180); // 保持相机在 z 轴上的位置
    camera.lookAt(0, 0, 0); // 让相机始终看向原点
    camera.zoom = 0.9; // 设置缩放比例
    cameraRef.current = camera;
    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;
    // 添加轨道控制
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;

    controls.mouseButtons = {
      LEFT: THREE.MOUSE.PAN, // 左键平移
      MIDDLE: THREE.MOUSE.DOLLY, // 中键缩放
      RIGHT: THREE.MOUSE.ROTATE, // 右键旋转
    };

    // 禁用默认的旋转行为，我们将自定义Z轴旋转
    controls.enableRotate = false;
    controls.enablePan = true;
    controls.enableZoom = true;

    // 自定义Z轴旋转
    let isRotating = false;
    let lastMouseX = 0;

    const onMouseDown = (event: MouseEvent) => {
      if (event.button === 2) {
        // 右键
        isRotating = true;
        lastMouseX = event.clientX;
        event.preventDefault();
      }
    };

    const onMouseMove = (event: MouseEvent) => {
      if (isRotating) {
        const deltaX = event.clientX - lastMouseX;
        const rotationSpeed = 0.003;

        // 绕Z轴旋转场景
        if (sceneRef.current) {
          sceneRef.current.rotation.z += deltaX * rotationSpeed;
        }

        lastMouseX = event.clientX;
        event.preventDefault();
      }

      const geo: any = getGeolocation(event, renderer, camera);
      const ego = {
        x: !Number.isNaN(geo.x) ? geo.x.toFixed(1) : "0.00",
        y: !Number.isNaN(geo.y) ? geo.y.toFixed(1) : "0.00",
      };
      setGeolocation(ego);
    };

    const onMouseUp = (event: MouseEvent) => {
      if (event.button === 2) {
        // 右键
        isRotating = false;
      }
    };

    const onClick = (event: MouseEvent) => {
      const rect = renderer.domElement.getBoundingClientRect();
      const mouse = new THREE.Vector2(
        ((event.clientX - rect.left) / rect.width) * 2 - 1,
        -((event.clientY - rect.top) / rect.height) * 2 + 1
      );

      const raycaster = new THREE.Raycaster();
      raycaster.setFromCamera(mouse, camera);

      // 检测所有可交互对象（排除辅助线等）
      const interactableObjects = scene.children.filter(
        (obj) => obj.userData.isInteractable !== false
      );

      const intersects = raycaster.intersectObjects(interactableObjects, true);
      const value = intersects.filter((v) => v.object.name);
      if (value.length > 0) {
        const clickedObj: any = value[0].object;
        const id = String(clickedObj.name);

        // 检查是否是centerline对象
        if (clickedObj.userData?.isCenterline || clickedObj.userData?.isCenterlineArrow) {
          // 处理centerline点击
          const centerlineId = clickedObj.userData.centerlineId || id;

          const centerlineIndex = centerlineList.indexOf(centerlineId);
          if (centerlineIndex === -1) {
            centerlineList.push(centerlineId);
          } else {
            centerlineList.splice(centerlineIndex, 1);
          }

          // 调用centerline变化回调
          if (props.changeCenterlineIds) {
            props.changeCenterlineIds(centerlineList);
          }
          updateCenterlineColor(centerlineId, centerlineList.includes(centerlineId));

          // 更新centerline颜色
          // const isSelected = centerlineList.includes(id);
          // const defaultColor = clickedObj.defaultColor;
          // if (clickedObj.material) {
          //   clickedObj.material.color.set(isSelected ? "#9900ff" : defaultColor);
          // }
        } else {
          // 处理普通对象点击（原有逻辑）
          if (clickedObj.children && clickedObj.children[0]) {
            clickedObj.children[0].visible = !clickedObj.children[0].visible;
            const visible = clickedObj.children[0].visible;
            const defaultColor = clickedObj.defaultColor;
            // 更改物体材质颜色
            clickedObj.material.color.set(visible ? "#9900ff" : defaultColor);
          }

          const index = objectList.indexOf(id);
          if (index === -1) {
            objectList.push(id);
          } else {
            objectList.splice(index, 1);
          }

          if (props.changeIds) {
            props.changeIds(objectList);
          }
        }
      }
    };
// 添加新的辅助函数：更新整条centerline的颜色
const updateCenterlineColor = (centerlineId: string, isSelected: boolean) => {
  if (!visualizationGroupRef.current) return;

  // 遍历场景中的所有对象，找到匹配的centerline线段和箭头
  visualizationGroupRef.current.children.forEach((child) => {
    const userData = child.userData;
    
    // 检查是否是同一条centerline的线段或箭头
    if (userData.centerlineId === centerlineId && 
        (userData.isCenterline || userData.isCenterlineArrow)) {
      
      const defaultColor = (child as any).defaultColor;
      const targetColor = isSelected ? "#01E8F8" : defaultColor;

      // 更新线段颜色
      if (userData.isCenterline && (child as any).material) {
        (child as any).material.color.set(targetColor);
      }
      
      // 更新箭头颜色
      if (userData.isCenterlineArrow && (child as any).material) {
        (child as any).material.color.set(targetColor);
      }
    }
  });
};
    // 添加事件监听器
    renderer.domElement.addEventListener("mousedown", onMouseDown);
    renderer.domElement.addEventListener("mousemove", onMouseMove);
    renderer.domElement.addEventListener("mouseup", onMouseUp);
    renderer.domElement.addEventListener("contextmenu", (e) =>
      e.preventDefault()
    ); // 禁用右键菜单

    renderer.domElement.addEventListener("click", onClick);

    controlsRef.current = controls;

    // 创建并挂载两个组
    const vizGroup = new THREE.Group();
    vizGroup.userData = { type: "visualization_group" };
    scene.add(vizGroup);
    visualizationGroupRef.current = vizGroup;

    const infGroup = new THREE.Group();
    infGroup.userData = { type: "inference_group" };
    scene.add(infGroup);
    inferenceGroupRef.current = infGroup;

    // // 添加坐标轴
    // const axesHelper = new THREE.AxesHelper(100);
    // axesHelper.position.set(0,0,0)
    // scene.add(axesHelper);

    // const gridHelper = new THREE.GridHelper(10,10,0xff0000);
    // gridHelper.position.set(0,0,0)
    // gridHelper.rotation.set(Math.PI / 2,0,0)
    // scene.add(gridHelper);

    // 添加环境光和平行光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
    // 从正上方照射下来，位置在z轴正向
    directionalLight.position.set(0, 0, 10);
    // 确保光线朝下照射
    directionalLight.target.position.set(0, 0, 0);
    scene.add(directionalLight);
    scene.add(directionalLight.target);

    // 可以添加一个较弱的环境光来防止场景中的暗部过于黑暗
    // const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    // scene.add(ambientLight);

    // 动画循环
    const animate = () => {
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();

    // 处理窗口大小变化
    const handleResize = () => {
      if (!mountRef.current) return;

      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;
      const aspect = width / height;

      // 更新正交相机的参数
      const orthoCam = camera as THREE.OrthographicCamera;
      const frustumSize = 100;
      orthoCam.left = (-frustumSize * aspect) / 2;
      orthoCam.right = (frustumSize * aspect) / 2;
      orthoCam.top = frustumSize / 2;
      orthoCam.bottom = -frustumSize / 2;

      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    // 清理函数
    return () => {
      window.removeEventListener("resize", handleResize);
      if (rendererRef.current) {
        rendererRef.current.domElement.removeEventListener(
          "mousedown",
          onMouseDown
        );
        rendererRef.current.domElement.removeEventListener(
          "mousemove",
          onMouseMove
        );
        rendererRef.current.domElement.removeEventListener(
          "mouseup",
          onMouseUp
        );
        rendererRef.current.domElement.removeEventListener("contextmenu", (e) =>
          e.preventDefault()
        );
      }

      // 清理场景中的所有对象
      if (sceneRef.current) {
        // 递归释放场景中所有对象的资源
        const disposeObjects = (obj: THREE.Object3D) => {
          while (obj.children.length > 0) {
            disposeObjects(obj.children[0]);
            obj.remove(obj.children[0]);
          }

          if (obj instanceof THREE.Mesh) {
            if (obj.geometry) {
              obj.geometry.dispose();
            }

            if (obj.material) {
              if (Array.isArray(obj.material)) {
                obj.material.forEach((material) => material.dispose());
              } else {
                obj.material.dispose();
              }
            }
          }
        };

        disposeObjects(sceneRef.current);
      }

      // 清理渲染器
      if (rendererRef.current) {
        rendererRef.current.dispose();
        mountRef.current?.removeChild(rendererRef.current.domElement);
      }

      // 清理控制器
      if (controlsRef.current) {
        controlsRef.current.dispose();
      }
    };
  }, []);

  // 清空三维组的辅助函数
  const clearGroup = (group: THREE.Group | null) => {
    if (!group) return;
    while (group.children.length) {
      group.remove(group.children[0]);
    }
  };

  // 线性插值函数
  const lerp = (a: number, b: number, t: number): number => {
    return a + (b - a) * t;
  };

  // 根据时间戳插值计算位置和朝向
  const interpolateObjectState = (
    futureInfo: {
      points: number[][];
      timestamps: number[];
      rotation: number[];
    },
    targetTimestamp: number
  ): { position: number[]; rotation: number } | null => {
    if (!futureInfo || !futureInfo.points || !futureInfo.timestamps) {
      console.warn("Invalid futureInfo structure:", futureInfo);
      return null;
    }

    const { points, timestamps, rotation } = futureInfo;

    if (
      !Array.isArray(timestamps) ||
      !Array.isArray(points) ||
      timestamps.length === 0 ||
      points.length === 0
    ) {
      console.warn("Invalid arrays in futureInfo:", { timestamps, points });
      return null;
    }

    // 如果目标时间戳小于等于第一个时间戳
    if (targetTimestamp <= timestamps[0]) {
      return {
        position: points[0],
        rotation: rotation[0] + Math.PI / 2 || 0,
      };
    }

    // 如果目标时间戳大于等于最后一个时间戳
    if (targetTimestamp >= timestamps[timestamps.length - 1]) {
      return {
        position: points[points.length - 1],
        rotation: rotation[rotation.length - 1] + Math.PI / 2 || 0,
      };
    }

    // 找到目标时间戳所在的区间
    for (let i = 0; i < timestamps.length - 1; i++) {
      if (
        targetTimestamp >= timestamps[i] &&
        targetTimestamp <= timestamps[i + 1]
      ) {
        const t =
          (targetTimestamp - timestamps[i]) /
          (timestamps[i + 1] - timestamps[i]);

        // 插值位置
        const pos1 = points[i];
        const pos2 = points[i + 1];
        const interpolatedPosition = [
          lerp(pos1[0], pos2[0], t),
          lerp(pos1[1], pos2[1], t),
          2,
        ];

        // 插值朝向
        const rot1 = rotation[i] || 0;
        const rot2 = rotation[i + 1] || 0;
        const interpolatedRotation = lerp(rot1, rot2, t);

        return {
          position: interpolatedPosition,
          rotation: interpolatedRotation + Math.PI / 2,
        };
      }
    }

    return null;
  };

  // 渲染未来位置的方框
  const renderFutureObjects = (data: VisualizationData) => {
    if (!data.future_obj_infos || selectTimestamp <= 0) {
      return;
    }

    // // 添加调试信息
    // console.log('future_obj_infos:', data.future_obj_infos);
    // console.log('future_obj_infos type:', typeof data.future_obj_infos);
    // console.log('is array:', Array.isArray(data.future_obj_infos));

    const group = visualizationGroupRef.current!;

    // 确保 future_obj_infos 是数组
    let futureInfos: any[] = [];
    if (Array.isArray(data.future_obj_infos)) {
      futureInfos = data.future_obj_infos;
    } else if (typeof data.future_obj_infos === "object") {
      // 如果是对象，尝试转换为数组
      futureInfos = Object.values(data.future_obj_infos);
    } else {
      console.warn(
        "future_obj_infos is not in expected format:",
        data.future_obj_infos
      );
      return;
    }

    // 遍历选中的对象
    objectList.forEach((objectId) => {
      // 找到对应的未来信息
      const futureInfo = futureInfos.find(
        (info) => info && info.id === objectId.split("_")[1]
      );
      if (!futureInfo) {
        console.log(`No future info found for object ${objectId}`);
        return;
      }

      // 插值计算目标时间戳的位置和朝向
      const interpolatedState = interpolateObjectState(
        futureInfo,
        selectTimestamp
      );
      if (!interpolatedState) return;

      // 找到原始对象以获取尺寸和颜色信息
      const originalObject = data.objects.find((obj) => obj.id === objectId);
      if (!originalObject) return;

      // 创建未来位置的方框几何体
      const geometry = new THREE.BoxGeometry(
        originalObject.dimensions.width,
        originalObject.dimensions.height,
        originalObject.dimensions.depth
      );

      // 创建半透明材质，颜色与原来一致但更透明
      const material = new THREE.MeshStandardMaterial({
        color: originalObject.color,
        opacity: originalObject.opacity * 0.3, // 更透明
        transparent: true,
        wireframe: false,
      });

      // 创建网格
      const mesh = new THREE.Mesh(geometry, material);
      mesh.position.set(
        interpolatedState.position[0],
        interpolatedState.position[1],
        interpolatedState.position[2]
      );

      // 设置旋转，保持原有的x和y旋转，更新z旋转
      mesh.rotation.set(
        originalObject.rotation[0],
        originalObject.rotation[1],
        interpolatedState.rotation
      );

      mesh.userData = {
        type: "future_object",
        id: `future_${objectId}`,
        timestamp: selectTimestamp,
      };

      // 添加边框以便更好地识别
      const edges = new THREE.EdgesGeometry(geometry);
      const lineMaterial = new THREE.LineBasicMaterial({
        color: originalObject.color,
        opacity: 0.6,
        transparent: true,
      });
      const border = new THREE.LineSegments(edges, lineMaterial);
      mesh.add(border);

      group.add(mesh);
    });

    // 渲染自车未来轨迹
    renderEgoFutureTrajectory(data);
  };

  // 渲染自车未来轨迹
  const renderEgoFutureTrajectory = (data: VisualizationData) => {
    // 检查必要条件
    if (
      !pdpPaths ||
      !dlpTrajs ||
      highlightTrajIndex === null ||
      selectTimestamp <= 0
    ) {
      return;
    }

    // 获取第0条路径（自车行驶路径）
    const egoPath = pdpPaths[0];
    if (
      !egoPath ||
      !egoPath.visualization_points ||
      egoPath.visualization_points.length === 0
    ) {
      console.warn("No ego path found (index 0) or no visualization points");
      return;
    }

    // 获取选中的轨迹
    const selectedTraj = dlpTrajs.find(
      (traj) => traj.index === highlightTrajIndex
    );
    if (!selectedTraj || !selectedTraj.s || selectedTraj.s.length === 0) {
      console.warn("No selected trajectory found or no s values");
      return;
    }

    console.log("Rendering ego future trajectory:", {
      selectTimestamp,
      highlightTrajIndex,
      trajSLength: selectedTraj.s.length,
      pathPointsLength: egoPath.visualization_points.length,
    });

    // 计算目标时间戳对应的s值（使用插值）
    const targetS = interpolateSValue(selectedTraj.s, selectTimestamp);
    if (targetS === null) {
      console.warn(
        "Could not interpolate s value for timestamp:",
        selectTimestamp
      );
      return;
    }

    // 根据s值在路径上找到对应位置
    const egoPosition = interpolatePositionOnPath(
      egoPath.visualization_points,
      targetS
    );
    if (!egoPosition) {
      console.warn("Could not find position on path for s value:", targetS);
      return;
    }

    // 计算自车未来位置的朝向
    const egoHeading = calculateEgoHeading(
      egoPath.visualization_points,
      targetS
    );

    // 找到自车的原始对象以获取尺寸信息
    // 自车通常是第一个对象，或者ID包含特定标识
    const egoObject = data.objects[0]; // 如果找不到，使用第一个对象作为自车

    let egoDimensions = { width: 4.5, height: 1.8, depth: 2.0 }; // 默认自车尺寸
    let egoColor = 0x00ff00; // 默认绿色

    if (egoObject) {
      egoDimensions = egoObject.dimensions;
      egoColor = new THREE.Color(egoObject.color).getHex();
      console.log(
        "Found ego object:",
        egoObject.id,
        "dimensions:",
        egoDimensions
      );
    } else {
      console.warn("No ego object found, using default dimensions");
    }

    // 创建自车的未来位置方框
    const group = visualizationGroupRef.current!;

    const geometry = new THREE.BoxGeometry(
      egoDimensions.width,
      egoDimensions.height,
      egoDimensions.depth
    );

    // 创建半透明材质，颜色与原来一致但更透明
    const material = new THREE.MeshStandardMaterial({
      color: egoColor,
      opacity: 0.3, // 更透明
      transparent: true,
      wireframe: false,
    });

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(egoPosition[0], egoPosition[1], egoPosition[2] || 0.5);

    // 设置朝向
    mesh.rotation.set(0, 0, egoHeading + Math.PI / 2);

    mesh.userData = {
      type: "future_ego",
      id: `future_ego_${selectTimestamp}`,
      timestamp: selectTimestamp,
    };

    // 添加边框以便更好地识别
    const edges = new THREE.EdgesGeometry(geometry);
    const lineMaterial = new THREE.LineBasicMaterial({
      color: egoColor,
      opacity: 0.6,
      transparent: true,
    });
    const border = new THREE.LineSegments(edges, lineMaterial);
    mesh.add(border);

    group.add(mesh);
  };

  // 根据时间戳插值计算s值
  const interpolateSValue = (
    sValues: number[],
    targetTimestamp: number
  ): number | null => {
    if (!sValues || sValues.length === 0) return null;

    // s值数组是间隔0.2s的20个值，从0s开始
    const timeStep = 0.2;
    const maxTime = (sValues.length - 1) * timeStep;

    if (targetTimestamp <= 0) return sValues[0];
    if (targetTimestamp >= maxTime) return sValues[sValues.length - 1];

    // 找到对应的时间区间
    const index = targetTimestamp / timeStep;
    const lowerIndex = Math.floor(index);
    const upperIndex = Math.ceil(index);

    if (lowerIndex === upperIndex) {
      return sValues[lowerIndex];
    }

    // 线性插值
    const t = index - lowerIndex;
    return lerp(sValues[lowerIndex], sValues[upperIndex], t);
  };

  // 根据s值在路径上插值计算位置
  const interpolatePositionOnPath = (
    pathPoints: number[][],
    targetS: number
  ): number[] | null => {
    if (!pathPoints || pathPoints.length === 0) return null;

    // 计算路径上每个点的累积距离
    const distances = [0];
    for (let i = 1; i < pathPoints.length; i++) {
      const prev = pathPoints[i - 1];
      const curr = pathPoints[i];
      const dist = Math.sqrt(
        Math.pow(curr[0] - prev[0], 2) + Math.pow(curr[1] - prev[1], 2)
      );
      distances.push(distances[distances.length - 1] + dist);
    }

    const totalDistance = distances[distances.length - 1];

    // 如果目标s值超出路径长度，返回最后一个点
    if (targetS >= totalDistance) {
      return pathPoints[pathPoints.length - 1];
    }

    // 如果目标s值小于等于0，返回第一个点
    if (targetS <= 0) {
      return pathPoints[0];
    }

    // 找到目标s值所在的线段
    for (let i = 0; i < distances.length - 1; i++) {
      if (targetS >= distances[i] && targetS <= distances[i + 1]) {
        const segmentLength = distances[i + 1] - distances[i];
        if (segmentLength === 0) return pathPoints[i];

        const t = (targetS - distances[i]) / segmentLength;
        const p1 = pathPoints[i];
        const p2 = pathPoints[i + 1];

        return [
          lerp(p1[0], p2[0], t),
          lerp(p1[1], p2[1], t),
          lerp(p1[2] || 0, p2[2] || 0, t),
        ];
      }
    }

    return null;
  };

  // 计算自车在指定s值位置的朝向
  const calculateEgoHeading = (
    pathPoints: number[][],
    targetS: number
  ): number => {
    if (!pathPoints || pathPoints.length < 2) return 0;

    // 计算路径上每个点的累积距离
    const distances = [0];
    for (let i = 1; i < pathPoints.length; i++) {
      const prev = pathPoints[i - 1];
      const curr = pathPoints[i];
      const dist = Math.sqrt(
        Math.pow(curr[0] - prev[0], 2) + Math.pow(curr[1] - prev[1], 2)
      );
      distances.push(distances[distances.length - 1] + dist);
    }

    const totalDistance = distances[distances.length - 1];

    // 边界情况处理
    if (targetS <= 0) {
      // 使用前两个点计算朝向
      const p1 = pathPoints[0];
      const p2 = pathPoints[1];
      return Math.atan2(p2[1] - p1[1], p2[0] - p1[0]);
    }

    if (targetS >= totalDistance) {
      // 使用最后两个点计算朝向
      const p1 = pathPoints[pathPoints.length - 2];
      const p2 = pathPoints[pathPoints.length - 1];
      return Math.atan2(p2[1] - p1[1], p2[0] - p1[0]);
    }

    // 找到目标s值所在的线段
    for (let i = 0; i < distances.length - 1; i++) {
      if (targetS >= distances[i] && targetS <= distances[i + 1]) {
        const p1 = pathPoints[i];
        const p2 = pathPoints[i + 1];

        // 计算该线段的朝向
        return Math.atan2(p2[1] - p1[1], p2[0] - p1[0]);
      }
    }

    return 0;
  };

  // 渲染未来轨迹线条
  const renderFutureTrajectories = (data: VisualizationData) => {
    if (!data.future_obj_infos || !showFutureTrajectories) {
      return;
    }

    const group = visualizationGroupRef.current!;

    // 确保 future_obj_infos 是数组
    let futureInfos: any[] = [];
    if (Array.isArray(data.future_obj_infos)) {
      futureInfos = data.future_obj_infos;
    } else if (typeof data.future_obj_infos === "object") {
      // 如果是对象，尝试转换为数组
      futureInfos = Object.values(data.future_obj_infos);
    } else {
      console.warn(
        "future_obj_infos is not in expected format:",
        data.future_obj_infos
      );
      return;
    }

    // 遍历所有对象的未来轨迹信息
    futureInfos.forEach((futureInfo) => {
      if (
        !futureInfo ||
        !futureInfo.points ||
        futureInfo.points.length < 2 ||
        futureInfo.id == "0"
      ) {
        return;
      }
      const currentObject = data.objects.find(
        (obj) => obj.id.split("_")[1] === futureInfo.id
      );

      // 准备轨迹点坐标
      const positions: number[] = [];
      if (currentObject) {
        positions.push(
          currentObject.position[0],
          currentObject.position[1],
          currentObject.position[2] || 0.5
        );
      }
      futureInfo.points.forEach((point: number[]) => {
        positions.push(point[0], point[1], point[2] || 0.5);
      });

      // 创建轨迹线条几何体
      const geometry = new LineGeometry();
      geometry.setPositions(positions);

      // 创建半透明蓝色材质
      const material = new LineMaterial({
        color: 0x4a90e2, // 蓝色
        linewidth: 4,
        transparent: true,
        opacity: 0.6, // 透明度
        resolution: new THREE.Vector2(
          mountRef.current?.clientWidth || 800,
          mountRef.current?.clientHeight || 600
        ),
      });

      // 创建线条对象
      const line = new Line2(geometry, material);
      line.computeLineDistances();
      line.userData = {
        type: "future_trajectory",
        id: `future_traj_${futureInfo.id}`,
      };

      group.add(line);
    });
  };

  // 清除未来轨迹线条
  const clearFutureTrajectories = () => {
    if (!visualizationGroupRef.current) return;

    const objectsToRemove: THREE.Object3D[] = [];
    visualizationGroupRef.current.children.forEach((child) => {
      if (child.userData.type === "future_trajectory") {
        objectsToRemove.push(child);
      }
    });

    objectsToRemove.forEach((obj) => {
      visualizationGroupRef.current!.remove(obj);
    });
  };

  // 渲染预测轨迹线条
  const renderPredictionTrajectories = (data: VisualizationData) => {
    if (!data.future_obj_pred || !showPredictionTrajectories) {
      return;
    }

    const group = visualizationGroupRef.current!;

    // 确保 future_obj_pred 是数组
    let predictionInfos: any[] = [];
    if (Array.isArray(data.future_obj_pred)) {
      predictionInfos = data.future_obj_pred;
    } else if (typeof data.future_obj_pred === "object") {
      // 如果是对象，尝试转换为数组
      predictionInfos = Object.values(data.future_obj_pred);
    } else {
      console.warn(
        "future_obj_pred is not in expected format:",
        data.future_obj_pred
      );
      return;
    }

    predictionInfos.forEach((predInfo) => {
      if (!predInfo.points || predInfo.points.length < 2) {
        return;
      }

      // 尝试找到对应的当前对象
      // 现在预测轨迹的ID格式与future_obj_infos一致，都是对象索引（如"0", "1", "2"等）
      // 我们可以直接根据ID来匹配对应的对象
      // let currentObject: any = null;

      // 根据预测轨迹的ID直接匹配对象
      // predInfo.id 现在是对象索引，如"0", "1", "2"等
      // if (predInfo.id && data.objects) {
      //   // 查找ID中包含对应索引的对象
      //   currentObject = data.objects.find(
      //     (obj) => obj.id.split("_")[1] === predInfo.id && obj.id.split("_")[2] === "5"
      //   );
      // }

      // 准备轨迹点坐标
      const positions: number[] = [];

      // 如果找到了对应的当前对象，将其位置作为起点
      // if (currentObject) {
      //   positions.push(
      //     currentObject.position[0],
      //     currentObject.position[1],
      //     currentObject.position[2] || 0.5
      //   );
      // }

      // 添加预测轨迹的所有点
      predInfo.points.forEach((point: number[]) => {
        positions.push(point[0], point[1], point[2] || 0.5);
      });

      // 创建轨迹线条几何体
      const geometry = new LineGeometry();
      geometry.setPositions(positions);

      // 创建绿色材质
      const material = new LineMaterial({
        color: 0x039d00, // 绿色
        linewidth: 3,
        transparent: true,
        opacity: 0.5, // 透明度
        resolution: new THREE.Vector2(
          mountRef.current?.clientWidth || 800,
          mountRef.current?.clientHeight || 600
        ),
      });

      // 创建线条对象
      const line = new Line2(geometry, material);
      line.computeLineDistances();
      line.userData = {
        type: "prediction_trajectory",
        id: `pred_traj_${predInfo.id}`,
      };

      group.add(line);
    });
  };

  // 清除预测轨迹线条
  const clearPredictionTrajectories = () => {
    if (!visualizationGroupRef.current) return;

    const objectsToRemove: THREE.Object3D[] = [];
    visualizationGroupRef.current.children.forEach((child) => {
      if (child.userData.type === "prediction_trajectory") {
        objectsToRemove.push(child);
      }
    });

    objectsToRemove.forEach((obj) => {
      visualizationGroupRef.current!.remove(obj);
    });
  };

  // 清除未来对象
  const clearFutureObjects = () => {
    if (!visualizationGroupRef.current) return;

    const objectsToRemove: THREE.Object3D[] = [];
    visualizationGroupRef.current.children.forEach((child) => {
      if (
        child.userData.type === "future_object" ||
        child.userData.type === "future_ego"
      ) {
        objectsToRemove.push(child);
      }
    });

    objectsToRemove.forEach((obj) => {
      visualizationGroupRef.current!.remove(obj);
    });
  };

  // 基础可视化渲染
  const fetchAndRenderVisualization = async (filePath?: string) => {
    setLoading(true);
    setError(null);
    try {
      const resp = await axios.post("/api/visualize-pickle-rotate", {
        pickle_path: filePath,
        config: {},
      });
      const data: VisualizationData = resp.data;
      if (data.tbt) {
        setTbtInfo(data.tbt);
      } else {
        setTbtInfo(null);
      }
      if (data.traffic_lights) {
        setTraffic_lights_Info(data.traffic_lights);
      } else {
        setTraffic_lights_Info(null);
      }
      // 渲染到 visualizationGroup
      if (visualizationGroupRef.current) {
        clearGroup(visualizationGroupRef.current);
        renderBasicData(data);
      }
      // if (pdpPaths && Object.keys(pdpPaths).length > 0) {
      renderPdpPaths();
      // }
      // 保存当前可视化数据
      setCurrentVisualizationData(data);
    } catch (err) {
      setError("获取可视化数据失败，请检查连接");
      setTbtInfo(null);
      setTraffic_lights_Info(null);
    } finally {
      setLoading(false);
    }
  };

  // 单独渲染基础部分（polygons, polylines, arrows, objects）
  const renderBasicData = (data: VisualizationData) => {
    // console.log('selectedObjectIds', selectedObjectIds)
    // console.log('Object list',objectList);

    const group = visualizationGroupRef.current!;
    let ego = data.metadata?.ego_heading ?? (data.objects[0]?.rotation[2] || 0);
    group.rotation.set(0, 0, -ego );
    const parent = inferenceGroupRef.current!;
    parent.rotation.z = visualizationGroupRef.current?.rotation.z || 0;
    const hasGroundTruth =
      pdpPaths &&
      Object.values(pdpPaths).some((path) => path.is_ground_truth === true);

    // 渲染多边形
    data.polygons.forEach((polygon) => {
      const points: THREE.Vector3[] = [];
      for (const vertex of polygon.vertices) {
        points.push(new THREE.Vector3(vertex[0], vertex[1], vertex[2]));
      }

      const shape = new THREE.Shape();
      shape.moveTo(points[0].x, points[0].y);
      for (let i = 1; i < points.length; i++) {
        shape.lineTo(points[i].x, points[i].y);
      }
      shape.lineTo(points[0].x, points[0].y);

      const geometry = new THREE.ShapeGeometry(shape);
      const material = new THREE.MeshStandardMaterial({
        color: polygon.color,
        transparent: true,
        opacity: polygon.opacity,
        side: THREE.DoubleSide,
      });

      const mesh = new THREE.Mesh(geometry, material);
      mesh.userData = { type: "visualization", id: polygon.id };
      group.add(mesh);
    });

    // 渲染多段线
    data.polylines.forEach((polyline) => {
      if (!showGroundTruth && polyline.id === "ego_path") {
        return; // 如果不显示地面真实路径且当前是ego路径，则跳过渲染
      }
      // if (hasGroundTruth && polyline.id === "ego_path") {
      //   return;
      // }
      const positions: number[] = [];
      polyline.points.forEach((p) => {
        positions.push(p[0], p[1], p[2] || 0);
      });

      const geometry = new LineGeometry();
      geometry.setPositions(positions);

      const material = new LineMaterial({
        color: polyline.color,
        linewidth: polyline.thickness * 30, // 需要适当的比例转换，因为这里单位不同
        vertexColors: false,
        dashed: polyline.type === "dashed",
        resolution: new THREE.Vector2(
          mountRef.current?.clientWidth || 800,
          mountRef.current?.clientHeight || 600
        ),
      });

      const line = new Line2(geometry, material);
      line.computeLineDistances();
      line.userData = { type: "visualization", id: polyline.id };
      group.add(line);
    });

    data.arrows.forEach((arrow) => {
      const startVec = new THREE.Vector3(
        arrow.start[0],
        arrow.start[1],
        arrow.start[2]
      );
      const endVec = new THREE.Vector3(
        arrow.end[0],
        arrow.end[1],
        arrow.end[2]
      );

      const arrowLength = startVec.distanceTo(endVec);

      // 如果箭头长度过小，则不渲染，以避免计算错误
      if (arrowLength < 0.0001) {
        return;
      }

      const direction = endVec.clone().sub(startVec).normalize();

      // 定义箭头头部（锥形）的尺寸
      // coneHeight 对应 ArrowHelper 中的 headLength
      // const coneHeight = arrow.headSize * arrowLength;
      // // coneRadius 对应 ArrowHelper 中的 headWidth / 2
      // // ArrowHelper 的 headWidth 参数是直径，ConeGeometry 需要半径
      // const coneRadius = (arrow.headSize * arrowLength) / 2;
     // 绘制箭头头部
     const headSize = 1.5; // 箭头头部大小
     const coneHeight = headSize;
     const coneRadius = headSize / 3;
      // 如果计算出的头部尺寸过小或为零，则不渲染
      if (coneHeight <= 0.0001 || coneRadius <= 0.0001) {
        return;
      }

      // 创建锥形几何体
      const coneGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 16); // 参数: radius, height, radialSegments
      const coneMaterial = new THREE.MeshStandardMaterial({
        color: new THREE.Color(arrow.color),
      });
      const coneMesh = new THREE.Mesh(coneGeometry, coneMaterial);

      // 定位锥形
      // ConeGeometry 默认沿Y轴正方向，尖端在 Y = coneHeight / 2
      // 我们需要将锥体重心定位，使得其尖端在 arrow.end 位置
      // 锥体重心 = endVec - direction * (coneHeight / 2)
      coneMesh.position
        .copy(endVec)
        .addScaledVector(direction, -coneHeight / 2);

      // 设置锥形的方向
      // 默认锥体指向Y轴正方向 (0,1,0)
      const yAxis = new THREE.Vector3(0, 1, 0);
      coneMesh.quaternion.setFromUnitVectors(yAxis, direction);

      coneMesh.userData = {
        type: "visualization",
        id: arrow.id,
        isArrowhead: true,
      };
      group.add(coneMesh);
    });

    // 渲染3D对象
    data.objects.forEach((obj) => {
      const checked = selectedObjectIds?.includes(String(obj.id));
      const geometry = new THREE.BoxGeometry(
        obj.dimensions.width,
        obj.dimensions.height,
        obj.dimensions.depth
      );

      // 主物体材质
      const material = new THREE.MeshStandardMaterial({
        color: checked ? "#9900ff" : obj.color,
        opacity: obj.opacity,
        transparent: true,
      });

      // 创建主物体
      const mesh: any = new THREE.Mesh(geometry, material);
      mesh.position.set(obj.position[0], obj.position[1], obj.position[2]);
      mesh.rotation.set(obj.rotation[0], obj.rotation[1], obj.rotation[2]);
      mesh.userData = { type: "visualization", id: obj.id };
      mesh.defaultColor = obj.color;
      mesh.name = obj.id;

      // 创建边框几何体
      const edges = new THREE.EdgesGeometry(geometry);
      const lineMaterial = new THREE.LineBasicMaterial({
        color: 0xff9900,
        linewidth: 2,
      });

      // 创建边框对象
      const border = new THREE.LineSegments(edges, lineMaterial);
      border.visible = checked ?? false;
      mesh.add(border);
      // 添加朝向指示器 - 使用ArrowHelper
      const addOrientationArrow = () => {
        // 箭头长度
        const arrowLength =
          Math.max(
            obj.dimensions.width,
            obj.dimensions.height,
            obj.dimensions.depth
          ) * 0.4;

        // 创建ArrowHelper
        const arrowHelper = new THREE.ArrowHelper(
          new THREE.Vector3(0, -1, 0), // 方向 - 沿Y轴正方向
          new THREE.Vector3(0, -0.5, 4), // 起点位置 - 物体前方
          arrowLength, // 长度
          0xfff700, // 颜色 - 红色
          arrowLength * 0.7, // 头部长度
          arrowLength * 0.7 // 头部宽度
        );
        // 使用更可靠的方式设置线宽
        const lineMaterial = arrowHelper.line
          .material as THREE.LineBasicMaterial;
        lineMaterial.transparent = true;
        lineMaterial.opacity = 0.5;

        // 同时也需要设置锥体的透明度
        const coneMaterial = arrowHelper.cone.material as THREE.Material;
        coneMaterial.transparent = true;
        coneMaterial.opacity = 0.5;
        mesh.add(arrowHelper);
      };

      // 调用添加朝向箭头
      addOrientationArrow();
      group.add(mesh);
    });

    data.texts.forEach((text, index) => {
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      if (context) {
        const id = data.objects[index].id;
        canvas.width = 400;
        canvas.height = 200;
        context.font = `${text.fontSize}px Arial`;
        context.textBaseline = "top";
        context.fillStyle = text.color;
        // 计算行高
        const lineHeight = text.fontSize * 1.2;
        // 第一行文本
        context.fillText(id, 130, 70);
        // 第二行文本
        context.fillText(text.content, 130, 70 + lineHeight);
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({
          map: texture,
          transparent: true,
        });

        const sprite = new THREE.Sprite(spriteMaterial);
        sprite.position.set(
          text.position[0],
          text.position[1],
          text.position[2]
        );
        sprite.scale.set(10, 5, 1);
        sprite.userData = { type: "visualization", id: id };

        group.add(sprite);
      }
    });

    // if (data.recommend_lanes) {
    //   data.recommend_lanes.forEach((lane) => {
    //     const startVec = new THREE.Vector3(
    //       lane.start[0],
    //       lane.start[1],
    //       lane.start[2] || 0
    //     );
    //     const endVec = new THREE.Vector3(
    //       lane.end[0],
    //       lane.end[1],
    //       lane.end[2] || 0
    //     );

    //     const laneLength = startVec.distanceTo(endVec);

    //     // 如果车道长度过小，则不渲染
    //     if (laneLength < 0.0001) {
    //       return;
    //     }

    //     const direction = endVec.clone().sub(startVec).normalize();

    //     const positions = [
    //       startVec.x,
    //       startVec.y,
    //       startVec.z,
    //       endVec.x,
    //       endVec.y,
    //       endVec.z,
    //     ];

    //     const lineGeometry = new LineGeometry();
    //     lineGeometry.setPositions(positions);
    //     const lineMaterial = new LineMaterial({
    //       color: 0xf4f801,
    //       transparent: true,
    //       opacity: 0.8, // 更透明
    //       linewidth: 4, // 更LineBasicMaterial粗
    //       resolution: new THREE.Vector2(
    //         mountRef.current?.clientWidth || 800,
    //         mountRef.current?.clientHeight || 600
    //       ),
    //     });
    //     const line = new Line2(lineGeometry, lineMaterial);
    //     line.userData = { type: "visualization", id: `lane_line_${lane.id}` };
    //     group.add(line);

    //     // 绘制箭头头部（更大、更透明）
    //     const headSize = 1.8; // 增大箭头尺寸
    //     const coneHeight = headSize;
    //     const coneRadius = headSize / 2;

    //     // 如果计算出的头部尺寸过小或为零，则不渲染
    //     if (coneHeight <= 0.0001 || coneRadius <= 0.0001) {
    //       return;
    //     }

    //     // 创建锥形几何体
    //     const coneGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 16);
    //     const coneMaterial = new THREE.MeshStandardMaterial({
    //       color: 0xf4f801,
    //       transparent: true,
    //       opacity: 1, // 更透明
    //     });
    //     const coneMesh = new THREE.Mesh(coneGeometry, coneMaterial);

    //     // 定位锥形
    //     coneMesh.position
    //       .copy(endVec)
    //       .addScaledVector(direction, -coneHeight / 2);

    //     // 设置锥形的方向
    //     const yAxis = new THREE.Vector3(0, 1, 0);
    //     coneMesh.quaternion.setFromUnitVectors(yAxis, direction);

    //     coneMesh.userData = {
    //       type: "visualization",
    //       id: `lane_arrow_${lane.id}`,
    //       isArrowhead: true,
    //     };
    //     group.add(coneMesh);
    //   });
    // }

    // 渲染centerlines_for_drivable箭头
    if (data.centerlines_for_drivable) {
      data.centerlines_for_drivable.forEach((centerline) => {
        if (!centerline.points || centerline.points.length < 2) {
          return; // 至少需要2个点才能绘制箭头
        }

        const points = centerline.points;
        const isSelected = props.selectedCenterlineIds?.includes(centerline.id) || false;

        // 根据选中状态和推荐标签确定颜色
        let color: number;
        if (isSelected) {
          color = 0x01E8F8; 
        } else {
          color = centerline.points_recommend_label === 1 ? 0xffff00 : 0xffffff; // 1为黄色，0为白色
        }

        // 为每对相邻的点绘制箭头
        for (let i = 0; i < points.length - 1; i++) {
          const startPoint = points[i];
          const endPoint = points[i + 1];

          const startVec = new THREE.Vector3(
            startPoint[0],
            startPoint[1],
            startPoint[2] || 0.1
          );
          const endVec = new THREE.Vector3(
            endPoint[0],
            endPoint[1],
            endPoint[2] || 0.1
          );

          const arrowLength = startVec.distanceTo(endVec);

          // 如果箭头长度过小，则不渲染
          if (arrowLength < 0.0001) {
            continue;
          }

          const direction = endVec.clone().sub(startVec).normalize();

          // 绘制箭头线条
          const positions = [
            startVec.x,
            startVec.y,
            startVec.z,
            endVec.x,
            endVec.y,
            endVec.z,
          ];

          const lineGeometry = new LineGeometry();
          lineGeometry.setPositions(positions);
          const lineMaterial = new LineMaterial({
            color: color,
            transparent: true,
            opacity: 0.8,
            linewidth: 8,
            resolution: new THREE.Vector2(
              mountRef.current?.clientWidth || 800,
              mountRef.current?.clientHeight || 600
            ),
          });
          const line = new Line2(lineGeometry, lineMaterial);
          line.userData = {
            type: "visualization",
            id: `centerline_line_${centerline.id}_${i}`,
            centerlineId: centerline.id,
            isCenterline: true,
            isInteractable: true
          };
          line.name = centerline.id; // 设置name属性用于点击检测
          (line as any).defaultColor = centerline.points_recommend_label === 1 ? "#ffff00" : "#ffffff";
          group.add(line);

          // 绘制箭头头部
          const headSize = 2.5; // 箭头头部大小
          const coneHeight = headSize;
          const coneRadius = headSize / 3;

          if (coneHeight > 0.0001 && coneRadius > 0.0001) {
            // 创建锥形几何体
            const coneGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 8);
            const coneMaterial = new THREE.MeshStandardMaterial({
              color: color,
              transparent: true,
              opacity: 0.8,
            });
            const coneMesh = new THREE.Mesh(coneGeometry, coneMaterial);

            // 定位锥形 - 放在线段的中点
            const midPoint = startVec.clone().lerp(endVec, 0.7); // 稍微偏向终点
            coneMesh.position.copy(midPoint);

            // 设置锥形的方向
            const yAxis = new THREE.Vector3(0, 1, 0);
            coneMesh.quaternion.setFromUnitVectors(yAxis, direction);

            coneMesh.userData = {
              type: "visualization",
              id: `centerline_arrow_${centerline.id}_${i}`,
              centerlineId: centerline.id,
              isCenterlineArrow: true,
              isInteractable: true
            };
            coneMesh.name = centerline.id; // 设置name属性用于点击检测
            (coneMesh as any).defaultColor = centerline.points_recommend_label === 1 ? "#ffff00" : "#ffffff";
            group.add(coneMesh);
          }
        }
      });
    }
  };

  // 仅渲染推理轨迹
  const getPathColor = (path: PdpPathInfo, isHighlighted: boolean): number => {
    if (isHighlighted) {
      return 0xffff00; // 高亮黄色
    }
    if (path.color) {
      // 如果提供了自定义颜色，将十六进制字符串转换为数字
      const colorStr = path.color.replace("#", "");
      return parseInt(colorStr, 16);
    }

    if (path.annotation?.annotation) {
      switch (path.annotation.annotation.toLowerCase()) {
        case "good":
          return 0x00ff00; // 绿色
        case "bad":
          return 0xff0000; // 红色
        case "unknown":
          return 0x0000ff; // 蓝色
        default:
          return 0x808080; // 灰色（默认）
      }
    }

    return 0x808080; // 无标注时为灰色
  };
  const getPathLineWidth = (
    path: PdpPathInfo,
    isHighlighted: boolean
  ): number => {
    // 如果高亮，使用高亮宽度
    if (isHighlighted) {
      return 4;
    }

    // 新增：使用自定义宽度，如果提供的话
    if (path.lineWidth !== undefined) {
      return path.lineWidth;
    }

    // 默认宽度
    return 1;
  };

  const renderPdpPaths = () => {
    if (
      !pdpPaths ||
      Object.keys(pdpPaths).length === 0 ||
      !inferenceGroupRef.current
    ) {
      console.log("No PDP paths to render");
      return;
    }
    clearGroup(inferenceGroupRef.current);
    Object.values(pdpPaths).forEach((path) => {
      if (!path.visualization_points || path.visualization_points.length < 2)
        return;

      const points = path.visualization_points;
      const positions: number[] = [];

      // 将点坐标展平为一维数组
      points.forEach((point) => {
        positions.push(point[0], point[1], point[2] || 0.5); // 如果没有z坐标,默认为0.5
      });

      const geometry = new LineGeometry();
      geometry.setPositions(positions);

      const isHighlighted = path.index === highlightPathIndex;
      const color = getPathColor(path, isHighlighted);
      // const lineWidth = isHighlighted ? 4 : 2;
      const lineWidth = getPathLineWidth(path, isHighlighted);

      const material = new LineMaterial({
        color: color,
        linewidth: lineWidth,
        resolution: new THREE.Vector2(
          mountRef.current?.clientWidth || 800,
          mountRef.current?.clientHeight || 600
        ),
      });

      const line = new Line2(geometry, material);
      line.computeLineDistances();
      line.userData = {
        type: "pdp_path",
        id: `path_${path.index}`,
        probability: path.probability,
      };

      inferenceGroupRef.current!.add(line);

      const middle_point = path.middle_point || [0, 0, 0];
      const sphereGeometry = new THREE.SphereGeometry(0.4, 16, 16);
      const sphereMaterial = new THREE.MeshStandardMaterial({
        color: 0xff0000,
      });
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
      sphere.position.set(
        middle_point[0],
        middle_point[1],
        middle_point[2] || 0.5
      );
      sphere.userData = {
        type: "pdp_path",
        id: `path_${path.index}_sphere`,
      };
      inferenceGroupRef.current!.add(sphere);
    });
  };

  // // 添加到现有 useEffect 中,在评测数据加载后渲染PDP路径
  useEffect(() => {
    // 如果有 PDP 路径数据,渲染这些路径
    if (pdpPaths && Object.keys(pdpPaths).length > 0) {
      renderPdpPaths();
    }
  }, [pdpPaths]);

  // 监听高亮路径索引变化
  useEffect(() => {
    if (!pdpPaths || !inferenceGroupRef.current) return;
    // renderPdpPaths();

    inferenceGroupRef.current.children.forEach((child) => {
      if (child.userData.type === "pdp_path") {
        const pathIndex = parseInt(child.userData.id.split("_")[1]);
        const path = pdpPaths[pathIndex];
        if (!path) return;

        const isHighlighted = pathIndex === highlightPathIndex;

        // 新增：根据hideOtherPaths状态控制可见性
        if (hideOtherPaths && highlightPathIndex !== null) {
          child.visible = isHighlighted;
        } else {
          child.visible = true;
        }

        // 如果路径可见，更新其样式
        if (child.visible) {
          const material = (child as Line2).material as LineMaterial;
          const color = getPathColor(path, isHighlighted);
          const lineWidth = isHighlighted ? 2 : 1;

          material.color.set(color);
          material.linewidth = lineWidth;
        }
      }
    });
  }, [highlightPathIndex, hideOtherPaths]); // 新增hideOtherPaths依赖

  // evaluationCase 变更：全量重绘基础，可复用或获取轨迹
  useEffect(() => {
    if (!evaluationCase?.pkl_dir || !evaluationCase.pkl_name) return;
    const fullPath = `${evaluationCase.pkl_dir}/${evaluationCase.pkl_name}`;
    // 全量清空
    clearGroup(visualizationGroupRef.current);
    clearGroup(inferenceGroupRef.current);
    // 清除未来轨迹
    clearFutureTrajectories();
    // 重绘基础
    fetchAndRenderVisualization(fullPath);
    // console.log('fetchAndRenderVisualization');
  }, [evaluationCase]);

  // 监听时间戳和对象列表变化，重新渲染未来对象
  useEffect(() => {
    if (!currentVisualizationData) return;

    // 先清除现有的未来对象
    clearFutureObjects();

    // 如果时间戳大于0，则渲染未来对象
    if (selectTimestamp > 0) {
      // 渲染选中对象的未来位置
      if (objectList.length > 0) {
        renderFutureObjects(currentVisualizationData);
      }

      // 渲染自车未来轨迹（如果有选中的轨迹）
      if (highlightTrajIndex !== null) {
        renderEgoFutureTrajectory(currentVisualizationData);
      }
    }
  }, [
    selectTimestamp,
    objectList,
    highlightTrajIndex,
    currentVisualizationData,
  ]);

  // 监听显示未来轨迹状态变化
  useEffect(() => {
    if (!currentVisualizationData) return;

    // 先清除现有的未来轨迹
    clearFutureTrajectories();

    // 如果需要显示未来轨迹，则渲染
    if (showFutureTrajectories) {
      renderFutureTrajectories(currentVisualizationData);
    }
  }, [showFutureTrajectories, currentVisualizationData]);

  // 监听显示预测轨迹状态变化
  useEffect(() => {
    if (!currentVisualizationData) return;

    // 先清除现有的预测轨迹
    clearPredictionTrajectories();

    // 如果需要显示预测轨迹，则渲染
    if (showPredictionTrajectories) {
      renderPredictionTrajectories(currentVisualizationData);
    }
  }, [showPredictionTrajectories, currentVisualizationData]);

  // 获得坐标位置
  const getGeolocation = (
    e: MouseEvent,
    renderer: THREE.WebGLRenderer,
    camera: THREE.OrthographicCamera
  ) => {
    if (!renderer?.domElement) return;
    const canvas = renderer.domElement;
    const rect = canvas.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / canvas.clientWidth) * 2 - 1;
    const y = -((e.clientY - rect.top) / canvas.clientHeight) * 2 + 1;
    const worldPos = new THREE.Vector3(x, y, 0).unproject(camera);
    return new THREE.Vector3(worldPos.x, worldPos.y, 0);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="bg-gray-100 p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          {(
            <div
              style={{
                background: "white",
                padding: "10px",
                borderRadius: "0.5rem",
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "center",
                  gap: "1.5rem",
                }}
              >
                {/* <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#eeeeee",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  <span style={{ fontWeight: "bold" ,fontSize: "0.9rem"}}>
                    x:{geolocation?.x} y:{geolocation?.y}
                  </span>
                </div> */}

                {tbtInfo && (
                  <>
                  <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#e6f7ff",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>距离:</span> */}
                  <span style={{ fontWeight: "bold" }}>
                    {Math.round(tbtInfo.dist)}m
                  </span>
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#f6ffed",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>操作:</span> */}
                  <span style={{ fontWeight: "bold" }}>{tbtInfo.maneuver}</span>
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#f9f0ff",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>推荐车道:</span> */}
                  <span style={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
                    {tbtInfo.lane_action}
                  </span>
                </div>
                </>
                )}

                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    background: "#f9f0ff",
                    padding: "0.5rem 1rem",
                    borderRadius: "0.5rem",
                    boxShadow: "0 1px 4px rgba(0, 0, 0, 0.1)",
                  }}
                >
                  {/* <span style={{ fontWeight: 500, marginRight: '0.5rem' }}>推荐车道:</span> */}
                  <span
                    style={{
                      fontWeight: "bold",
                      whiteSpace: "nowrap",
                      color: traffic_lights_Info?.color,
                    }}
                  >
                    {traffic_lights_Info?.sign}
                  </span>
                </div>
                
                
              </div>
            </div>
          )}
        </div>
        {error && <p className="text-red-500 mt-2">{error}</p>}
        {loading && <p className="text-blue-500 mt-2">正在加载可视化数据...</p>}
      </div>

      <div ref={mountRef} className="flex-grow" style={{ height }}></div>
    </div>
  );
};

export default RotatePickleVisualizer;
