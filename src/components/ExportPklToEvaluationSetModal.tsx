import React, { useState, useEffect } from "react";
import {
  Modal,
  Input,
  Table,
  Button,
  message,
  Spin,
  Space,
  Alert,
  Pagination,
  Typography,
} from "antd";
import { SearchOutlined } from "@ant-design/icons";
import axios from "axios";
import { EvaluationSet, EvaluationCase } from "../types";

interface ExportPklToEvaluationSetModalProps {
  visible: boolean;
  onClose: () => void;
  selectedPkl: EvaluationCase | null;
  currentEvaluationSetId: string;
}

const ExportPklToEvaluationSetModal: React.FC<ExportPklToEvaluationSetModalProps> = ({
  visible,
  onClose,
  selectedPkl,
  currentEvaluationSetId,
}) => {
  const [availableEvaluationSets, setAvailableEvaluationSets] = useState<EvaluationSet[]>([]);
  const [selectedTargetSetId, setSelectedTargetSetId] = useState<number | null>(null);
  const [exportLoading, setExportLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    pages: 1,
  });
  const [filters, setFilters] = useState({
    name: "",
    creator: "",
  });

  // 处理分页变化
  const handlePageChange = (page: number) => {
    loadAvailableEvaluationSets(page, filters.name, filters.creator);
  };

  // 处理筛选条件变化
  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
  };

  // 应用筛选
  const applyFilters = () => {
    loadAvailableEvaluationSets(1, filters.name, filters.creator);
  };

  // 重置筛选
  const resetFilters = () => {
    setFilters({ name: "", creator: "" });
    loadAvailableEvaluationSets(1, "", "");
  };

  // 加载可用的评测集列表
  const loadAvailableEvaluationSets = async (
    page = 1,
    nameFilter = filters.name,
    creatorFilter = filters.creator
  ) => {
    setTableLoading(true);
    try {
      const response = await axios.get("/api/evaluation_sets", {
        params: {
          page,
          per_page: pagination.pageSize,
          name: nameFilter || undefined,
          creator: creatorFilter || undefined,
        },
      });

      if (response.data.success) {
        // 过滤掉当前评测集
        const filteredSets = response.data.data.filter(
          (set: EvaluationSet) => set.id !== parseInt(currentEvaluationSetId)
        );
        setAvailableEvaluationSets(filteredSets);
        setPagination({
          current: response.data.page,
          pageSize: response.data.per_page,
          total: response.data.total,
          pages: response.data.pages,
        });
      } else {
        message.error("加载评测集列表失败");
      }
    } catch (error) {
      console.error("加载评测集列表失败:", error);
      message.error("加载评测集列表失败");
    } finally {
      setTableLoading(false);
    }
  };

  // 执行导出操作
  const handleExportToEvaluationSet = async () => {
    if (!selectedPkl || !selectedTargetSetId) {
      message.warning("请选择目标评测集");
      return;
    }

    setExportLoading(true);
    try {
      const response = await axios.post(
        `/api/evaluation_sets/${selectedTargetSetId}/cases`,
        {
          case_ids: [selectedPkl.id],
        }
      );

      if (response.data.success) {
        message.success("PKL文件导出成功");
        onClose();
        setSelectedTargetSetId(null);
      } else {
        message.error(response.data.error || "导出失败");
      }
    } catch (error: any) {
      console.error("导出失败:", error);
      if (error.response?.status === 409) {
        message.warning("该PKL文件已存在于目标评测集中");
      } else {
        message.error("导出失败，请重试");
      }
    } finally {
      setExportLoading(false);
    }
  };

  // 弹窗打开时加载数据
  useEffect(() => {
    if (visible) {
      loadAvailableEvaluationSets();
      setSelectedTargetSetId(null);
    }
  }, [visible]);

  // 表格列定义
  const columns = [
    {
      title: "评测集名称",
      dataIndex: "set_name",
      key: "set_name",
      width: 200,
    },
    {
      title: "创建人",
      dataIndex: "creator_name",
      key: "creator_name",
      width: 120,
    },
    {
      title: "案例数量",
      dataIndex: "case_count",
      key: "case_count",
      width: 100,
      render: (count: number) => count || "计算中...",
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      width: 150,
      render: (time: string) => (time ? new Date(time).toLocaleDateString() : "-"),
    },
  ];

  return (
    <Modal
      title="导出PKL到其他评测集"
      open={visible}
      onOk={handleExportToEvaluationSet}
      onCancel={() => {
        onClose();
        setSelectedTargetSetId(null);
      }}
      confirmLoading={exportLoading}
      okText="确认导出"
      cancelText="取消"
      width={800}
      okButtonProps={{
        disabled: !selectedTargetSetId,
      }}
    >
      <div style={{ marginBottom: 16 }}>
        <Typography.Text strong>
          当前PKL文件: {selectedPkl?.pkl_name}
        </Typography.Text>
      </div>

      <Space direction="vertical" style={{ width: "100%" }} size="middle">
        <div className="filter-section">
          <Input
            placeholder="按评测集名称筛选"
            value={filters.name}
            onChange={(e) => handleFilterChange("name", e.target.value)}
            style={{ width: 200, marginRight: 16 }}
            allowClear
          />
          <Input
            placeholder="按创建人筛选"
            value={filters.creator}
            onChange={(e) => handleFilterChange("creator", e.target.value)}
            style={{ width: 200, marginRight: 16 }}
            allowClear
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={applyFilters}
          >
            筛选
          </Button>
          <Button onClick={resetFilters} style={{ marginLeft: 8 }}>
            重置
          </Button>
        </div>

        <Spin spinning={tableLoading}>
          <Table
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectedTargetSetId ? [selectedTargetSetId] : [],
              onChange: (selectedRowKeys) => {
                setSelectedTargetSetId(selectedRowKeys[0] as number || null);
              },
            }}
            columns={columns}
            dataSource={availableEvaluationSets}
            rowKey="id"
            pagination={false}
            locale={{ emptyText: "暂无可用的评测集" }}
            size="small"
            scroll={{ y: 300 }}
          />
          {pagination.total > 0 && (
            <div style={{ marginTop: 16, textAlign: 'center' }}>
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                showSizeChanger={false}
                showTotal={(total) => `共 ${total} 条记录`}
                size="small"
              />
            </div>
          )}
        </Spin>

        {selectedTargetSetId && (
          <Alert
            message={`已选择评测集: ${availableEvaluationSets.find(set => set.id === selectedTargetSetId)?.set_name}`}
            type="success"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Space>
    </Modal>
  );
};

export default ExportPklToEvaluationSetModal;
