// src/components/EvaluationSetSelector.tsx
import React from 'react';
import { Spin, Empty, Card, Tag } from 'antd';
import { CheckCircleFilled } from '@ant-design/icons';
import './EvaluationSetSelector.css';

interface EvaluationSet {
    id: number;
    set_name: string;
    creator_name: string;
    description: string;
    created_at?: string;
}

interface EvaluationSetSelectorProps {
    evaluationSets: EvaluationSet[];
    selectedSets: number[];
    onToggleSet: (id: number) => void;
    loading: boolean;
}

const EvaluationSetSelector: React.FC<EvaluationSetSelectorProps> = ({
    evaluationSets,
    selectedSets,
    onToggleSet,
    loading
}) => {
    // 按创建者分组评测集
    const groupedSets = evaluationSets.reduce((acc, set) => {
        if (!acc[set.creator_name]) {
            acc[set.creator_name] = [];
        }
        acc[set.creator_name].push(set);
        return acc;
    }, {} as Record<string, EvaluationSet[]>);

    if (loading) {
        return <div className="loading-container"><Spin size="large" tip="加载评测集..." /></div>;
    }

    if (evaluationSets.length === 0) {
        return <Empty description="暂无可用的评测集" />;
    }

    return (
        <div className="evaluation-set-selector">
            <h2 className="section-title">选择评测数据集</h2>

            <div className="selection-count">
                已选择 {selectedSets.length} 个评测集
            </div>

            <div className="creator-groups">
                {Object.entries(groupedSets).map(([creator, sets]) => (
                    <div key={creator} className="creator-group">
                        <h3 className="creator-name">创建者: {creator}</h3>
                        <div className="sets-grid">
                            {sets.map(set => (
                                <Card
                                    key={set.id}
                                    className={`set-card ${selectedSets.includes(set.id) ? 'selected' : ''}`}
                                    onClick={() => onToggleSet(set.id)}
                                    hoverable
                                >
                                    {selectedSets.includes(set.id) && (
                                        <div className="selection-indicator">
                                            <CheckCircleFilled />
                                        </div>
                                    )}
                                    <div className="set-name">{set.set_name}</div>
                                    <div className="set-description">{set.description}</div>
                                    <div className="set-meta">
                                        {set.created_at && (
                                            <Tag color="blue">{new Date(set.created_at).toLocaleDateString()}</Tag>
                                        )}
                                    </div>
                                </Card>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default EvaluationSetSelector;