import React, { useState, useEffect } from "react";
import {
  Modal,
  Input,
  Table,
  Button,
  message,
  Spin,
  Space,
  Alert,
  Pagination,
} from "antd";
import { ExportOutlined, SearchOutlined } from "@ant-design/icons";
import axios from "axios";
import { EvaluationSet } from "../types";

interface ExportDlpAnnotationsModalProps {
  visible: boolean;
  onClose: () => void;
}

const ExportDlpAnnotationsModal: React.FC<ExportDlpAnnotationsModalProps> = ({
  visible,
  onClose,
}) => {
  const [evaluationSets, setEvaluationSets] = useState<EvaluationSet[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    pages: 1,
  });
  const [filters, setFilters] = useState({
    name: "",
    creator: "",
  });
  // 处理分页变化
  const handlePageChange = (page: number) => {
    fetchEvaluationSets(page, filters.name, filters.creator);
  };

  // 处理筛选条件变化
  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
  };

  // 应用筛选
  const applyFilters = () => {
    fetchEvaluationSets(1, filters.name, filters.creator);
  };

  // 重置筛选
  const resetFilters = () => {
    setFilters({ name: "", creator: "" });
    fetchEvaluationSets(1, "", "");
  };
  // 加载评测集列表
  const fetchEvaluationSets = async (
    page = 1,
    nameFilter = filters.name,
    creatorFilter = filters.creator
  ) => {
    setLoading(true);
    try {
      const response = await axios.get("/api/evaluation_sets", {
        params: {
          page,
          per_page: pagination.pageSize,
          name: nameFilter || undefined,
          creator: creatorFilter || undefined,
        },
      });

      if (response.data.success) {
        setEvaluationSets(response.data.data);
        setPagination({
          current: response.data.page,
          pageSize: response.data.per_page,
          total: response.data.total,
          pages: response.data.pages,
        });
      } else {
        message.error("获取评测集失败: " + response.data.error);
      }
    } catch (error) {
      message.error("请求出错");
    } finally {
      setLoading(false);
    }
  };

  // 弹窗打开时加载数据
  useEffect(() => {
    if (visible) {
      fetchEvaluationSets();
      setSelectedRowKeys([]);
    }
  }, [visible]);

  // 处理导出
  const handleExport = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一个评测集");
      return;
    }

    setExporting(true);
    try {
      const response = await axios.post(
        "/api/annotation/export-multiple-annotations",
        selectedRowKeys
      );
      // 为每个选中的评测集打开导出页面
      if (response.data.success) {
        // 如果你想在新窗口中显示结果数据
        // 创建一个新窗口并显示数据
        // const newWindow = window.open('', '_blank');
        // if (newWindow) {
        //   newWindow.document.write(`
        //     ${JSON.stringify(response.data, null, 2)}
        //   `);
        //   newWindow.document.close();
        // }
        // 下载响应数据为JSON文件
        const dataStr = JSON.stringify(response.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: "application/json" });

        // 创建下载链接
        const url = window.URL.createObjectURL(dataBlob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "dlp_annotations_export.json");
        document.body.appendChild(link);
        link.click();

        // 清理
        link.remove();
        window.URL.revokeObjectURL(url);
        message.success(
          `正在导出 ${selectedRowKeys.length} 个评测集的DLP标注数据`
        );
      }
    } catch (error) {
      message.error("导出失败");
      console.error("Export error:", error);
    } finally {
      setExporting(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "评测集名称",
      dataIndex: "set_name",
      key: "set_name",
      width: 250,
    },
    {
      title: "创建人",
      dataIndex: "creator_name",
      key: "creator_name",
      width: 120,
    },
    {
      title: "案例数量",
      dataIndex: "case_count",
      key: "case_count",
      width: 120,
      render: (count: number) => count || "计算中...",
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      width: 180,
      render: (time: string) => (time ? new Date(time).toLocaleString() : "-"),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as number[]);
    },
    getCheckboxProps: (record: EvaluationSet) => ({
      name: record.set_name,
    }),
    preserveSelectedRowKeys: true, // 关键：保留选中行的key值
  };

  return (
    <Modal
      title="导出选定评测集DLP标注结果"
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="export"
          type="primary"
          icon={<ExportOutlined />}
          loading={exporting}
          onClick={handleExport}
          disabled={selectedRowKeys.length === 0}
        >
          导出选定评测集 ({selectedRowKeys.length})
        </Button>,
      ]}
    >
      <Space direction="vertical" style={{ width: "100%" }} size="middle">
        <div className="filter-section">
          <Input
            placeholder="按评测集名称筛选"
            value={filters.name}
            onChange={(e) => handleFilterChange("name", e.target.value)}
            style={{ width: 200, marginRight: 16 }}
            allowClear
          />
          <Input
            placeholder="按创建人筛选"
            value={filters.creator}
            onChange={(e) => handleFilterChange("creator", e.target.value)}
            style={{ width: 200, marginRight: 16 }}
            allowClear
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={applyFilters}
          >
            筛选
          </Button>
          <Button onClick={resetFilters} style={{ marginLeft: 8 }}>
            重置
          </Button>
        </div>
        <Spin spinning={loading}>
          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={evaluationSets}
            rowKey="id"
            pagination={false}
            locale={{ emptyText: "暂无评测集" }}
            size="small"
          />
          {pagination.total > 0 && (
            <div className="pagination-container">
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={handlePageChange}
                showSizeChanger={false}
                showTotal={(total) => `共 ${total} 条记录`}
              />
            </div>
          )}
        </Spin>

        {selectedRowKeys.length > 0 && (
          <Alert
            message={`已选择 ${selectedRowKeys.length} 个评测集`}
            type="success"
            showIcon
          />
        )}
      </Space>
    </Modal>
  );
};

export default ExportDlpAnnotationsModal;
