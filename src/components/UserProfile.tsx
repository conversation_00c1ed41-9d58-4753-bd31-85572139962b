// src/components/UserProfile.tsx
import React from 'react';
import { Dropdown, Avatar, Button, Space } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { User } from '../types/index'; // Adjust the import path as necessary
interface UserProfileProps {
    user: User;
    onLogout: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onLogout }) => {
    const menuItems: MenuProps['items'] = [
        {
            key: 'logout',
            icon: <LogoutOutlined />,
            label: '退出登录',
            onClick: onLogout,
        },
    ];

    return (
        <Dropdown menu={{ items: menuItems }} placement="bottomRight">
            <Space className="user-profile-dropdown">
                <Avatar icon={<UserOutlined />} />
                <span>{user.username}</span>
            </Space>
        </Dropdown>
    );
};

export default UserProfile;