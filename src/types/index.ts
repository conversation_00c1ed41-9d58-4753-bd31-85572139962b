// 评测案例接口
export interface EvaluationCase {
  id: number;
  pkl_name: string;
  pkl_dir: string;
  key_obs_id: number;
  path_range: number[];
  dirty_data: boolean;
  is_checked?: boolean;      // 新增：是否已检查
  checked_at?: string;       // 新增：检查时间
  checked_by?: string;
  problems?: string;
  has_annotation?: boolean;
}

// 评测集接口
export interface EvaluationSet {
  id: number;
  set_name: string;
  creator_name: string;
  description: string;
  cases?: EvaluationCase[];
  case_count?: number;
  created_at?: string;
}

// 推理配置接口
export interface InferenceConfig {
  id: number;
  json_name: string;
  pth_name: string;
  pth_upload_time: string;
}

// 评测指标接口
export interface EvaluationMetrics {
    // Top1 指标
    ade_40: number;
    fde_40: number;
    ade_200: number;
    fde_200: number;
    ade_4s: number;
    fde_4s: number;
    static_collision: number;
    case_count?: number;
}

// API 响应接口
export interface EvaluationCaseResponse {
  data: EvaluationCase[];
  total: number;
  page: number;
  per_page: number;
  pages: number;
}

export interface PdpPathInfo {
    index: number;
    probability: number;
    points_count: number;
    visualization_points?: number[][];
    annotation?: {
        annotation: string;
        employee_id?: string;  // 添加工号字段
        created_at?: string;
        updated_at?: string;
    };
    middle_point?: number[];
    is_ground_truth?: boolean;
    color?: string;
    lineWidth?: number;
}
export interface DlpTrajectoryInfo {
  index: number;
  probability: number;
  vel: number[];
  acc: number[];
  s: number[];
  annotation?: {
    annotation: string;
    employee_id?: string;
    created_at?: string;
    updated_at?: string;
  };
}
export interface User {
    id: number;
    username: string;
    employee_id: string;
    role: string;
}

export interface PdpPathResponse {
    success: boolean;
    pdp_paths: Record<number, PdpPathInfo>;
    image_data?: string;
    ego2img?: number[][]; // 新增：4x4矩阵
    error?: string;
}

// bag集相关接口
export interface BagSet {
    id: number;
    set_name: string;
    creator_name: string;
    description?: string;
    bag_count: number;
    total_pkl_count: number;
    created_at: string;
    updated_at: string;
}

export interface Bag {
    id: number;
    bag_name: string;
    bag_path: string;
    pkl_count: number;
    created_at: string;
}

export interface BagPkl {
    id: number;
    pkl_path: string;
    pkl_name: string;
    created_at: string;
}

export interface BagSetDetail {
    bag_set: BagSet;
    bags: Bag[];
    total_bags: number;
    total_pkls: number;
}

export interface BagSetResponse {
    success: boolean;
    data: BagSet[];
    total: number;
    page: number;
    per_page: number;
    pages: number;
}