-- 修改标注表以支持bag集模式
-- 为pdp_lane_scene_annotation表添加bag_id字段

-- 检查并添加bag_id字段到pdp_lane_scene_annotation表
ALTER TABLE pdp_lane_scene_annotation 
ADD COLUMN bag_id INT NULL COMMENT 'bag ID，用于bag集模式',
ADD INDEX idx_bag_id (bag_id),
ADD INDEX idx_pkl_bag (pkl_id, bag_id);

-- 检查并添加bag_id字段到pdp_lane_scene_selected_centerlines表
ALTER TABLE pdp_lane_scene_selected_centerlines 
ADD COLUMN bag_id INT NULL COMMENT 'bag ID，用于bag集模式',
ADD INDEX idx_bag_id (bag_id),
ADD INDEX idx_pkl_bag (pkl_id, bag_id);

-- 检查并添加bag_id字段到pdp_lane_scene_tags表
ALTER TABLE pdp_lane_scene_tags 
ADD COLUMN bag_id INT NULL COMMENT 'bag ID，用于bag集模式',
ADD INDEX idx_bag_id (bag_id),
ADD INDEX idx_pkl_bag (pkl_id, bag_id);

-- 修改唯一约束，支持bag集模式
-- 删除原有的唯一约束
ALTER TABLE pdp_lane_scene_annotation DROP INDEX IF EXISTS unique_pkl_evaluation_set;
ALTER TABLE pdp_lane_scene_selected_centerlines DROP INDEX IF EXISTS unique_pkl_evaluation_set;
ALTER TABLE pdp_lane_scene_tags DROP INDEX IF EXISTS unique_pkl_evaluation_set;

-- 添加新的复合唯一约束，支持evaluation_set_id和bag_id两种模式
-- 对于pdp_lane_scene_annotation表
ALTER TABLE pdp_lane_scene_annotation 
ADD CONSTRAINT unique_pkl_annotation 
UNIQUE (pkl_id, evaluation_set_id, bag_id);

-- 对于pdp_lane_scene_selected_centerlines表
ALTER TABLE pdp_lane_scene_selected_centerlines 
ADD CONSTRAINT unique_pkl_centerlines 
UNIQUE (pkl_id, evaluation_set_id, bag_id);

-- 对于pdp_lane_scene_tags表
ALTER TABLE pdp_lane_scene_tags 
ADD CONSTRAINT unique_pkl_tags 
UNIQUE (pkl_id, evaluation_set_id, bag_id);
