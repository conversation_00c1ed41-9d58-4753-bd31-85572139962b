import sys
import os
os.environ['OPENBLAS_NUM_THREADS'] = '1'
IMPORT_DIR = os.path.abspath('/home/<USER>')
# print(IMPORT_DIR);exit()
if IMPORT_DIR not in sys.path:
    sys.path.append(IMPORT_DIR)

import tqdm
import typing
import argparse
from config import EVAL_DB_CONFIG
from pkl_name import PklNameInfo

import mysql.connector.pooling
import numpy as np
import multiprocessing as mp

from pdp.utils.text import Text
from pdp.utils.chunk import Chunk
from pdp.utils.pickle import Pickle
from pdp.utils.command import Command
from pdp.utils.compression import Compression

from pdp.utils.name_space import NameSpace
# from portable.array.base import Context as ArrayContext

class PatitionRecommend:

    @staticmethod
    def process_fn(
        lines: typing.List[str],
        txt_output_path: str,
        process_index: int,
    ):
        all_pkl_files = []

        uturn_pkl_files = []
        left_turn_pkl_files = []
        right_turn_pkl_files = []
        straight_pkl_files = []



        for line in tqdm.tqdm(lines):
            pni = PklNameInfo(line)
            if pni.centerlines_for_drivable_n == 0:
                continue
            if pni.centerlines_for_drivable_recommend_n == 0:
                continue

            #centerline proposal训练用的数据条件
            if not pni.pathformer_loss_flag:
                continue
            if not pni.proposal_from_recommend_flag:
                continue
            if not pni.proposal_source_is_recommend_lane:
                continue

            #暂时只看有空中标识牌的数据
            if pni.lane_turn_sign_mask_num == 0:
                continue
        
            pdp_mining_type_turn_left = 10
            pdp_mining_type_turn_right = 11
            pdp_mining_type_U_turn = 12
            pdp_flag_set = set(pni.pdp_mining_types)

            #按照不同的场景划分
            uturn_b = (
                pni.centerline_proposal_lateral_type_uturn or \
                (pdp_mining_type_U_turn in pdp_flag_set)
            )
            left_turn_b = (
                pni.centerline_proposal_lateral_type_left_turn or \
                (pdp_mining_type_turn_left in pdp_flag_set)
            )
            right_turn_b = (
                pni.centerline_proposal_lateral_type_right_turn or \
                (pdp_mining_type_turn_right in pdp_flag_set)
            )
            flag = True
            if uturn_b:
                uturn_pkl_files.append(line)
                flag = False
            if left_turn_b:
                left_turn_pkl_files.append(line)
                flag = False
            if right_turn_b:
                right_turn_pkl_files.append(line)
                flag = False
            if flag:
                straight_pkl_files.append(line)
            
            all_pkl_files.append(line)




        all_lines_and_txts = [
            (
                all_pkl_files,
                'all.txt',
            ),
            (
                uturn_pkl_files,
                'uturn.txt',
            ),
            (
                left_turn_pkl_files,
                'left_turn.txt',
            ),
            (
                right_turn_pkl_files,
                'right_turn.txt',
            ),
            (
                straight_pkl_files,
                'straight.txt',
            ),
        ]

        for (lines, txt) in all_lines_and_txts:
            full_txt = os.path.join(txt_output_path, txt + f'_{process_index}')
            Command.mkdir_with_777_by_file(full_txt)
            # print(f'writing {len(lines)} lines to {full_txt}')
            Text.write_lines(lines=lines, file=full_txt)

    @staticmethod
    def merge_txts_for_process(
        txt_path_i: str,
        n_process: int,
    ):
        #在这里合并每个进程的数据
        all_txt_froms = []
        uturn_txt_froms = []
        left_turn_txt_froms = []
        right_turn_txt_froms = []
        straight_txt_froms = []
        for process_index in range(n_process):
            all_txt_from = os.path.join(txt_path_i, 'all.txt' + f'_{process_index}')
            all_txt_froms.append(all_txt_from)

            uturn_txt_from = os.path.join(txt_path_i, 'uturn.txt' + f'_{process_index}')
            uturn_txt_froms.append(uturn_txt_from)

            left_turn_txt_from = os.path.join(txt_path_i, 'left_turn.txt' + f'_{process_index}')
            left_turn_txt_froms.append(left_turn_txt_from)

            right_turn_txt_from = os.path.join(txt_path_i, 'right_turn.txt' + f'_{process_index}')
            right_turn_txt_froms.append(right_turn_txt_from)

            straight_txt_from = os.path.join(txt_path_i, 'straight.txt' + f'_{process_index}')
            straight_txt_froms.append(straight_txt_from)


        dest_path = txt_path_i
        pairs = [
            (
                all_txt_froms,
                os.path.join(dest_path, 'all.txt'),
            ),
            (
                uturn_txt_froms,
                os.path.join(dest_path, 'uturn.txt')
            ),
            (
                left_turn_txt_froms,
                os.path.join(dest_path, 'left_turn.txt')
            ),
            (
                right_turn_txt_froms,
                os.path.join(dest_path, 'right_turn.txt')
            ),
            (
                straight_txt_froms,
                os.path.join(dest_path, 'straight.txt')
            ),
        ]
        for from_files, to_file in pairs:
            Command.mkdir_with_777_by_file(to_file)
            Text.merge_files(merge_from_files=from_files, merge_to_file=to_file, sort=True, remove=True, log_stream=sys.stdout)

    @staticmethod
    def merge_txts_for_sequence(
        txt_paths: typing.List[str],
        dest_path: str,
    ):
        all_txt_froms = []
        uturn_txt_froms = []
        left_turn_txt_froms = []
        right_turn_txt_froms = []
        straight_txt_froms = []
        for txt_path_i in txt_paths:
            all_txt_from = os.path.join(txt_path_i, 'all.txt')
            all_txt_froms.append(all_txt_from)

            uturn_txt_from = os.path.join(txt_path_i, 'uturn.txt')
            uturn_txt_froms.append(uturn_txt_from)

            left_turn_txt_from = os.path.join(txt_path_i, 'left_turn.txt')
            left_turn_txt_froms.append(left_turn_txt_from)

            right_turn_txt_from = os.path.join(txt_path_i, 'right_turn.txt')
            right_turn_txt_froms.append(right_turn_txt_from)

            straight_txt_from = os.path.join(txt_path_i, 'straight.txt')
            straight_txt_froms.append(straight_txt_from)




        pairs = [
            (
                all_txt_froms,
                os.path.join(dest_path, 'all.txt'),
            ),
            (
                uturn_txt_froms,
                os.path.join(dest_path, 'uturn.txt')
            ),
            (
                left_turn_txt_froms,
                os.path.join(dest_path, 'left_turn.txt')
            ),
            (
                right_turn_txt_froms,
                os.path.join(dest_path, 'right_turn.txt')
            ),
            (
                straight_txt_froms,
                os.path.join(dest_path, 'straight.txt')
            ),
        ]
        for from_files, to_file in pairs:
            Command.mkdir_with_777_by_file(to_file)
            Text.merge_files(merge_from_files=from_files, merge_to_file=to_file, sort=False, remove=False, log_stream=sys.stdout)

    @staticmethod
    def process(
        config: NameSpace,      
    ):
        

        txt_path = os.path.join(config.root_path, config.short_txt_path)

        txt_path_list = []
        for txt_flag, input_txt in config.input_txts:
            print(f'reading from {input_txt}')
            lines = Text.read_lines(input_txt)
            n_lines = len(lines)
            txt_path_i = os.path.join(txt_path, txt_flag)
            txt_path_list.append(txt_path_i)

            ps = []
            n_process = min(config.n_process, n_lines)
            for process_index, (begin_index, end_index) in enumerate(Chunk.chunk1(length=n_lines, chunks=n_process)):
                p = mp.Process(
                    target=PatitionRecommend.process_fn,
                    args=(lines[begin_index: end_index], txt_path_i, process_index)
                )
                p.start()
                ps.append(p)

            for p in ps:
                p.join()
            PatitionRecommend.merge_txts_for_process(txt_path_i=txt_path_i, n_process=n_process)
        
        PatitionRecommend.merge_txts_for_sequence(txt_paths=txt_path_list, dest_path=txt_path)

        exit()
        #开始batching
        config_batching = config.batching
        batch_size = config_batching.batch_size
        n_process = config_batching.n_process

        # for input_txt in need_batching_txt:
        #     _, f = os.path.split(input_txt)
        #     prefix, suffix = os.path.splitext(f)

        #     output_txt = os.path.join(config.root_path, config_batching.short_path, f'{prefix}__{batch_size}{suffix}')
        #     output_txt_path, short_txt = os.path.split(output_txt)
        #     short_txt_prefix, _ = os.path.splitext(short_txt)
        #     output_path = os.path.join(output_txt_path, short_txt_prefix)
        #     Command.mkdir_with_777(output_path)
        #     Command.mkdir_with_777_by_file(output_txt)

        #     command = f'python3 ./script/pkl/utils/batching_data.py --input_txt {input_txt} --output_txt {output_txt} --output_path {output_path} --batch_size {batch_size} --n_process {n_process}'
        #     #--compressed
        #     if config_batching.compressed:
        #         command = command + ' --compressed'

            # Command.execute(command)



def get_db_connection(database='my_eval_db'):
    """获取数据库连接"""
    config = EVAL_DB_CONFIG.copy()
    if 'database' not in config:
        config['database'] = database  # 使用默认数据库名
    # return mysql.connector.connect(**config)
    return connection_pool.get_connection()

def update_bag_info(bag_set_id=None):
    db_connection = get_db_connection()
    # get bag list
    cursor = db_connection.cursor(dictionary=True)

    try:
        # 查询所有的 bag 列表
        if bag_set_id:
            cursor.execute("SELECT id, bag_name FROM bag WHERE bag_set_id = %s", (bag_set_id,))
        else:
            cursor.execute("SELECT id, bag_name FROM bag")
        bags = cursor.fetchall()

        print(f"Total bags: {len(bags)}")
        # for bag in bags:
        #tqdm
        for bag in tqdm.tqdm(bags):
            bag_id = bag['id']
            bag_name = bag['bag_name']

            # 查询当前 bag 下的所有 pkl 文件
            cursor.execute(
                "SELECT id, pkl_path, pkl_name FROM bag_pkl_relation WHERE bag_id = %s",
                (bag_id,)
            )
            pkls = cursor.fetchall()
            pkl_path_list=[os.path.join(pkl['pkl_path'],pkl['pkl_name']) for pkl in pkls]
            # print(f"Bag ID: {bag_id}, Bag Name: {bag_name}, PKLs:{len(pkls)}")
            uturn=0
            left_turn=0
            right_turn=0
            straight=0
            for path in pkl_path_list:
                pni=PklNameInfo(path)
                if pni.centerlines_for_drivable_n == 0:
                    continue
                if pni.centerlines_for_drivable_recommend_n == 0:
                    continue

                #centerline proposal训练用的数据条件
                if not pni.pathformer_loss_flag:
                    continue
                if not pni.proposal_from_recommend_flag:
                    continue
                if not pni.proposal_source_is_recommend_lane:
                    continue

                #暂时只看有空中标识牌的数据
                if pni.lane_turn_sign_mask_num == 0:
                    continue
            
                pdp_mining_type_turn_left = 10
                pdp_mining_type_turn_right = 11
                pdp_mining_type_U_turn = 12
                pdp_flag_set = set(pni.pdp_mining_types)

                #按照不同的场景划分
                uturn_b = (
                    pni.centerline_proposal_lateral_type_uturn or \
                    (pdp_mining_type_U_turn in pdp_flag_set)
                )
                left_turn_b = (
                    pni.centerline_proposal_lateral_type_left_turn or \
                    (pdp_mining_type_turn_left in pdp_flag_set)
                )
                right_turn_b = (
                    pni.centerline_proposal_lateral_type_right_turn or \
                    (pdp_mining_type_turn_right in pdp_flag_set)
                )
                flag = True
                if uturn_b:
                    uturn+=1
                    flag = False
                if left_turn_b:
                    left_turn+=1
                    flag = False
                if right_turn_b:
                    right_turn+=1
                    flag = False
                if flag:
                    straight+=1
            cursor.execute(
                "UPDATE bag SET uturn = %s, left_turn = %s, right_turn = %s, straight = %s WHERE id = %s",
                (uturn, left_turn, right_turn, straight, bag_id)
            )   
            # break
            # 在这里可以对每个 bag 的 pkl 文件进行统计分析
            # 例如统计 uturn、left_turn 等信息

    except Exception as e:
        print(f"Error: {e}")
    finally:
        cursor.close()
        db_connection.close()
pool_config = EVAL_DB_CONFIG.copy()
if 'database' not in pool_config:
    pool_config['database'] = 'my_eval_db'

# 连接池配置
pool_config.update({
    'pool_name': 'eval_pool',
    'pool_size': 10,  # 根据实际并发量调整
    'pool_reset_session': True,
    'autocommit': True
})
connection_pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config) 
if __name__ == '__main__':
    # pkl_file = '/extraStore/groupdatahw01/Predictionalgorithm/42060/generated_data/all/20250821_proposal/from_bags/urp_ee35/pkl/LFZ63AZ59SD000197_record_data_2025_05_10_21_13_28/C10^LFZ63AZ59SD000197_record_data_2025_05_10_21_13_28^1746882907536360852^[0]^[9,11,6]^1^1^1^0^23^1^0^0^-1^0^1^0^8^1^4^182^185^1^1^0^1^0^0^0^0^2.pkl'
    # pni = PklNameInfo(pkl_file)
    # print(pni.pathformer_loss_flag)
    # print(pni.proposal_from_recommend_flag)
    # # print(pni.__proposal_from_recommend_flag_str)
    # splits = 'C10^LFZ63AZ59SD000197_record_data_2025_05_10_21_13_28^1746882907536360852^[0]^[9,11,6]^1^1^1^0^23^1^0^0^-1^0^1^0^8^1^4^182^185^1^1^0^1^0^0^0^0^2'.split('^')
    # # print(splits)
    # for i, v in enumerate(splits):
    #     print(i, v)

    # print(len(splits))
    # uturn_b = pni.centerline_proposal_lateral_type_uturn
    # left_turn_b = pni.centerline_proposal_lateral_type_left_turn
    # right_turn_b = pni.centerline_proposal_lateral_type_right_turn
    # lane_change_b = pni.centerline_proposal_lateral_type_lane_change
    # lane_change_num = pni.centerline_proposal_lateral_type_change_lane_num
    # print(f'uturn_b:{uturn_b}, left_turn_b:{left_turn_b},right_turn_b:{right_turn_b},lane_change_b:{lane_change_b},lane_change_num:{lane_change_num}')

    # parser = argparse.ArgumentParser()
    # parser.add_argument(
    #     '--json', type=str,
    # )
    # args = parser.parse_args()


    # config = NameSpace.from_json_file(args.json)
    # PatitionRecommend.process(config)

    update_bag_info(bag_set_id=13)
