

import sys
import os
IMPORT_DIR = os.path.abspath('/home/<USER>')
if IMPORT_DIR not in sys.path:
    sys.path.append(IMPORT_DIR)
THIRD_DIR = os.path.join(IMPORT_DIR, 'pdp/third')
if THIRD_DIR not in sys.path:
    sys.path.append(THIRD_DIR)

import typing
import numpy as np
from pdp.special.pkl_flag import Pkl<PERSON>lag
from pdp.script.bag.bag_identity import BagIdentity

class PklNameInfo:
    def __init__(
        self,
        pkl_name: str,
    ) -> None:
        path, short_name = os.path.split(pkl_name)
        short_name, _ = os.path.splitext(short_name)#把后缀去掉

        (
            ego_vehicle_type, 
            bag_identity,
            fusion_timestamp_str,
            # map_timestamp_str,
            prediction_mining_type_str,
            pdp_mining_types_str,
            manual_driving_str,
            pathformer_loss_flag_str,
            decision_loss_flag_str,
            cutin_flag_str,
            god_object_list_str,
            road_island_points_list_str,
            checked_pathformer_flag_str,
            *other_infos,
        ) = short_name.split(PklName.sep_char)

        self.__ego_vehicle_type = ego_vehicle_type
        self.__bag_identity = bag_identity
        self.__fusion_timestamp_str = fusion_timestamp_str
        # self.__map_timestamp_str = map_timestamp_str
        self.__prediction_mining_type_str = prediction_mining_type_str
        self.__pdp_mining_types_str = pdp_mining_types_str
        self.__manual_driving_str = manual_driving_str
        self.__pathformer_loss_flag_str = pathformer_loss_flag_str
        self.__decision_loss_flag_str = decision_loss_flag_str
        self.__cutin_flag_str = cutin_flag_str
        self.__god_object_list_str = god_object_list_str
        self.__road_island_points_list_str = road_island_points_list_str
        self.__checked_pathformer_flag_str = checked_pathformer_flag_str


        def set_default_slot_value():
            self.__slot_ids_n_str = '0'
            self.__slot_gt_str = '0'
            self.__find_ego_on_line_str = '0'

        def set_default_proposal_from_value():
            self.__proposal_from_recommend_flag_str = '0'
            self.__proposal_from_recommend_start_index_str = '0'
            self.__proposal_from_recommend_end_index_str = '0'

            self.__proposal_from_centerlines_flag_str = '0'
            self.__proposal_from_centerlines_n_str = '0'
            self.__best_proposal_start_index_str = '0'
            self.__best_proposal_end_index_str = '0'

        def set_default_centerline_lateral_types():
            self.__proposal_source_str = '1'
            self.__navigation_flag_str = '1'
            self.__tbt_action_flag_str = '0'
            self.__centerline_proposal_lateral_type_lane_change_str = '0'
            self.__centerline_proposal_lateral_type_right_turn_str = '0'
            self.__centerline_proposal_lateral_type_left_turn_str = '0'
            self.__centerline_proposal_lateral_type_uturn_str = '0'
            self.__centerline_proposal_lateral_type_change_lane_num_str = '0'
            self.__centerline_proposal_lateral_type_change_direction_str = '0'

        def set_default_maneuver():
            self.__maneuver_id_str = '0'
            self.__maneuver_time_str = '0'
            self.__maneuver_dist_str = '0'
            self.__lane_turn_sign_mask_num_str = '0'

        def set_default_drivable():
            self.__centerlines_for_drivable_n_str = '0'
            self.__centerlines_for_drivable_recommend_n_str = '0'
            self.__centerlines_for_drivable_max_count_point_str = '0'
            self.__centerlines_for_drivable_ego_path_is_not_recommend_str = '0'
        
        if len(other_infos) == 27:
            self.__slot_ids_n_str, self.__slot_gt_str, self.__find_ego_on_line_str, \
            self.__proposal_from_recommend_flag_str, self.__proposal_from_recommend_start_index_str, self.__proposal_from_recommend_end_index_str,\
            self.__proposal_from_centerlines_flag_str, self.__proposal_from_centerlines_n_str, self.__best_proposal_start_index_str, self.__best_proposal_end_index_str,\
            self.__proposal_source_str, self.__navigation_flag_str, self.__tbt_action_flag_str,\
            self.__centerline_proposal_lateral_type_lane_change_str,\
            self.__centerline_proposal_lateral_type_right_turn_str,\
            self.__centerline_proposal_lateral_type_left_turn_str,\
            self.__centerline_proposal_lateral_type_uturn_str,\
            self.__centerline_proposal_lateral_type_change_lane_num_str,\
            self.__centerline_proposal_lateral_type_change_direction_str,\
            self.__maneuver_id_str, self.__maneuver_time_str, self.__maneuver_dist_str,\
            self.__lane_turn_sign_mask_num_str,\
            self.__centerlines_for_drivable_n_str,\
            self.__centerlines_for_drivable_recommend_n_str,\
            self.__centerlines_for_drivable_max_count_point_str,\
            self.__centerlines_for_drivable_ego_path_is_not_recommend_str,\
             = other_infos

        elif len(other_infos) == 23:
            self.__slot_ids_n_str, self.__slot_gt_str, self.__find_ego_on_line_str, \
            self.__proposal_from_recommend_flag_str, self.__proposal_from_recommend_start_index_str, self.__proposal_from_recommend_end_index_str,\
            self.__proposal_from_centerlines_flag_str, self.__proposal_from_centerlines_n_str, self.__best_proposal_start_index_str, self.__best_proposal_end_index_str,\
            self.__proposal_source_str, self.__navigation_flag_str, self.__tbt_action_flag_str,\
            self.__centerline_proposal_lateral_type_lane_change_str,\
            self.__centerline_proposal_lateral_type_right_turn_str,\
            self.__centerline_proposal_lateral_type_left_turn_str,\
            self.__centerline_proposal_lateral_type_uturn_str,\
            self.__centerline_proposal_lateral_type_change_lane_num_str,\
            self.__centerline_proposal_lateral_type_change_direction_str,\
            self.__maneuver_id_str, self.__maneuver_time_str, self.__maneuver_dist_str,\
            self.__lane_turn_sign_mask_num_str,\
             = other_infos
            set_default_drivable()

        elif len(other_infos) == 19:
            self.__slot_ids_n_str, self.__slot_gt_str, self.__find_ego_on_line_str, \
            self.__proposal_from_recommend_flag_str, self.__proposal_from_recommend_start_index_str, self.__proposal_from_recommend_end_index_str,\
            self.__proposal_from_centerlines_flag_str, self.__proposal_from_centerlines_n_str, self.__best_proposal_start_index_str, self.__best_proposal_end_index_str,\
            self.__proposal_source_str, self.__navigation_flag_str, self.__tbt_action_flag_str,\
            self.__centerline_proposal_lateral_type_lane_change_str,\
            self.__centerline_proposal_lateral_type_right_turn_str,\
            self.__centerline_proposal_lateral_type_left_turn_str,\
            self.__centerline_proposal_lateral_type_uturn_str,\
            self.__centerline_proposal_lateral_type_change_lane_num_str,\
            self.__centerline_proposal_lateral_type_change_direction_str,\
             = other_infos
            # print(other_infos)
            # print(self.__proposal_from_recommend_flag_str)
            # exit()
            set_default_maneuver()
            set_default_drivable()
        elif len(other_infos) == 10:
            self.__slot_ids_n_str, self.__slot_gt_str, self.__find_ego_on_line_str, \
            self.__proposal_from_recommend_flag_str, self.__proposal_from_recommend_start_index_str, self.__proposal_from_recommend_end_index_str,\
            self.__proposal_from_centerlines_flag_str, self.__proposal_from_centerlines_n_str, self.__best_proposal_start_index_str, self.__best_proposal_end_index_str,\
             = other_infos
            set_default_centerline_lateral_types()
            set_default_maneuver()
            set_default_drivable()
            
        elif len(other_infos) == 3:
            #find_ego_on_line_str
            self.__slot_ids_n_str, self.__slot_gt_str, self.__find_ego_on_line_str = other_infos
            set_default_proposal_from_value()
            set_default_centerline_lateral_types()
            set_default_maneuver()
            set_default_drivable()
        else:
            set_default_slot_value()
            set_default_proposal_from_value()
            set_default_centerline_lateral_types()
            set_default_maneuver()
            set_default_drivable()

        # print(self.__proposal_from_recommend_flag_str);exit()
    @property
    def vehicle_type(self) -> str:
        return self.__ego_vehicle_type
    
    @property
    def bag_identity(self) -> str:
        return self.__bag_identity
    
    @property
    def fusion_timestamp(self) -> int:
        return int(self.__fusion_timestamp_str)
    
    # @property
    # def map_timestamp(self) -> int:
    #     return int(self.__map_timestamp_str)
    
    @property
    def prediction_mining_type(self) -> typing.List[int]:
        #self.__prediction_mining_type_str
        return eval(self.__prediction_mining_type_str)
    
    @property
    def pdp_mining_types(self) -> typing.List[int]:
        #比较危险操作
        #如果都是调用这个py代码则是安全的
        return eval(self.__pdp_mining_types_str)
    
    @property
    def manual_driving(self) -> bool:
        return self.__manual_driving_str == '1'
    
    @property
    def pathformer_loss_flag(self) -> bool:
        return self.__pathformer_loss_flag_str == '1'
    
    @property
    def decision_loss_flag(self) -> bool:
        return self.__decision_loss_flag_str == '1'
    
    @property
    def cutin_flag(self) -> bool:
        return self.__cutin_flag_str == '1'
    
    @property
    def god_object_list_len(self) -> int:
        return int(self.__god_object_list_str)

    @property
    def road_island_points_list_len(self) -> int:
        return int(self.__road_island_points_list_str)

    @property
    def checked_pathformer_flag(self) -> bool:
        return self.__checked_pathformer_flag_str == '1'
    
    @property
    def slot_ids_n(self) -> int:
        return int(self.__slot_ids_n_str)
    
    @property
    def slot_gt(self) -> int:
        return int(self.__slot_gt_str)
    
    @property
    def find_ego_on_line(self) -> bool:
        return self.__find_ego_on_line_str == '1'
    
    @property
    def proposal_from_recommend_flag(self) -> bool:
        # print('self.__proposal_from_recommend_flag_str', self.__proposal_from_recommend_flag_str)
        return self.__proposal_from_recommend_flag_str == '1'
    
    @property
    def proposal_from_recommend_start_index(self) -> int:
        return int(self.__proposal_from_recommend_start_index_str)

    @property
    def proposal_from_recommend_end_index(self) -> int:
        return int(self.__proposal_from_recommend_end_index_str)

    @property
    def proposal_from_centerlines_flag(self) -> bool:
        return self.__proposal_from_centerlines_flag_str == '1'
    
    @property
    def proposal_from_centerlines_n(self) -> int:
        return int(self.__proposal_from_centerlines_n_str)
    
    @property
    def best_proposal_start_index(self) -> int:
        return int(self.__best_proposal_start_index_str)
    
    @property
    def best_proposal_end_index(self) -> int:
        return int(self.__best_proposal_end_index_str)
    

    @property
    def proposal_source_is_recommend_lane(self) -> bool:
        return self.__proposal_source_str == '0'
    @property
    def proposal_source_is_centerline(self) -> bool:
        return self.__proposal_source_str == '1'

    @property
    def navigation_flag(self) -> bool:
        return self.__navigation_flag_str == '1'
    @property
    def tbt_action_flag(self) -> bool:
        return self.__tbt_action_flag_str == '1'
    
    @property
    def centerline_proposal_lateral_type_lane_change(self) -> bool:
        return self.__centerline_proposal_lateral_type_lane_change_str == '1'
    @property
    def centerline_proposal_lateral_type_right_turn(self) -> bool:
        return self.__centerline_proposal_lateral_type_right_turn_str == '1'
    @property
    def centerline_proposal_lateral_type_left_turn(self) -> bool:
        return self.__centerline_proposal_lateral_type_left_turn_str == '1'
    @property
    def centerline_proposal_lateral_type_uturn(self) -> bool:
        return self.__centerline_proposal_lateral_type_uturn_str == '1'
    @property
    def centerline_proposal_lateral_type_change_lane_num(self) -> int:
        return int(self.__centerline_proposal_lateral_type_change_lane_num_str)
    @property
    def centerline_proposal_lateral_type_change_direction(self) -> str:
        flag_str = self.__centerline_proposal_lateral_type_change_direction_str
        if flag_str == '0':
            return 'none'
        elif flag_str == '1':
            return 'left'
        elif flag_str == '2':
            return 'right'
        else:
            return 'unknown'
        
    @property
    def maneuver_id(self) -> int:
        return int(self.__maneuver_id_str)

    @property
    def maneuver_time(self) -> int:
        return int(self.__maneuver_time_str)
    
    @property
    def maneuver_dist(self) -> int:
        return int(self.__maneuver_dist_str)

    @property
    def lane_turn_sign_mask_num(self) -> int:
        return int(self.__lane_turn_sign_mask_num_str)
    
    @property
    def centerlines_for_drivable_n(self) -> int:
        return int(self.__centerlines_for_drivable_n_str)
    
    @property
    def centerlines_for_drivable_recommend_n(self) -> int:
        return int(self.__centerlines_for_drivable_recommend_n_str)
    
    @property
    def centerlines_for_drivable_max_count_point(self) -> int:
        return int(self.__centerlines_for_drivable_max_count_point_str)
    
    @property
    def centerlines_for_drivable_ego_path_is_not_recommend(self) -> bool:
        return self.__centerlines_for_drivable_ego_path_is_not_recommend_str == '1'


class PklName:
    sep_char = '^'


    
    @staticmethod
    def cutin_flag(data_dict: typing.Dict[str, typing.Any]) -> bool:
        cutin = data_dict['cutin']
        return 'cutin' in cutin


    @staticmethod
    def get_short_name_of_pkl(
        data_dict: typing.Dict[str, typing.Any],
        suffix: str = '.pkl'
    ) -> str:
        bag_name = data_dict['bag_name']
        ego_vehicle_type = data_dict['ego_vehicle_type']
        manual_driving = data_dict['manual_driving']
        mining_type = data_dict['mining_type']
        pdp_mining_type = data_dict['pdp_mining_type']
        timestamp = data_dict['timestamp']#fusion_time
        # map_timestamp = data_dict['map_timestamp']#map_time
        god_object_list = data_dict.get('god_object_list', None)
        road_island_points_list = data_dict.get('road_island_points_list', None)
        checked_pathformer_flag = data_dict.get('checked_pathformer_flag', False)

        find_ego_on_line = data_dict.get('find_ego_on_line', False)

        slot_ids = data_dict.get('slot_ids', None)
        if slot_ids is not None:
            slot_ids_n = len(slot_ids)
        else:
            slot_ids_n = 0
        
        slot_gt = data_dict.get('slot_gt', 0)
    
        manual_driving_str = '1' if manual_driving else '0'

        fusion_timestamp_str = str(timestamp)
        # map_timestamp_str = str(map_timestamp)
        bag_identity = BagIdentity.get_identity(bag_name=bag_name)


        # prediction_mining_type_str = str(mining_type)
        s = []
        for t in mining_type:
            s.append(str(t))
        prediction_mining_type_str = '[' + ','.join(s) + ']'

        s = []
        for t in pdp_mining_type:
            s.append(str(t))
        pdp_mining_types_str = '[' + ','.join(s) + ']'


        pathformer_loss_flag = PklFlag.compute_pathformer_loss_flag(data_dict=data_dict)
        pathformer_loss_flag_str = '1' if pathformer_loss_flag else '0'

        decision_loss_flag = PklFlag.compute_decision_loss_flag(data_dict=data_dict)
        decision_loss_flag_str = '1' if decision_loss_flag else '0'

        cutin_flag = PklName.cutin_flag(data_dict=data_dict)
        cutin_flag_str = '1' if cutin_flag else '0'

        if god_object_list is not None:
            god_object_list_str = str(len(god_object_list))
        else:
            god_object_list_str = '0'


        if road_island_points_list is not None:
            road_island_points_list_str = str(len(road_island_points_list))
        else:
            road_island_points_list_str = '0'

        checked_pathformer_flag_str = '1' if checked_pathformer_flag else '0'

        slot_ids_n_str = str(slot_ids_n)
        slot_gt_str = str(slot_gt)

        if find_ego_on_line:
            find_ego_on_line_str = '1'
        else:
            find_ego_on_line_str = '0'


        #添加propsal_from_recommend
        centerline_proposal_from_recommend_lane = data_dict.get('centerline_proposal_from_recommend_lane', None)
        if centerline_proposal_from_recommend_lane is not None:
            proposal_from_recommend_flag = True
            proposal_from_recommend_start_index = centerline_proposal_from_recommend_lane['start_index']
            proposal_from_recommend_end_index = centerline_proposal_from_recommend_lane['end_index']
            proposal_source = centerline_proposal_from_recommend_lane['source']
        else:
            proposal_from_recommend_flag = False
            proposal_from_recommend_start_index = 0
            proposal_from_recommend_end_index = 0
            proposal_source = 'recommend_lane'
        
        proposal_from_recommend_flag_str = '1' if proposal_from_recommend_flag else '0'
        proposal_from_recommend_start_index_str = str(proposal_from_recommend_start_index)
        proposal_from_recommend_end_index_str = str(proposal_from_recommend_end_index)
        proposal_source_str = '0' if proposal_source == 'recommend_lane' else '1'


        centerline_proposal_from_centerlines = data_dict.get('centerline_proposal_from_centerlines', None)
        if centerline_proposal_from_centerlines is not None:
            proposal_from_centerlines_flag = True

            proposal_items = centerline_proposal_from_centerlines['proposal_items']
            proposal_from_centerlines_n = len(proposal_items)
            best_proposal_index = centerline_proposal_from_centerlines['best_proposal_index']

            best_proposal_item = proposal_items[best_proposal_index]
            best_proposal_start_index = best_proposal_item['start_index']
            best_proposal_end_index = best_proposal_item['end_index']
        else:
            proposal_from_centerlines_flag = False
            proposal_from_centerlines_n = 0
            best_proposal_start_index = 0
            best_proposal_end_index = 0
        
        proposal_from_centerlines_flag_str = '1' if proposal_from_centerlines_flag else '0'
        proposal_from_centerlines_n_str = str(proposal_from_centerlines_n)
        best_proposal_start_index_str = str(best_proposal_start_index)
        best_proposal_end_index_str = str(best_proposal_end_index)




        navigation_flag = PklFlag.compute_navigation_point_flag(data_dict)
        tbt_action_flag = PklFlag.compute_tbt_action_flag(data_dict)
        # ego_status_flag = PklFlag.compute_ego_status_flag(data_dict)
        # path_point_flag = PklFlag.compute_path_point_flag(data_dict)
        navigation_flag_str = '1' if navigation_flag else '0'
        tbt_action_flag_str = '1' if tbt_action_flag else '0'


        centerline_proposal_lateral_types = data_dict.get('centerline_proposal_lateral_types', None)
        if centerline_proposal_lateral_types is not None:
            centerline_proposal_lateral_type_lane_change = centerline_proposal_lateral_types['lane_change']
            centerline_proposal_lateral_type_right_turn = centerline_proposal_lateral_types['right_turn']
            centerline_proposal_lateral_type_left_turn = centerline_proposal_lateral_types['left_turn']
            centerline_proposal_lateral_type_uturn = centerline_proposal_lateral_types['uturn']
            centerline_proposal_lateral_type_change_lane_num = centerline_proposal_lateral_types['change_lane_num']
            centerline_proposal_lateral_type_change_direction = centerline_proposal_lateral_types['change_direction']
        else:            
            centerline_proposal_lateral_type_lane_change = False
            centerline_proposal_lateral_type_right_turn = False
            centerline_proposal_lateral_type_left_turn = False
            centerline_proposal_lateral_type_uturn = False
            centerline_proposal_lateral_type_change_lane_num = 0
            centerline_proposal_lateral_type_change_direction = 'none'
        
        centerline_proposal_lateral_type_lane_change_str = '1' if centerline_proposal_lateral_type_lane_change else '0'
        centerline_proposal_lateral_type_right_turn_str = '1' if centerline_proposal_lateral_type_right_turn else '0'
        centerline_proposal_lateral_type_left_turn_str = '1' if centerline_proposal_lateral_type_left_turn else '0'
        centerline_proposal_lateral_type_uturn_str = '1' if centerline_proposal_lateral_type_uturn else '0'
        centerline_proposal_lateral_type_change_lane_num_str = str(centerline_proposal_lateral_type_change_lane_num)
        if centerline_proposal_lateral_type_change_direction == 'none':
            centerline_proposal_lateral_type_change_direction_str = '0'
        elif centerline_proposal_lateral_type_change_direction == 'left':
            centerline_proposal_lateral_type_change_direction_str = '1'
        elif centerline_proposal_lateral_type_change_direction == 'right':
            centerline_proposal_lateral_type_change_direction_str = '2'
        else:
            centerline_proposal_lateral_type_change_direction_str = '3'


        tbt = data_dict.get('tbt', None)
        if tbt is not None:
            maneuver_id = tbt.get('maneuver_id', 0)
            maneuver_time = int(tbt.get('time', 0))
            maneuver_dist = int(tbt.get('dist', 0))

        else:
            maneuver_id = 0
            maneuver_time = 0
            maneuver_dist = 0

        maneuver_id_str = str(maneuver_id)
        maneuver_time_str = str(maneuver_time)
        maneuver_dist_str = str(maneuver_dist)      

        lane_turn_sign_mask = data_dict.get('lane_turn_sign_mask', None)
        if lane_turn_sign_mask is None:
            lane_turn_sign_mask_num = 0
        else:
            # lane_turn_sign_mask.shape = (time_step, max_num)
            num = np.sum(lane_turn_sign_mask)
            lane_turn_sign_mask_num = int(num)

        lane_turn_sign_mask_num_str = str(lane_turn_sign_mask_num)



        centerlines_for_drivable = data_dict.get('centerlines_for_drivable', None)
        if centerlines_for_drivable is not None:
            points_list = centerlines_for_drivable['points_list']
            lane_recommend_count = centerlines_for_drivable['lane_recommend_count']
            lane_max_count_of_point = centerlines_for_drivable['lane_max_count_of_point']
            ego_path_is_not_recommend = centerlines_for_drivable['ego_path_is_not_recommend']

            centerlines_for_drivable_n = len(points_list)
            centerlines_for_drivable_recommend_n = lane_recommend_count
            centerlines_for_drivable_max_count_point = lane_max_count_of_point
            centerlines_for_drivable_ego_path_is_not_recommend = 1 if ego_path_is_not_recommend else 0
        else:
            centerlines_for_drivable_n = 0
            centerlines_for_drivable_recommend_n = 0
            centerlines_for_drivable_max_count_point = 0
            centerlines_for_drivable_ego_path_is_not_recommend = 0

        centerlines_for_drivable_n_str = str(centerlines_for_drivable_n)
        centerlines_for_drivable_recommend_n_str = str(centerlines_for_drivable_recommend_n)
        centerlines_for_drivable_max_count_point_str = str(centerlines_for_drivable_max_count_point)
        centerlines_for_drivable_ego_path_is_not_recommend_str = str(centerlines_for_drivable_ego_path_is_not_recommend)

            
        
        
        flags = [
            ego_vehicle_type, #0
            bag_identity,#1
            fusion_timestamp_str,#2
            # map_timestamp_str,
            prediction_mining_type_str,#3
            pdp_mining_types_str,#4
            manual_driving_str,#5
            pathformer_loss_flag_str,#6
            decision_loss_flag_str,#7
            cutin_flag_str,#8
            god_object_list_str,#9
            road_island_points_list_str,#10
            checked_pathformer_flag_str,#11
            
            slot_ids_n_str,#12
            slot_gt_str,#13
            find_ego_on_line_str,#14

            proposal_from_recommend_flag_str,#15
            proposal_from_recommend_start_index_str,#16
            proposal_from_recommend_end_index_str,#17
            proposal_from_centerlines_flag_str,#18
            proposal_from_centerlines_n_str,#19
            best_proposal_start_index_str,#20
            best_proposal_end_index_str,#21

            proposal_source_str,#22
            navigation_flag_str,#23
            tbt_action_flag_str,#24
            centerline_proposal_lateral_type_lane_change_str,#25
            centerline_proposal_lateral_type_right_turn_str,#25
            centerline_proposal_lateral_type_left_turn_str,#27
            centerline_proposal_lateral_type_uturn_str,#28
            centerline_proposal_lateral_type_change_lane_num_str,#29
            centerline_proposal_lateral_type_change_direction_str,#30

            maneuver_id_str,#31
            maneuver_time_str,#32,
            maneuver_dist_str,#33
            lane_turn_sign_mask_num_str,#34

            centerlines_for_drivable_n_str,#35,
            centerlines_for_drivable_recommend_n_str,#36,
            centerlines_for_drivable_max_count_point_str,#37,
            centerlines_for_drivable_ego_path_is_not_recommend_str,#38
        ]

        return PklName.sep_char.join(flags) + suffix


    @staticmethod
    def get_info_from_pkl_name(
        pkl_name: str,
    ):
        return PklNameInfo(pkl_name=pkl_name)
