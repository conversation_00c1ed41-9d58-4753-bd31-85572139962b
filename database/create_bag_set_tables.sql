-- 创建bag集管理相关的数据库表

-- 1. bag集表
CREATE TABLE IF NOT EXISTS bag_set (
    id INT AUTO_INCREMENT PRIMARY KEY,
    set_name VARCHAR(255) NOT NULL COMMENT 'bag集名称',
    creator_name VARCHAR(255) NOT NULL COMMENT 'bag集创建人',
    description TEXT COMMENT 'bag集描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_set_name (set_name),
    INDEX idx_creator_name (creator_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='bag集表';

-- 2. bag表
CREATE TABLE `bag` (
    `id` int NOT NULL AUTO_INCREMENT,
    `bag_name` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'bag名称',
    `bag_path` varchar(1024) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT 'bag文件路径',
    `bag_set_id` int NOT NULL COMMENT '关联的bag集ID',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `pkl_count` int DEFAULT '0' COMMENT 'bag下的pkl文件数量',
    `bag_timestamp` varchar(19) COLLATE utf8mb3_unicode_ci GENERATED ALWAYS AS (substr(`bag_name`, - (19))) STORED,
    `centerline_count` int DEFAULT '0' COMMENT '选中centerlines的数量',
    `scene_tag_count` int DEFAULT '0' COMMENT '场景标签的数量',
    `uturn` int DEFAULT NULL,
    `left_turn` int DEFAULT NULL,
    `right_turn` int DEFAULT NULL,
    `straight` int DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_bag_path` (`bag_name`),
    KEY `idx_bag_set_id` (`bag_set_id`),
    KEY `idx_bag_name` (`bag_name`),
    KEY `idx_bag_timestamp` (`bag_timestamp`)
) ENGINE = InnoDB AUTO_INCREMENT = 45780 DEFAULT CHARSET = utf8mb3 COLLATE = utf8mb3_unicode_ci COMMENT = 'bag表'

-- 3. bag与pkl的关联表
CREATE TABLE IF NOT EXISTS bag_pkl_relation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bag_id INT NOT NULL COMMENT '关联的bag ID',
    pkl_path VARCHAR(1024) NOT NULL COMMENT 'pkl文件路径',
    pkl_name VARCHAR(512) NOT NULL COMMENT 'pkl文件名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (bag_id) REFERENCES bag(id) ON DELETE CASCADE,
    UNIQUE KEY unique_bag_pkl (pkl_name),
    INDEX idx_bag_id (bag_id),
    INDEX idx_pkl_path (pkl_path),
    INDEX idx_pkl_name (pkl_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='bag与pkl关联表';

-- 4. 创建视图，方便查询bag集、bag和pkl的关系
-- 创建视图，方便查询bag集、bag和pkl的关系
CREATE OR REPLACE VIEW bag_set_detail_view AS
SELECT 
    bs.id as bag_set_id,
    bs.set_name as bag_set_name,
    bs.creator_name,
    bs.description as bag_set_description,
    bs.created_at as bag_set_created_at,
    b.id as bag_id,
    b.bag_name,
    b.bag_path,
    bpr.id as pkl_relation_id,
    bpr.pkl_path,
    bpr.pkl_name,
    (SELECT COUNT(DISTINCT b2.id) 
     FROM bag b2 
     WHERE b2.bag_set_id = bs.id) as bag_count,
    (SELECT COUNT(bpr2.id) 
     FROM bag b2 
     LEFT JOIN bag_pkl_relation bpr2 ON b2.id = bpr2.bag_id 
     WHERE b2.bag_set_id = bs.id) as total_pkl_count,
    (SELECT COUNT(bpr3.id) 
     FROM bag_pkl_relation bpr3 
     WHERE bpr3.bag_id = b.id) as bag_pkl_count
FROM bag_set bs
LEFT JOIN bag b ON bs.id = b.bag_set_id
LEFT JOIN bag_pkl_relation bpr ON b.id = bpr.bag_id
ORDER BY bs.created_at DESC, b.bag_name, bpr.pkl_name;

---5. 创建选中centerlines表
CREATE TABLE `lane_scene_centerlines` (
    `id` int NOT NULL AUTO_INCREMENT,
    `pkl_id` int NOT NULL COMMENT '关联bag_pkl_relation中的PKL文件ID',
    `selected_centerline_ids` json NOT NULL COMMENT '选中的centerline ID列表，JSON格式存储',
    `bag_id` int DEFAULT NULL COMMENT '关联的bag ID',
    `employee_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员工号',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_pkl_evaluation` (`pkl_id`),
    KEY `idx_pkl_id` (`pkl_id`),
    KEY `idx_employee_id` (`employee_id`),
    CONSTRAINT `lane_scene_centerlines_ibfk_1` FOREIGN KEY (`pkl_id`) REFERENCES `bag_pkl_relation` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 11 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '选中centerlines表'

---6. 创建场景标签表
CREATE TABLE `lane_scene_tags` (
    `id` int NOT NULL AUTO_INCREMENT,
    `pkl_id` int NOT NULL COMMENT '关联bag_pkl_relation中的PKL文件ID',
    `tags` json DEFAULT NULL,
    `bag_id` int DEFAULT NULL COMMENT '关联的bag ID',
    `employee_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作员工号',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_pkl_evaluation` (`pkl_id`),
    KEY `idx_pkl_id` (`pkl_id`),
    KEY `idx_employee_id` (`employee_id`),
    CONSTRAINT `lane_scene_tags_ibfk_1` FOREIGN KEY (`pkl_id`) REFERENCES `bag_pkl_relation` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 45 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'lane_scene标签表'

CREATE TABLE bag_set_bag_relation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bag_set_id INT NOT NULL COMMENT '关联的bag集ID',
    bag_id INT NOT NULL COMMENT '关联的bag ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (bag_set_id) REFERENCES bag_set(id) ON DELETE CASCADE,
    FOREIGN KEY (bag_id) REFERENCES bag(id) ON DELETE CASCADE,
    UNIQUE KEY unique_bag_set_relation (bag_set_id, bag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='bag集与bag的关联表';