DELIMITER $$

DROP PROCEDURE IF EXISTS proc_delete_pkl_before_bag_id $$
CREATE PROCEDURE proc_delete_pkl_before_bag_id(IN p_threshold INT, IN p_batch INT)
BEGIN
    DECLARE v_cnt INT DEFAULT 0;
    DECLARE v_total BIGINT DEFAULT 0;

    REPEAT
        DELETE FROM bag_pkl_relation
        WHERE bag_id < p_threshold
        LIMIT p_batch;
        SET v_cnt = ROW_COUNT();
        SET v_total = v_total + v_cnt;
    UNTIL v_cnt < p_batch END REPEAT;

    SELECT CONCAT('Deleted ', v_total, ' rows with bag_id < ', p_threshold) AS result;
END $$
DELIMITER ;

-- 调用
CALL proc_delete_pkl_before_bag_id(45780, 1000);