-- 设置要删除的bag_set_id
SET @bag_set_id = 10;

-- 查看要删除的数据统计
SELECT 
    bs.set_name,
    COUNT(DISTINCT b.id) as bag_count,
    COUNT(bpr.id) as pkl_relation_count
FROM bag_set bs
LEFT JOIN bag b ON bs.id = b.bag_set_id
LEFT JOIN bag_pkl_relation bpr ON b.id = bpr.bag_id
WHERE bs.id = @bag_set_id
GROUP BY bs.id, bs.set_name;

-- 手动多次执行以下语句直到影响行数小于1000
DELETE bpr FROM bag_pkl_relation bpr
INNER JOIN bag b ON bpr.bag_id = b.id
WHERE b.bag_set_id = @bag_set_id
LIMIT 1000;

-- 检查每次删除的影响行数
SELECT ROW_COUNT() as deleted_rows;

-- 最后删除其余关联数据
START TRANSACTION;
DELETE FROM bag_set_bag_relation WHERE bag_set_id = @bag_set_id;
DELETE FROM bag WHERE bag_set_id = @bag_set_id;
DELETE FROM bag_set WHERE id = @bag_set_id;
COMMIT;