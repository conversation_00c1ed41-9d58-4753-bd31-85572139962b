@router.get("/export-dlp-annotations/{evaluation_set_id}")
async def export_dlp_annotations_by_set(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """
    导出指定评测集下的DLP标注结果，以pkl_dir+pkl_name为索引，同时包含检查状态和标签信息

    参数:
    - evaluation_set_id: 路径参数，评测集ID

    返回:
    - 以{pkl_path: [标注结果]} 格式的数据，每条路径包含is_checked字段和tag信息
    """
    try:
        # 检查评测集ID是否存在
        check_set_query = "SELECT id FROM evaluation_set WHERE id = %s"
        set_exists_result = execute_query(
            check_set_query, (evaluation_set_id,), fetch_one=True
        )
        if not set_exists_result["success"] or not set_exists_result["data"]:
            raise HTTPException(
                status_code=404, detail=f"评测集ID {evaluation_set_id} 不存在"
            )

        # 修改查询，添加检查状态信息和标签信息
        query = """
        SELECT
            CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            a.traj_index,
            a.annotation,
            a.inference_config_id,
            a.updated_at,
            a.employee_id,
            COALESCE(escp.is_checked, FALSE) as is_checked,
            escp.checked_at,
            escp.checked_by,
            dso.selected_object_ids,
            dt.tag,  -- 添加标签字段
            p.id as pkl_id,  -- 添加pkl_id用于后续处理
            p.vin,  -- 添加VIN信息
            p.vehicle_type  -- 添加车型信息
        FROM
            dlp_path_annotation a
        JOIN
            evaluation_case_pool p ON a.pkl_id = p.id
        LEFT JOIN
            evaluation_set_case_pool escp ON a.pkl_id = escp.evaluation_case_id
            AND escp.evaluation_set_id = %s
        LEFT JOIN
            dlp_selected_objects dso ON a.pkl_id = dso.pkl_id
        LEFT JOIN
            dlp_tags dt ON a.pkl_id = dt.pkl_id 
            AND dt.evaluation_set_id = %s  -- 确保获取对应评测集的标签
        WHERE
            a.evaluation_set_id = %s
        ORDER BY
            pickle_path, a.traj_index
        """

        result = execute_query(
            query, (evaluation_set_id, evaluation_set_id, evaluation_set_id), fetch_all=True
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据，按pickle_path分组
        annotations_by_path = {}

        for row in result["data"]:
            (
                pickle_path,
                traj_index,
                annotation,
                db_inference_config_id,
                updated_at,
                employee_id,
                is_checked,
                checked_at,
                checked_by,
                select_objected_ids,
                tag,  # 新增标签字段
                pkl_id,  # 新增pkl_id
                vin,  # 新增VIN
                vehicle_type,  # 新增车型
            ) = row
            
            id_list = []
            # transfer objected_ids to list
            if select_objected_ids is not None:
                select_objected_ids = json.loads(select_objected_ids)
                id_list = [int(id.split("_")[1]) for id in select_objected_ids]
            
            # 确保路径存在于字典中
            if pickle_path not in annotations_by_path:
                annotations_by_path[pickle_path] = {
                    "pkl_id": pkl_id,  # 添加pkl_id
                    "vin": vin,  # 添加VIN信息
                    "vehicle_type": vehicle_type,  # 添加车型信息
                    "tag": tag,  # 添加标签信息
                    "metadata": {
                        "last_updated": None,
                        "last_updated_by": None,
                    },
                    "selected_object_ids": id_list,
                    "annotations": []  # 将标注数据放在annotations数组中
                }

            # 添加标注信息，包含检查状态
            annotations_by_path[pickle_path]["annotations"].append({
                "traj_index": traj_index,
                "annotation": annotation,
                "inference_config_id": db_inference_config_id,
                "is_checked": bool(is_checked),  # 确保是布尔值
                "checked_at": checked_at.isoformat() if checked_at else None,
                "checked_by": checked_by,
            })
            
            # 更新最近更新时间和人员
            if updated_at and (
                not annotations_by_path[pickle_path]["metadata"]["last_updated"]
                or updated_at.isoformat()
                > annotations_by_path[pickle_path]["metadata"]["last_updated"]
            ):
                annotations_by_path[pickle_path]["metadata"]["last_updated"] = (
                    updated_at.isoformat()
                )
                annotations_by_path[pickle_path]["metadata"]["last_updated_by"] = employee_id

        # 统计信息
        total_annotations = len(result["data"])
        pkl_with_tags = sum(1 for data in annotations_by_path.values() if data.get('tag'))
        pkl_with_annotations = len(annotations_by_path)

        return {
            "success": True,
            "evaluation_set_id": evaluation_set_id,
            "export_time": datetime.now().isoformat(),
            "statistics": {
                "total_annotations": total_annotations,
                "total_pkls": pkl_with_annotations,
                "pkl_with_tags": pkl_with_tags,
            },
            "data": annotations_by_path,
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")