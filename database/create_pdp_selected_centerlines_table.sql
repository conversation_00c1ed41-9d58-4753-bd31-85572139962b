-- 创建pdp_selected_centerlines表，用于存储选中的centerlines
CREATE TABLE IF NOT EXISTS selected_centerlines (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pkl_id INT NOT NULL COMMENT '关联evaluation_case_pool中的PKL文件ID',
    selected_centerline_ids JSON NOT NULL COMMENT '选中的centerline ID列表，JSON格式存储',
    evaluation_set_id INT DEFAULT NULL COMMENT '关联的评测集ID',
    bag_id INT DEFAULT NULL COMMENT '关联的bag ID',
    employee_id VARCHAR(50) DEFAULT NULL COMMENT '操作员工号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- FOREIGN KEY (pkl_id) REFERENCES evaluation_case_pool(id) ON DELETE CASCADE,
    -- FOREIGN KEY (evaluation_set_id) REFERENCES evaluation_set(id) ON DELETE SET NULL,
    UNIQUE KEY unique_pkl_evaluation (pkl_id, evaluation_set_id),
    INDEX idx_pkl_id (pkl_id),
    INDEX idx_evaluation_set_id (evaluation_set_id),
    INDEX idx_employee_id (employee_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PDP选中centerlines表';
