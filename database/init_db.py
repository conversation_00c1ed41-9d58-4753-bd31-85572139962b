# database/create_db_user.py

import mysql.connector
from database.config import EVAL_DB_CONFIG

# 连接到 MySQL，不指定数据库（因为可能要先创建数据库）
config = EVAL_DB_CONFIG.copy()
config.pop('database', None)  # 删除 database 字段

conn = mysql.connector.connect(**config)
cursor = conn.cursor()

# 创建数据库（如果不存在）
# cursor.execute("DROP DATABASE IF EXISTS my_eval_db;")
cursor.execute(
    "CREATE DATABASE IF NOT EXISTS my_eval_db CHARACTER SET utf8 COLLATE utf8_general_ci;"
)

print("✅ 数据库 my_eval_db 创建完成（如已存在则跳过）")

# 切换到该数据库
cursor.execute("USE my_eval_db;")
# cursor.execute("DROP TABLE IF EXISTS pdp_path_annotation;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_dlp_class_bool_metrics;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_dlp_class_float_metrics;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_path_class_bool_metrics;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_path_class_float_metrics;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_topk_bool_metrics;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_topk_metrics;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_result;")
# cursor.execute("DROP TABLE IF EXISTS inference_result;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_set_case_pool;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_set;")
# cursor.execute("DROP TABLE IF EXISTS evaluation_case_pool;")

# 创建表 evaluation_case_pool
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_case_pool (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pkl_dir VARCHAR(1024) NOT NULL,
    pkl_name VARCHAR(512) NOT NULL,
    vehicle_type SET('B10','C10','C11','C16','B01') NOT NULL,
    vin VARCHAR(255) NOT NULL,
    time_ns BIGINT UNSIGNED NOT NULL,
    key_obs_id INT DEFAULT 0,
    path_range JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dirty_data BOOLEAN DEFAULT FALSE, -- 是否脏数据
    insert_uuid CHAR(36) DEFAULT NULL, -- 新增字段，存UUID
    UNIQUE KEY unique_insert_uuid (insert_uuid), -- 给UUID加唯一索引
    UNIQUE(vin, time_ns, key_obs_id)  -- 确保同一评测集不重复
);
""")

cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_set (
    id INT AUTO_INCREMENT PRIMARY KEY,
    set_name VARCHAR(255) NOT NULL,  -- 评测集的名称
    creator_name VARCHAR(255) NOT NULL,  -- 评测集创建人
    description TEXT,  -- 评测集的描述
    scene_tag SET('EFFICIENCY_LCR','LEFT_TURN','RIGHT_TURN','NUDGE_STATIC_OBS','NUDGE_SLOW_OBS','ENV_CONSTRAINT'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 评测集创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,  -- 评测集更新时间
    UNIQUE(set_name)  -- 确保同一创建人不能创建同名评测集
);
""")
cursor.execute("""
    CREATE TABLE IF NOT EXISTS evaluation_set_case_pool (
    evaluation_set_id INT,  -- 关联的评测集 ID
    evaluation_case_id INT,  -- 关联的评测案例 ID
    PRIMARY KEY (evaluation_set_id, evaluation_case_id),
    FOREIGN KEY (evaluation_set_id) REFERENCES evaluation_set(id) ON DELETE CASCADE,
    FOREIGN KEY (evaluation_case_id) REFERENCES evaluation_case_pool(id) ON DELETE CASCADE
);
""")

cursor.execute("""
CREATE TABLE IF NOT EXISTS inference_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    json_name VARCHAR(255) NOT NULL,
    json_content JSON NOT NULL,
    json_md5 CHAR(32) NOT NULL,
    
    pth_name VARCHAR(255) NOT NULL,
    pth_dir VARCHAR(512) NOT NULL,
    pth_md5 CHAR(32) NOT NULL,
    
    pth_upload_time DATETIME,
    UNIQUE(json_md5, pth_md5)
);
""")

# 创建inference_result表
cursor.execute("""
CREATE TABLE IF NOT EXISTS inference_result (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inference_config_id INT NOT NULL,
    result_binary MEDIUMBLOB NOT NULL,
    pkl_id INT NOT NULL,
    attachment_info TEXT,
    FOREIGN KEY (inference_config_id) REFERENCES inference_config(id),
    FOREIGN KEY (pkl_id) REFERENCES evaluation_case_pool(id),
    UNIQUE(inference_config_id, pkl_id)
);
""")
print("✅ 表 evaluation_case_pool 创建成功 (如果存在则跳过)")
# 创建evaluation_result表
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_result (
    id INT AUTO_INCREMENT PRIMARY KEY,
    inference_result_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inference_result_id) REFERENCES inference_result(id),
    UNIQUE(inference_result_id)
);
""")

# 创建 TopKMetrics 表
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_topk_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    evaluation_id INT NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- 'prediction_accuracy_ade', 'pathformer_accuracy_40_fde'等
    top_1 FLOAT,
    top_3 FLOAT,
    top_6 FLOAT,
    count INT,
    FOREIGN KEY (evaluation_id) REFERENCES evaluation_result(id) ON DELETE CASCADE,
    UNIQUE(evaluation_id, metric_type)
);
""")

# 创建 TopKBoolMetrics 表
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_topk_bool_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    evaluation_id INT NOT NULL,
    metric_type VARCHAR(50) NOT NULL, -- 'static_4s_collision'等
    top_1 BOOLEAN,
    top_3 BOOLEAN,
    top_6 BOOLEAN,
    count INT,
    FOREIGN KEY (evaluation_id) REFERENCES evaluation_result(id) ON DELETE CASCADE,
    UNIQUE(evaluation_id, metric_type)
);
""")

# 创建 PathClassFloatMetrics 表
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_path_class_float_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    evaluation_id INT NOT NULL,
    path_class INT NOT NULL,
    ade FLOAT,
    fde FLOAT,
    count INT,
    FOREIGN KEY (evaluation_id) REFERENCES evaluation_result(id) ON DELETE CASCADE,
    UNIQUE(evaluation_id, path_class)
);
""")

# 创建 PathClassBoolMetrics 表
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_path_class_bool_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    evaluation_id INT NOT NULL,
    path_class INT NOT NULL,
    collision BOOLEAN,
    count INT,
    FOREIGN KEY (evaluation_id) REFERENCES evaluation_result(id) ON DELETE CASCADE,
    UNIQUE(evaluation_id, path_class)
);
""")

# 创建 DlpClassFloatMetrics 表
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_dlp_class_float_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    evaluation_id INT NOT NULL,
    dlp_class INT NOT NULL,
    ade FLOAT,
    fde FLOAT,
    count INT,
    FOREIGN KEY (evaluation_id) REFERENCES evaluation_result(id) ON DELETE CASCADE,
    UNIQUE(evaluation_id, dlp_class)
);
""")

# 创建 DlpClassBoolMetrics 表
cursor.execute("""
CREATE TABLE IF NOT EXISTS evaluation_dlp_class_bool_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    evaluation_id INT NOT NULL,
    dlp_class INT NOT NULL,
    collision BOOLEAN,
    count INT,
    FOREIGN KEY (evaluation_id) REFERENCES evaluation_result(id) ON DELETE CASCADE,
    UNIQUE(evaluation_id, dlp_class)
);
""")
cursor.execute("""
CREATE TABLE IF NOT EXISTS pdp_path_annotation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pkl_id INT NOT NULL COMMENT '关联evaluation_case_pool中的ID',
    inference_config_id INT,
    path_index INT NOT NULL COMMENT 'pdp_path中的路径索引',
    annotation ENUM('good', 'bad','unknown') NOT NULL COMMENT '标注结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (pkl_id) REFERENCES evaluation_case_pool(id) ON DELETE CASCADE,
    FOREIGN KEY (inference_config_id) REFERENCES inference_config(id),
    UNIQUE(pkl_id, path_index, inference_config_id)
);
""")
# cursor.execute("""
# ALTER TABLE pdp_path_annotation
# ADD COLUMN IF NOT EXISTS
#     evaluation_set_id INT DEFAULT NULL COMMENT '关联的评测集ID',
# ADD FOREIGN KEY (evaluation_set_id) REFERENCES evaluation_set(id) ON DELETE SET NULL;
# """)
cursor.execute("""
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    employee_id VARCHAR(50) UNIQUE NOT NULL COMMENT '工号',
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user', 'annotator') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
""")

# 会话表
cursor.execute("""
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
""")

# 以下负责 bag validity check 相关任务
cursor.execute("""
CREATE TABLE IF NOT EXISTS validity_check_task_pkls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pkl_name VARCHAR(255) NOT NULL COMMENT 'PKL文件名',
    pkl_dir VARCHAR(500) NOT NULL COMMENT 'PKL文件目录',
    bag_name VARCHAR(255) COMMENT 'Bag名称',
    vehicle_type ENUM('C10', 'B10', 'C11', 'B01', 'C01', 'C16') COMMENT '车辆类型',
    date DATE COMMENT '数据日期',
    mode ENUM('URP', 'CNAP') COMMENT '模式',
    version ENUM('3.0', '3.5', '4.0') COMMENT '版本',
    scene_tag VARCHAR(100) COMMENT '场景标签',
    set_id INT COMMENT '平台数据集id',
    is_checked BOOLEAN DEFAULT FALSE COMMENT '是否已审核',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_pkl (pkl_name, pkl_dir),
    INDEX idx_vehicle_type (vehicle_type),
    INDEX idx_mode (mode),
    INDEX idx_date (date)
);
""")

cursor.execute("""
CREATE TABLE IF NOT EXISTS validity_check_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_name VARCHAR(255) NOT NULL COMMENT '任务名称',
    description TEXT COMMENT '任务描述',
    creator_id VARCHAR(50) NOT NULL COMMENT '创建者ID',
    status ENUM('draft', 'active', 'completed', 'cancelled') DEFAULT 'draft' COMMENT '任务状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_creator_id (creator_id),
    INDEX idx_status (status)
);
""")
cursor.execute("""
CREATE TABLE IF NOT EXISTS validity_check_task_pkl_map (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    pkl_id INT NOT NULL,
    FOREIGN KEY (task_id) REFERENCES validity_check_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (pkl_id) REFERENCES validity_check_task_pkls(id) ON DELETE CASCADE,
    UNIQUE KEY unique_task_pkl (task_id, pkl_id),
    INDEX idx_task_id (task_id),
    INDEX idx_pkl_id (pkl_id)
);
""")
cursor.execute("""
CREATE TABLE IF NOT EXISTS validity_check_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    annotator_id VARCHAR(50) NOT NULL COMMENT '标注员ID',
    assigned_by VARCHAR(50) NOT NULL COMMENT '分配者ID',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL COMMENT '开始标注时间',
    completed_at TIMESTAMP NULL COMMENT '完成标注时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES validity_check_tasks(id) ON DELETE CASCADE,
    UNIQUE KEY unique_assignment (task_id, annotator_id),
    INDEX idx_task_id (task_id),
    INDEX idx_annotator_id (annotator_id)
);
""")
cursor.execute("""
CREATE TABLE IF NOT EXISTS validity_check_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    task_id INT NOT NULL,
    pkl_id INT NOT NULL,
    annotator_id VARCHAR(50) NOT NULL,
    validity ENUM('valid', 'bad_gt', 'not_centered', 'on_opposite_road', 'cross_boundary', 'deviate_from_navi' ,'unknown') COMMENT '质量评分',
    reviewer_id VARCHAR(50) COMMENT '审核员ID',
    review_status ENUM('pending', 'approved', 'rejected', 'needs_revision') DEFAULT 'pending',
    review_time TIMESTAMP NULL COMMENT '审核时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assignment_id) REFERENCES validity_check_assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES validity_check_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (pkl_id) REFERENCES validity_check_task_pkls(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_annotator_id (annotator_id),
    INDEX idx_review_status (review_status)
);
""")
cursor.execute("""
CREATE TABLE IF NOT EXISTS validity_check_annotator_pkl_map (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    annotator_id VARCHAR(50) NOT NULL COMMENT '标注员ID',
    pkl_id INT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否已完成标注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES validity_check_tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (pkl_id) REFERENCES validity_check_task_pkls(id) ON DELETE CASCADE,
    UNIQUE KEY unique_task_annotator_pkl (task_id, annotator_id, pkl_id),
    INDEX idx_task_id (task_id),
    INDEX idx_annotator_id (annotator_id),
    INDEX idx_pkl_id (pkl_id)
);
""")
cursor.close()
conn.close()
