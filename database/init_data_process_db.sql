-- 创建数据库
CREATE DATABASE autonomous_driving_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE autonomous_driving_data;

-- 创建 bag_infos 表
CREATE TABLE `bag_infos` (
    `id` int NOT NULL AUTO_INCREMENT,
    `bag_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'bag文件名称，保证唯一',
    `translated_dir` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '翻译目录路径',
    `img_dir` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图像目录路径',
    `set_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据集ID',
    `date` date NOT NULL COMMENT '数据日期',
    `vin` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车辆识别号',
    `version` enum('3.0', '3.5') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本号',
    `mode` enum('CNAP', 'URP') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模式',
    `redmine_tag` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Redmine标签',
    `origin_dir` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始目录路径',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `bag_name` (`bag_name`),
    KEY `idx_date` (`date`),
    KEY `idx_version` (`version`),
    KEY `idx_mode` (`mode`),
    KEY `idx_vin` (`vin`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'bag文件信息表'

-- 创建 pickle_infos 表
CREATE TABLE `pickle_infos` (
    `id` int NOT NULL AUTO_INCREMENT,
    `bag_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的bag文件名称',
    `pkl_dir` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'pickle文件目录路径',
    `pkl_utility` enum('全量', '打分器', '打点', '预测') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'pickle文件用途',
    `date` date NOT NULL COMMENT '数据日期',
    `version` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '版本号',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_bag_name` (`bag_name`),
    KEY `idx_pkl_utility` (`pkl_utility`),
    KEY `idx_date` (`date`),
    KEY `idx_version` (`version`),
    CONSTRAINT `pickle_infos_ibfk_1` FOREIGN KEY (`bag_name`) REFERENCES `bag_infos` (`bag_name`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'pickle文件信息表'

-- 创建用于查询bag和对应pickle信息的视图
CREATE VIEW bag_pickle_view AS
SELECT 
    b.bag_name,
    b.translated_dir,
    b.img_dir,
    b.set_id,
    b.date as bag_date,
    b.vin,
    b.version as bag_version,
    b.mode,
    b.redmine_tag,
    b.origin_dir,
    p.pkl_dir,
    p.pkl_utility,
    p.date as pkl_date,
    p.version as pkl_version
FROM bag_infos b
LEFT JOIN pickle_infos p ON b.bag_name = p.bag_name;