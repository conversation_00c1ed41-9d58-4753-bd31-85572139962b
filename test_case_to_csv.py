import pandas as pd
txt_dir = './0528_7w.txt'
with open(txt_dir, 'r') as file:
    lines = file.readlines()
pkls_list = []
for line in lines:
    line = line.strip()
    pkl_name = line.split('/')[-1]
    pkl_dir = line.split('/')[:-1]
    pkl_dir = '/'.join(pkl_dir)
    if line:
        pkls_list.append([pkl_name, pkl_dir])
df = pd.DataFrame(pkls_list, columns=['pkl_name', 'pkl_dir']).to_csv(
    txt_dir.replace('txt', 'csv'), index=False)
