from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from database.db_operations import execute_query
from api.auth.auth import get_current_user

router = APIRouter(prefix="/api/annotation", tags=["annotation"])

class ClipTimeRange(BaseModel):
    time_ns: int
    time_range_seconds: int = 3  # 默认前后3秒

class PdpClipAnnotation(BaseModel):
    pkl_id: int
    time_ns: int
    annotation: str  # "good" or "bad"
    evaluation_set_id: Optional[int] = None

@router.get("/pdp-clip/{time_ns}")
async def get_clip_pkls(
    time_ns: int,
    time_range_seconds: int = 3,
    evaluation_set_id: Optional[int] = None
):
    """获取指定时间戳前后时间范围内的所有PKL文件"""
    time_range_ns = time_range_seconds * 1000000000  # 转换为纳秒
    
    query = """
    SELECT * FROM evaluation_case_pool 
    WHERE time_ns BETWEEN %s - %s AND %s + %s
    ORDER BY time_ns
    """
    params = (time_ns, time_range_ns, time_ns, time_range_ns)
    result = execute_query(query, params)
    
    if not result["success"]:
        raise HTTPException(status_code=500, detail="数据库查询失败")
    
    return {"pkls": result["data"]}

@router.post("/pdp-clip-annotation")
async def annotate_clip(
    annotation: PdpClipAnnotation,
    current_user: Optional[dict] = Depends(get_current_user)
):
    """标注时间片段内的PKL文件"""
    # 验证标注值
    if annotation.annotation not in ["good", "bad", "unknown"]:
        raise HTTPException(status_code=400, detail="标注值必须是'good'或'bad'或'unknown'")
    
    # TODO: 实现标注逻辑
    return {"success": True, "message": "标注成功"}