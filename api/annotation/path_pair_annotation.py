from fastapi import APIRouter, HTTPException, Body, Path, Query, Depends
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime
import os
import pickle
import random
import itertools
from database.db_operations import execute_query
from api.visualize.pickle_visualize import load_pickle
from api.auth.auth import get_current_user
import json
import io
import csv
import numpy as np
import cv2
import base64
from api.visualize.calib_reader import CalibReader,CalibReaderDict

calib_reader_dict=CalibReaderDict()

router = APIRouter(prefix="/api/annotation", tags=["path_pair_annotation"])

class PathInfo(BaseModel):
    index: int  # -1表示GT路径
    type: str  # 'ground_truth' | 'predicted'
    probability: float
    points: List[List[float]]

class PathPair(BaseModel):
    pair_id: int
    path_a: PathInfo
    path_b: PathInfo
class ExportRequest(BaseModel):
    evaluation_set_id: int
    include_conflicts: bool = True
    export_format: str = "json"  # json 或 csv

class PathPairAnnotationRequest(BaseModel):
    pkl_id: int
    path_a_index: int
    path_b_index: int
    comparison_result: str  # 'A_better' | 'B_better' | 'indistinguishable'
    evaluation_set_id: int
    annotator_id: str

def select_6_paths_for_annotation(pdp_paths, ego_gt=None):
    """
    选择6条路径用于pair标注的固定策略
    """
    selected_paths = []
    
    # 1. 如果有GT路径，优先包含
    if ego_gt is not None and len(ego_gt) > 0:
        selected_paths.append({
            'index': -1,  # GT路径用-1表示
            'type': 'ground_truth',
            'probability': 1.0,
            'points': ego_gt
        })
    
    # 2. 按概率排序，选择概率最高的路径
    if isinstance(pdp_paths, dict):
        path_items = [(idx, path) for idx, path in pdp_paths.items()]
    else:
        path_items = list(enumerate(pdp_paths))
    
    # 过滤出有效的预测路径
    # valid_paths = []
    for idx, path in path_items:
        if isinstance(path, dict):
            points = path.get('raw_points', [])
            prob = path.get('prob', 0.0)
        if len(points) > 0:
            selected_paths.append({
                'index': idx,
                'type': 'predicted',
                'probability': prob,
                'points': points
            })
    
    # 按概率排序
    selected_paths.sort(key=lambda x: x['probability'], reverse=True)
    
    # 选择剩余的路径
    remaining_slots = 6 - len(selected_paths)
    selected_paths = selected_paths[:6]  # 确保最多6条路径
    
    # 确保有足够的路径
    if len(selected_paths) < 2:
        raise ValueError("至少需要2条路径才能进行pair标注")
    
    return selected_paths

def generate_pairs(selected_paths):
    """生成所有可能的pair组合"""
    pairs = []
    for i in range(len(selected_paths)):
        for j in range(i + 1, len(selected_paths)):
            pairs.append({
                'pair_id': len(pairs),
                'path_a': selected_paths[i],
                'path_b': selected_paths[j]
            })
    return pairs

def generate_all_path_combinations_and_select(pdp_paths, ego_gt=None, target_pairs=15):
    """
    生成所有可能的路径组合C(N,2)，然后随机选择指定数量的pairs
    """
    all_paths = []
    
    # 1. 如果有GT路径，加入路径列表
    if ego_gt is not None and len(ego_gt) > 0:
        all_paths.append({
            'index': -1,  # GT路径用-1表示
            'type': 'ground_truth',
            'probability': 1.0,
            'points': ego_gt
        })
    
    # 2. 处理所有预测路径
    if isinstance(pdp_paths, dict):
        path_items = [(idx, path) for idx, path in pdp_paths.items()]
    else:
        path_items = list(enumerate(pdp_paths))
    
    # 过滤出有效的预测路径
    for idx, path in path_items:
        if isinstance(path, dict):
            points = path.get('raw_points', [])
            prob = path.get('prob', 0.0)
            if len(points) > 0:
                all_paths.append({
                    'index': idx,
                    'type': 'predicted',
                    'probability': prob,
                    'points': points
                })
    
    # 确保有足够的路径
    if len(all_paths) < 2:
        raise ValueError("至少需要2条路径才能进行pair标注")
    
    # 3. 生成所有可能的路径组合 C(N,2)
    all_combinations = list(itertools.combinations(all_paths, 2))
    
    # 4. 随机选择指定数量的pairs
    if len(all_combinations) <= target_pairs:
        # 如果总组合数不超过目标数量，返回所有组合
        selected_combinations = all_combinations
    else:
        # 随机选择target_pairs个组合
        selected_combinations = random.sample(all_combinations, target_pairs)
    
    # 5. 构建pairs格式
    pairs = []
    for i, (path_a, path_b) in enumerate(selected_combinations):
        pairs.append({
            'pair_id': i,
            'path_a': path_a,
            'path_b': path_b
        })
    
    return pairs

@router.get("/path-pairs/{pkl_id}")
async def get_path_pairs(
    pkl_id: int,
    evaluation_set_id: Optional[int] = Query(None, description="评测集ID")
):
    """获取指定PKL文件中的路径pairs"""
    # 查询pkl文件路径
    query = """
    SELECT pkl_dir, pkl_name, vin FROM evaluation_case_pool WHERE id = %s
    """
    result = execute_query(query, (pkl_id,), fetch_one=True)

    if not result["success"] or not result["data"]:
        raise HTTPException(status_code=404, detail="PKL文件不存在")

    pkl_dir, pkl_name,vin = result["data"]
    pickle_path = os.path.join(pkl_dir, pkl_name)

    # 检查文件是否存在
    # print('...',pickle_path,os.path.exists(pickle_path))
    if not os.path.exists(pickle_path):
        raise HTTPException(status_code=404, detail="PKL文件不存在于文件系统中")

    try:
        # 首先尝试从数据库获取已保存的路径对
        existing_pairs = get_path_pairs_from_db(pkl_id, evaluation_set_id)
        
        # 处理e2e_image图像
        image_data = None
        data = load_pickle(pickle_path)
        image = data.get('e2e_image', None)
        ego_yaw=-1.*data.get('feature_obj',None)[0,0,10]
        # print('.bbb')
        if image:
            np_image = image.get('image', None)
            if np_image is not None and isinstance(np_image, np.ndarray):
                # 将numpy数组格式转换为适合前端显示的格式
                try:
                    # 转换为RGB图像
                    if len(np_image.shape) == 3 and np_image.shape[0] == 3:
                        # 如果是(3, height, width)格式，转换为(height, width, 3)
                        np_image = np.transpose(np_image, (1, 2, 0))

                    # 确保图像数据在0-255范围内
                    if np_image.max() <= 1.0:
                        np_image = (np_image * 255).astype(np.uint8)
                    np_image = cv2.cvtColor(np_image, cv2.COLOR_YUV2BGR)
                    # 编码为JPEG图像
                    _, buffer = cv2.imencode('.jpg', np_image)
                    image_data = base64.b64encode(buffer).decode('utf-8')
                except Exception as e:
                    print(f"处理图像出错: {str(e)}")
        # print('...a',existing_pairs)
        if existing_pairs:
            # 如果数据库中有记录，需要重新构建完整的路径信息
            # 重新加载pickle文件获取完整路径数据
            data = load_pickle(pickle_path)
            pdp_paths = data.get('pdp_path', [])
            
            # 获取GT路径
            ego_gt = None
            ego_gt_points = data.get('path_point', [])
            ego_gt_mask = data.get('path_mask', [])
            # print('...a1')
            if ego_gt_points is not None and ego_gt_mask is not None and len(ego_gt_mask) > 0 and ego_gt_mask[-1] >= 0.5:
                gt_visualization_points = []
                for point, mask in zip(ego_gt_points, ego_gt_mask):
                    if mask >= 0.5:
                        gt_visualization_points.append([float(point[0]), float(point[1]), 0.0])
                
                if len(gt_visualization_points) > 0:
                    if len(gt_visualization_points) > 50:
                        step = max(1, len(gt_visualization_points) // 50)
                        gt_visualization_points = gt_visualization_points[::step]
                    ego_gt = gt_visualization_points
            
            # 重新构建所有路径以获取完整信息
            all_paths = []
            
            # 添加GT路径
            if ego_gt is not None and len(ego_gt) > 0:
                all_paths.append({
                    'index': -1,
                    'type': 'ground_truth',
                    'probability': 1.0,
                    'points': ego_gt
                })
                # print('...a2')
            
            # 添加预测路径
            if isinstance(pdp_paths, dict):
                path_items = [(idx, path) for idx, path in pdp_paths.items()]
                # print('...a3')
            else:
                path_items = list(enumerate(pdp_paths))
            
            for idx, path in path_items:
                if isinstance(path, dict):
                    # print('...a4')
                    points = path.get('raw_points', [])
                    prob = path.get('prob', 0.0)
                    if len(points) > 0:
                        all_paths.append({
                            'index': idx,
                            'type': 'predicted',
                            'probability': prob,
                            'points': points
                        })
            
            # 构建路径映射
            path_mapping = {}
            for path in all_paths:
                path_mapping[path['index']] = path
            
            # 重新构建完整的pair信息
            full_path_pairs = []
            for pair_def in existing_pairs:
                path_a_index = pair_def['path_a_index']
                path_b_index = pair_def['path_b_index']
                
                if path_a_index in path_mapping and path_b_index in path_mapping:
                    full_path_pairs.append({
                        'pair_id': pair_def['pair_id'],
                        'path_a': path_mapping[path_a_index],
                        'path_b': path_mapping[path_b_index]
                    })
            
            # 返回完整的pair信息而不是简化版本
            path_pairs = full_path_pairs
        else:
            # print('...b')
            # 如果没有记录，重新生成并保存
            data = load_pickle(pickle_path)
            
            # 提取PDP路径
            pdp_paths = data.get('pdp_path', [])
            
            # 获取GT路径
            ego_gt = None
            ego_gt_points = data.get('path_point', [])
            ego_gt_mask = data.get('path_mask', [])
            
            if ego_gt_points is not None and ego_gt_mask is not None and len(ego_gt_mask) > 0 and ego_gt_mask[-1] >= 0.5:
                gt_visualization_points = []
                for point, mask in zip(ego_gt_points, ego_gt_mask):
                    if mask >= 0.5:
                        gt_visualization_points.append([float(point[0]), float(point[1]), 0.0])
                
                if len(gt_visualization_points) > 0:
                    if len(gt_visualization_points) > 50:
                        step = max(1, len(gt_visualization_points) // 50)
                        gt_visualization_points = gt_visualization_points[::step]
                    ego_gt = gt_visualization_points
            
            # 使用新的生成策略：所有路径组合然后随机选择15条
            full_path_pairs = generate_all_path_combinations_and_select(pdp_paths, ego_gt, target_pairs=15)
            
            # 保存到数据库
            save_path_pairs_to_db(pkl_id, evaluation_set_id, full_path_pairs)
            
            # 直接返回完整的pair信息
            path_pairs = full_path_pairs
        
        # 查询现有标注
        annotation_query = """
        SELECT path_a_index, path_b_index, comparison_result, annotator_id, created_at
        FROM path_pair_annotation 
        WHERE pkl_id = %s AND evaluation_set_id = %s
        """
        annotation_result = execute_query(
            annotation_query, 
            (pkl_id, evaluation_set_id), 
            fetch_all=True
        )
        
        annotations = []
        if annotation_result["success"] and annotation_result["data"]:
            for row in annotation_result["data"]:
                annotations.append({
                    "path_a_index": row[0],
                    "path_b_index": row[1],
                    "comparison_result": row[2],
                    "annotator_id": row[3],
                    "created_at": row[4].isoformat() if row[4] else None
                })
        ego2img_array=calib_reader_dict.get_calib(vin=vin, scale=[0.2666, 0.2666], offset_x=0, offset_y=160*2160/576)['front_wide']['ego2img']
        # print('len',len(path_pairs))
        return {
            "success": True,
            "path_pairs": path_pairs,  # 现在返回完整的路径对信息
            "annotations": annotations,
            "total_pairs": len(path_pairs),
            "image_data": image_data,  # 添加图像数据
            "ego2img": ego2img_array.tolist() if ego2img_array is not None else None,
            "ego_yaw": ego_yaw
        }

    except Exception as e:
        # print('error for pair pickload')
        raise HTTPException(status_code=500, detail=f"处理PKL文件失败: {str(e)}")

@router.post("/path-pair-annotation")
async def annotate_path_pair(
    annotation: PathPairAnnotationRequest,
    current_user: Optional[dict] = Depends(get_current_user)
):
    """保存路径pair标注"""
    # 验证标注值
    valid_results = ['A_better', 'B_better', 'indistinguishable']
    if annotation.comparison_result not in valid_results:
        raise HTTPException(status_code=400, detail="无效的标注结果")

    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (annotation.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="PKL文件不存在")

    # 插入或更新标注
    upsert_query = """
    INSERT INTO path_pair_annotation 
    (pkl_id, path_a_index, path_b_index, comparison_result, annotator_id, evaluation_set_id)
    VALUES (%s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE 
    comparison_result = VALUES(comparison_result),
    updated_at = CURRENT_TIMESTAMP
    """
    
    params = (
        annotation.pkl_id,
        annotation.path_a_index,
        annotation.path_b_index,
        annotation.comparison_result,
        annotation.annotator_id,
        annotation.evaluation_set_id
    )
    
    result = execute_query(upsert_query, params)

    if not result["success"]:
        raise HTTPException(status_code=500, detail="保存标注失败")

    return {
        "success": True,
        "message": "标注保存成功"
    }


    
@router.get("/path-pair-overview-statistics/{evaluation_set_id}")
async def get_path_pair_overview_statistics(evaluation_set_id: int):
    """获取路径pair标注的总体统计信息"""
    try:
        # 获取PKL文件总体统计
        pkl_stats_query = """
        SELECT 
            COUNT(DISTINCT ecp.id) as total_pkls,
            COUNT(DISTINCT ppa_pkls.pkl_id) as annotated_pkls
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
        LEFT JOIN (
            SELECT DISTINCT pkl_id
            FROM path_pair_annotation
            WHERE evaluation_set_id = %s
        ) ppa_pkls ON ecp.id = ppa_pkls.pkl_id
        WHERE escp.evaluation_set_id = %s AND ecp.dirty_data = FALSE
        """
        
        pkl_stats_result = execute_query(pkl_stats_query, (evaluation_set_id, evaluation_set_id), fetch_one=True)
        
        total_pkls = 0
        annotated_pkls = 0
        if pkl_stats_result["success"] and pkl_stats_result["data"]:
            total_pkls = pkl_stats_result["data"][0] or 0
            annotated_pkls = pkl_stats_result["data"][1] or 0
        
        # 计算完全完成的PKL数量（所有pairs都被标注）
        completed_pkls_query = """
        SELECT COUNT(*) as completed_pkls
        FROM (
            SELECT ecp.id
            FROM evaluation_case_pool ecp
            JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
            LEFT JOIN (
                SELECT pkl_id, COUNT(*) as total_pairs
                FROM path_pair_definition
                WHERE evaluation_set_id = %s
                GROUP BY pkl_id
            ) ppd_count ON ecp.id = ppd_count.pkl_id
            LEFT JOIN (
                SELECT pkl_id, COUNT(DISTINCT CONCAT(path_a_index, '-', path_b_index)) as annotated_pairs
                FROM path_pair_annotation
                WHERE evaluation_set_id = %s
                GROUP BY pkl_id
            ) ppa_count ON ecp.id = ppa_count.pkl_id
            WHERE escp.evaluation_set_id = %s 
              AND ecp.dirty_data = FALSE
              AND ppd_count.total_pairs = ppa_count.annotated_pairs 
              AND ppd_count.total_pairs > 0
        ) completed_count
        """
        
        completed_result = execute_query(completed_pkls_query, (evaluation_set_id, evaluation_set_id, evaluation_set_id), fetch_one=True)
        completed_pkls = 0
        if completed_result["success"] and completed_result["data"]:
            completed_pkls = completed_result["data"][0] or 0
        
        # 获取Pair标注总体统计
        pair_stats_query = """
        SELECT 
            COALESCE(SUM(ppd_count.total_pairs), 0) as total_pairs,
            COALESCE(SUM(ppa_count.annotated_pairs), 0) as annotated_pairs
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
        LEFT JOIN (
            SELECT pkl_id, COUNT(*) as total_pairs
            FROM path_pair_definition
            WHERE evaluation_set_id = %s
            GROUP BY pkl_id
        ) ppd_count ON ecp.id = ppd_count.pkl_id
        LEFT JOIN (
            SELECT pkl_id, COUNT(DISTINCT CONCAT(path_a_index, '-', path_b_index)) as annotated_pairs
            FROM path_pair_annotation
            WHERE evaluation_set_id = %s
            GROUP BY pkl_id
        ) ppa_count ON ecp.id = ppa_count.pkl_id
        WHERE escp.evaluation_set_id = %s AND ecp.dirty_data = FALSE
        """
        
        pair_stats_result = execute_query(pair_stats_query, (evaluation_set_id, evaluation_set_id, evaluation_set_id), fetch_one=True)
        
        total_pairs = 0
        annotated_pairs = 0
        if pair_stats_result["success"] and pair_stats_result["data"]:
            total_pairs = pair_stats_result["data"][0] or 0
            annotated_pairs = pair_stats_result["data"][1] or 0
        
        # 获取标注一致性统计
        consistency_query = """
        SELECT 
            SUM(CASE WHEN unique_results = 1 THEN 1 ELSE 0 END) as consistent_pairs,
            SUM(CASE WHEN unique_results > 1 THEN 1 ELSE 0 END) as inconsistent_pairs
        FROM (
            SELECT 
                pkl_id,
                path_a_index,
                path_b_index,
                COUNT(DISTINCT comparison_result) as unique_results
            FROM path_pair_annotation 
            WHERE evaluation_set_id = %s
            GROUP BY pkl_id, path_a_index, path_b_index
        ) consistency_stats
        """
        
        consistency_result = execute_query(consistency_query, (evaluation_set_id,), fetch_one=True)
        
        consistent_pairs = 0
        inconsistent_pairs = 0
        if consistency_result["success"] and consistency_result["data"]:
            consistent_pairs = consistency_result["data"][0] or 0
            inconsistent_pairs = consistency_result["data"][1] or 0
        
        # 构建返回数据
        overview_statistics = {
            "total_pkls": total_pkls,
            "annotated_pkls": annotated_pkls,  # 有任何标注的PKL数量
            "completed_pkls": completed_pkls,  # 完全完成标注的PKL数量
            "pending_pkls": total_pkls - annotated_pkls,  # 完全未开始标注的PKL数量
            "total_pairs": total_pairs,
            "annotated_pairs": annotated_pairs,
            "consistent_pairs": consistent_pairs,
            "inconsistent_pairs": inconsistent_pairs,
            "pending_pairs": total_pairs - annotated_pairs
        }
        
        return {
            "success": True,
            "overview_statistics": overview_statistics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取总体统计失败: {str(e)}")

@router.get("/path-pair-statistics/{evaluation_set_id}")
async def get_path_pair_statistics(
    evaluation_set_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_field: str = Query("pkl_name", description="排序字段"),
    sort_order: str = Query("asc", description="排序方向: asc/desc")
):
    """获取路径pair标注统计（分页版本）- 不包含总体统计"""
    # 计算偏移量
    offset = (page - 1) * page_size
    
    # 构建排序条件
    valid_sort_fields = ['pkl_name', 'annotated_pairs', 'annotator_count', 'total_pairs']
    if sort_field not in valid_sort_fields:
        sort_field = 'pkl_name'
    
    sort_direction = 'DESC' if sort_order.lower() == 'desc' else 'ASC'
    
    # 获取总数
    count_query = """
    SELECT COUNT(DISTINCT ecp.id)
    FROM evaluation_case_pool ecp
    JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
    WHERE escp.evaluation_set_id = %s AND ecp.dirty_data = FALSE
    """
    
    count_result = execute_query(count_query, (evaluation_set_id,), fetch_one=True)
    total_count = count_result["data"][0] if count_result["success"] and count_result["data"] else 0
    
    # 获取当前页的PKL列表
    pkl_list_query = f"""
    SELECT DISTINCT ecp.id as pkl_id, ecp.pkl_name
    FROM evaluation_case_pool ecp
    JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
    WHERE escp.evaluation_set_id = %s AND ecp.dirty_data = FALSE
    ORDER BY ecp.{sort_field} {sort_direction}
    LIMIT %s OFFSET %s
    """
    
    pkl_list_result = execute_query(pkl_list_query, (evaluation_set_id, page_size, offset), fetch_all=True)
    
    if not pkl_list_result["success"] or not pkl_list_result["data"]:
        return {
            "success": True,
            "pkl_statistics": [],
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": 0
            }
        }
    
    # 提取当前页的PKL ID列表
    current_page_pkl_ids = [row[0] for row in pkl_list_result["data"]]
    pkl_id_placeholders = ','.join(['%s'] * len(current_page_pkl_ids))
    
    # 批量获取当前页PKL的标注统计
    batch_stats_query = f"""
    SELECT 
        ecp.id as pkl_id,
        ecp.pkl_name,
        COUNT(DISTINCT CONCAT(ppa.path_a_index, '-', ppa.path_b_index)) as annotated_pairs,
        COUNT(DISTINCT ppa.annotator_id) as annotator_count
    FROM evaluation_case_pool ecp
    LEFT JOIN path_pair_annotation ppa ON ecp.id = ppa.pkl_id AND ppa.evaluation_set_id = %s
    WHERE ecp.id IN ({pkl_id_placeholders})
    GROUP BY ecp.id, ecp.pkl_name
    ORDER BY ecp.{sort_field} {sort_direction}
    """
    
    batch_stats_params = [evaluation_set_id] + current_page_pkl_ids
    batch_stats_result = execute_query(batch_stats_query, batch_stats_params, fetch_all=True)
    
    # 批量获取path pair定义数量
    pair_count_query = f"""
    SELECT pkl_id, COUNT(*) as total_pairs
    FROM path_pair_definition
    WHERE pkl_id IN ({pkl_id_placeholders}) AND evaluation_set_id = %s
    GROUP BY pkl_id
    """
    
    pair_count_params = current_page_pkl_ids + [evaluation_set_id]
    pair_count_result = execute_query(pair_count_query, pair_count_params, fetch_all=True)
    
    # 创建pair计数映射
    pair_count_map = {}
    if pair_count_result["success"] and pair_count_result["data"]:
        for row in pair_count_result["data"]:
            pair_count_map[row[0]] = row[1]
    # 批量获取一致性统计
    consistency_query = f"""
    SELECT 
        pkl_id,
        path_a_index,
        path_b_index,
        COUNT(DISTINCT comparison_result) as unique_results,
        COUNT(*) as total_annotations
    FROM path_pair_annotation 
    WHERE pkl_id IN ({pkl_id_placeholders}) AND evaluation_set_id = %s
    GROUP BY pkl_id, path_a_index, path_b_index
    """
    
    consistency_params = current_page_pkl_ids + [evaluation_set_id]
    consistency_result = execute_query(consistency_query, consistency_params, fetch_all=True)
    
    # 处理一致性数据
    consistency_dict = {}
    if consistency_result["success"] and consistency_result["data"]:
        for row in consistency_result["data"]:
            pkl_id, path_a, path_b, unique_results, total_annotations = row
            pair_key = f"{min(path_a, path_b)}-{max(path_a, path_b)}"
            key = f"{pkl_id}-{pair_key}"
            consistency_dict[key] = {
                'unique_results': unique_results,
                'total_annotations': total_annotations
            }
    
    # 批量获取详细标注数据（仅当前页）
    detailed_annotations_query = f"""
    SELECT 
        pkl_id,
        path_a_index,
        path_b_index,
        comparison_result,
        annotator_id
    FROM path_pair_annotation 
    WHERE pkl_id IN ({pkl_id_placeholders}) AND evaluation_set_id = %s
    ORDER BY pkl_id, annotator_id, path_a_index, path_b_index
    """
    
    detailed_annotations_params = current_page_pkl_ids + [evaluation_set_id]
    detailed_result = execute_query(detailed_annotations_query, detailed_annotations_params, fetch_all=True)
    
    # 处理详细标注数据
    pkl_annotations = {}
    if detailed_result["success"] and detailed_result["data"]:
        for row in detailed_result["data"]:
            pkl_id, path_a, path_b, result, annotator = row
            
            if pkl_id not in pkl_annotations:
                pkl_annotations[pkl_id] = {}
            
            if annotator not in pkl_annotations[pkl_id]:
                pkl_annotations[pkl_id][annotator] = {}
            
            pair_key = f"{min(path_a, path_b)}-{max(path_a, path_b)}"
            pkl_annotations[pkl_id][annotator][pair_key] = result
    
    # 批量获取path pair定义（用于构建映射）
    pair_definition_query = f"""
    SELECT pkl_id, pair_index, path_a_index, path_b_index
    FROM path_pair_definition
    WHERE pkl_id IN ({pkl_id_placeholders}) AND evaluation_set_id = %s
    ORDER BY pkl_id, pair_index
    """
    
    pair_definition_params = current_page_pkl_ids + [evaluation_set_id]
    pair_definition_result = execute_query(pair_definition_query, pair_definition_params, fetch_all=True)
    
    # 构建pair定义映射
    pkl_pair_definitions = {}
    if pair_definition_result["success"] and pair_definition_result["data"]:
        for row in pair_definition_result["data"]:
            pkl_id, pair_index, path_a_index, path_b_index = row
            if pkl_id not in pkl_pair_definitions:
                pkl_pair_definitions[pkl_id] = []
            pkl_pair_definitions[pkl_id].append({
                'pair_id': pair_index,
                'path_a_index': path_a_index,
                'path_b_index': path_b_index
            })
    
    # 构建当前页的统计数据
    pkl_statistics = []
    if batch_stats_result["success"] and batch_stats_result["data"]:
        for row in batch_stats_result["data"]:
            pkl_id, pkl_name, annotated_pairs, annotator_count = row
            
            # 获取总pairs数量
            total_pairs_for_pkl = pair_count_map.get(pkl_id, 0)
            
            # 计算一致性
            consistent_pairs = 0
            inconsistent_pairs = 0
            
            for key, cons_data in consistency_dict.items():
                if key.startswith(f"{pkl_id}-"):
                    if cons_data['unique_results'] == 1:
                        consistent_pairs += 1
                    else:
                        inconsistent_pairs += 1
            
            # 生成标注员进度数据
            annotator_progress = []
            if pkl_id in pkl_annotations:
                actual_pairs = pkl_pair_definitions.get(pkl_id, [])
                
                for annotator_id, annotations in pkl_annotations[pkl_id].items():
                    # 构建pair映射
                    pair_mapping = {}
                    for pair_def in actual_pairs:
                        pair_key = f"{min(pair_def['path_a_index'], pair_def['path_b_index'])}-{max(pair_def['path_a_index'], pair_def['path_b_index'])}"
                        pair_mapping[pair_key] = pair_def['pair_id']
                    
                    # 计算已标注的pair索引
                    annotated_pair_indices = []
                    annotation_dict = {}
                    
                    for pair_key, result in annotations.items():
                        if pair_key in pair_mapping:
                            pair_idx = pair_mapping[pair_key]
                            annotated_pair_indices.append(pair_idx)
                            annotation_dict[str(pair_idx)] = result
                    
                    annotator_progress.append({
                        "annotator_id": annotator_id,
                        "annotated_pairs": annotated_pair_indices,
                        "annotations": annotation_dict
                    })
            
            pkl_statistics.append({
                "pkl_id": pkl_id,
                "pkl_name": pkl_name,
                "total_pairs": total_pairs_for_pkl,
                "annotated_pairs": annotated_pairs,
                "annotator_count": annotator_count,
                "consistent_pairs": consistent_pairs,
                "inconsistent_pairs": inconsistent_pairs,
                "annotator_progress": annotator_progress
            })
    
    return {
        "success": True,
        "pkl_statistics": pkl_statistics,
        "pagination": {
            "current_page": page,
            "page_size": page_size,
            "total_count": total_count,
            "total_pages": (total_count + page_size - 1) // page_size
        }
    }
    
def save_path_pairs_to_db(pkl_id: int, evaluation_set_id: int, path_pairs: List[Dict]):
    """将生成的路径对保存到数据库"""
    # 先删除该PKL在该评测集的旧记录
    delete_query = "DELETE FROM path_pair_definition WHERE pkl_id = %s AND evaluation_set_id = %s"
    execute_query(delete_query, (pkl_id, evaluation_set_id))
    
    # 插入新的路径对定义
    insert_query = """
    INSERT INTO path_pair_definition (pkl_id, evaluation_set_id, pair_index, path_a_index, path_b_index)
    VALUES (%s, %s, %s, %s, %s)
    """
    
    for pair in path_pairs:
        params = (
            pkl_id,
            evaluation_set_id,
            pair['pair_id'],
            pair['path_a']['index'],
            pair['path_b']['index']
        )
        execute_query(insert_query, params)

def get_path_pairs_from_db(pkl_id: int, evaluation_set_id: int):
    """从数据库获取路径对定义"""
    query = """
    SELECT pair_index, path_a_index, path_b_index
    FROM path_pair_definition
    WHERE pkl_id = %s AND evaluation_set_id = %s
    ORDER BY pair_index
    """
    result = execute_query(query, (pkl_id, evaluation_set_id), fetch_all=True)
    
    if result["success"] and result["data"]:
        return [
            {
                'pair_id': row[0],
                'path_a_index': row[1],
                'path_b_index': row[2]
            }
            for row in result["data"]
        ]
    return []

@router.get("/export-pair-annotations/{evaluation_set_id}")
async def export_annotations_get(
    evaluation_set_id: int = Path(..., description="评测集ID"),
    include_conflicts: bool = Query(True, description="是否包含冲突详情"),
):
    """导出路径pair标注结果 - GET接口，直接返回JSON"""
    try:
        # 首先获取评测集名称
        evaluation_set_query = """
        SELECT set_name FROM evaluation_set WHERE id = %s
        """
        set_result = execute_query(evaluation_set_query, (evaluation_set_id,), fetch_one=True)
        
        if not set_result["success"] or not set_result["data"]:
            raise HTTPException(status_code=404, detail="评测集不存在")
        
        set_name = set_result["data"][0]
        
        # 获取所有标注数据
        annotations_query = """
        SELECT 
            ecp.id as pkl_id,
            ecp.pkl_dir,
            ecp.pkl_name,
            ppa.path_a_index,
            ppa.path_b_index,
            ppa.comparison_result,
            ppa.annotator_id,
            ppa.created_at
        FROM path_pair_annotation ppa
        JOIN evaluation_case_pool ecp ON ppa.pkl_id = ecp.id
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
        WHERE ppa.evaluation_set_id = %s AND escp.evaluation_set_id = %s
        ORDER BY ecp.pkl_name, ppa.path_a_index, ppa.path_b_index, ppa.annotator_id
        """
        
        result = execute_query(annotations_query, (evaluation_set_id, evaluation_set_id), fetch_all=True)
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail="查询标注数据失败")
        
        # 处理数据
        pkl_annotations = {}
        for row in result["data"]:
            pkl_id, pkl_dir, pkl_name, path_a, path_b, comparison_result, annotator_id, created_at = row
            
            pkl_key = f"{pkl_id}_{pkl_name}"
            if pkl_key not in pkl_annotations:
                pkl_annotations[pkl_key] = {
                    "pkl_id": pkl_id,
                    "pkl_name": pkl_name,
                    "pkl_path": os.path.join(pkl_dir, pkl_name),
                    "pairs": {}
                }
            
            pair_key = f"{min(path_a, path_b)}-{max(path_a, path_b)}"
            if pair_key not in pkl_annotations[pkl_key]["pairs"]:
                pkl_annotations[pkl_key]["pairs"][pair_key] = {
                    "path_a_index": min(path_a, path_b),
                    "path_b_index": max(path_a, path_b),
                    "annotations": []
                }
            
            pkl_annotations[pkl_key]["pairs"][pair_key]["annotations"].append({
                "annotator_id": annotator_id,
                "comparison_result": comparison_result,
                "created_at": created_at.isoformat() if created_at else None
            })
        
        # 合并标注结果
        export_data = []
        for pkl_key, pkl_data in pkl_annotations.items():
            for pair_key, pair_data in pkl_data["pairs"].items():
                annotations = pair_data["annotations"]
                
                # 统计标注结果
                result_counts = {}
                for ann in annotations:
                    result = ann["comparison_result"]
                    if result not in result_counts:
                        result_counts[result] = 0
                    result_counts[result] += 1
                
                # 判断是否有冲突
                has_conflict = len(result_counts) > 1
                
                # 获取最终结果（多数决定）
                final_result = max(result_counts.items(), key=lambda x: x[1])[0] if result_counts else None
                
                pair_export = {
                    "pkl_id": pkl_data["pkl_id"],
                    "pkl_name": pkl_data["pkl_name"],
                    "pkl_path": pkl_data["pkl_path"],
                    "path_a_index": pair_data["path_a_index"],
                    "path_b_index": pair_data["path_b_index"],
                    "final_result": final_result,
                    "has_conflict": has_conflict,
                    "annotation_count": len(annotations),
                    "result_distribution": result_counts
                }
                
                # 如果有冲突且用户选择包含冲突信息
                if has_conflict and include_conflicts:
                    pair_export["detailed_annotations"] = [
                        {
                            "annotator_id": ann["annotator_id"],
                            "comparison_result": ann["comparison_result"],
                            "created_at": ann["created_at"]
                        }
                        for ann in annotations
                    ]
                
                export_data.append(pair_export)
        
        # 生成导出时间戳
        export_timestamp = datetime.now().isoformat()
        
        return {
            "success": True,
            "export_info": {
                "evaluation_set_id": evaluation_set_id,
                "evaluation_set_name": set_name,
                "export_timestamp": export_timestamp,
                "include_conflicts": include_conflicts,
                "total_pairs": len(export_data)
            },
            "annotations": export_data
        }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


def export_json(export_data: List[Dict], set_name: str) -> StreamingResponse:
    """导出JSON格式"""
    json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
    
    def generate():
        yield json_str.encode('utf-8')
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"path_pair_annotations_{set_name}_{timestamp}.json"
    
    # 使用 URL 编码处理中文文件名
    from urllib.parse import quote
    encoded_filename = quote(filename.encode('utf-8'))
    
    headers = {
        'Content-Disposition': f'attachment; filename*=UTF-8\'\'{encoded_filename}'
    }
    
    return StreamingResponse(
        generate(),
        media_type='application/json',
        headers=headers
    )

def export_csv(export_data: List[Dict], include_conflicts: bool, set_name: str) -> StreamingResponse:
    """导出CSV格式"""
    
    output = io.StringIO()
    
    if include_conflicts:
        # 包含冲突详情的CSV
        fieldnames = [
            'pkl_id', 'pkl_name', 'pkl_path', 'path_a_index', 'path_b_index',
            'final_result', 'has_conflict', 'annotation_count',
            'A_better_count', 'B_better_count', 'indistinguishable_count',
            'conflict_details'
        ]
        
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        
        for item in export_data:
            row = {
                'pkl_id': item['pkl_id'],
                'pkl_name': item['pkl_name'],
                'pkl_path': item['pkl_path'],
                'path_a_index': item['path_a_index'],
                'path_b_index': item['path_b_index'],
                'final_result': item['final_result'],
                'has_conflict': item['has_conflict'],
                'annotation_count': item['annotation_count'],
                'A_better_count': item['result_distribution'].get('A_better', 0),
                'B_better_count': item['result_distribution'].get('B_better', 0),
                'indistinguishable_count': item['result_distribution'].get('indistinguishable', 0),
                'conflict_details': ''
            }
            
            if item['has_conflict'] and 'detailed_annotations' in item:
                conflict_details = []
                for ann in item['detailed_annotations']:
                    conflict_details.append(f"{ann['annotator_id']}:{ann['comparison_result']}")
                row['conflict_details'] = '; '.join(conflict_details)
            
            writer.writerow(row)
    else:
        # 简化版CSV
        fieldnames = [
            'pkl_id', 'pkl_name', 'pkl_path', 'path_a_index', 'path_b_index',
            'final_result', 'has_conflict', 'annotation_count'
        ]
        
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        
        for item in export_data:
            writer.writerow({
                'pkl_id': item['pkl_id'],
                'pkl_name': item['pkl_name'],
                'pkl_path': item['pkl_path'],
                'path_a_index': item['path_a_index'],
                'path_b_index': item['path_b_index'],
                'final_result': item['final_result'],
                'has_conflict': item['has_conflict'],
                'annotation_count': item['annotation_count']
            })
    
    output.seek(0)
    
    def generate():
        for line in output:
            yield line.encode('utf-8')
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"path_pair_annotations_{set_name}_{timestamp}.csv"
    
    # 使用 URL 编码处理中文文件名
    from urllib.parse import quote
    encoded_filename = quote(filename.encode('utf-8'))
    
    headers = {
        'Content-Disposition': f'attachment; filename*=UTF-8\'\'{encoded_filename}'
    }
    
    return StreamingResponse(
        generate(),
        media_type='text/csv',
        headers=headers
    )
    
class CombinedPklListRequest(BaseModel):
    evaluation_set_ids: List[int]
    page: int = 1
    per_page: int = 20
    dirty_filter: bool = True

@router.post("/combined-pkl-list")
async def get_combined_pkl_list(request: CombinedPklListRequest):
    """获取多个评测集组合后的PKL列表"""
    try:
        if not request.evaluation_set_ids:
            raise HTTPException(status_code=400, detail="至少需要提供一个评测集ID")
        
        # 构建查询条件
        placeholders = ','.join(['%s'] * len(request.evaluation_set_ids))
        
        # 构建基础查询条件
        where_conditions = [f"escp.evaluation_set_id IN ({placeholders})"]
        params = list(request.evaluation_set_ids)
        
        # 添加脏数据过滤
        if request.dirty_filter:
            where_conditions.append("ecp.dirty_data = FALSE")
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询总数
        count_query = f"""
        SELECT COUNT(*)
        FROM evaluation_case_pool ecp 
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id 
        JOIN evaluation_set es ON escp.evaluation_set_id = es.id
        WHERE {where_clause}
        """
        
        count_result = execute_query(count_query, params, fetch_one=True)
        total_count = count_result["data"][0] if count_result["success"] else 0
        
        # 查询PKL列表（分页）
        offset = (request.page - 1) * request.per_page
        cases_query = f"""
        SELECT 
            ecp.id, 
            ecp.pkl_name, 
            ecp.pkl_dir, 
            ecp.vehicle_type, 
            ecp.vin, 
            ecp.time_ns, 
            ecp.key_obs_id, 
            ecp.dirty_data,
            escp.evaluation_set_id,
            es.set_name as evaluation_set_name
        FROM evaluation_case_pool ecp 
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id 
        JOIN evaluation_set es ON escp.evaluation_set_id = es.id
        WHERE {where_clause}
        ORDER BY es.set_name, ecp.pkl_name
        LIMIT %s OFFSET %s
        """
        
        cases_params = params + [request.per_page, offset]
        cases_result = execute_query(cases_query, cases_params, fetch_all=True)
        
        combined_cases = []
        if cases_result["success"] and cases_result["data"]:
            for row in cases_result["data"]:
                combined_cases.append({
                    "id": row[0],
                    "pkl_name": row[1],
                    "pkl_dir": row[2],
                    "vehicle_type": row[3],
                    "vin": row[4],
                    "time_ns": row[5],
                    "key_obs_id": row[6],
                    "dirty_data": bool(row[7]),
                    "evaluation_set_id": row[8],
                    "evaluation_set_name": row[9]
                })
        
        return {
            "success": True,
            "combined_cases": combined_cases,
            "total_count": total_count,
            "page": request.page,
            "per_page": request.per_page,
            "evaluation_set_count": len(request.evaluation_set_ids)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取组合PKL列表失败: {str(e)}")