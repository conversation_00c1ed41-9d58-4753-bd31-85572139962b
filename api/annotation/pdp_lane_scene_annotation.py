from fastapi import APIRouter, HTTPException, Body, Path, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime, date
import os
from database.db_operations import execute_query
from api.visualize.pickle_visualize import load_pickle, extract_geometry_from_pickle
from api.auth.auth import get_current_user
import cv2
import numpy as np
import base64
from api.visualize.calib_reader import CalibR<PERSON><PERSON>, CalibReaderDict
from api.visualize.draw import draw_lane_turn_sign
import matplotlib.pyplot as plt
import json

calib_reader_dict = CalibReaderDict()

router = APIRouter(prefix="/api/annotation/lane-scene", tags=["lane_scene_annotation"])


class LaneSceneAnnotation(BaseModel):
    pkl_id: int
    vrulane_annotation: Optional[str] = None  # "good", "bad", "ignore"
    frontlane_annotation: Optional[str] = None  # "good", "bad", "ignore"
    evaluation_set_id: Optional[int] = None
    delete_annotation: Optional[bool] = False
    annotation_type: Optional[str] = (
        None  # "vrulane" or "frontlane" for partial deletion
    )


class LaneSceneSelectedCenterlines(BaseModel):
    pkl_id: int
    selected_centerline_ids: List[str]
    delete_annotation: Optional[bool] = False
    bag_id: Optional[int] = None


class LaneSceneTags(BaseModel):
    pkl_id: int
    tags: Optional[List[str]] = None
    delete_annotation: Optional[bool] = False
    evaluation_set_id: Optional[int] = None
    bag_id: Optional[int] = None


def get_lane_scene_annotation(pkl_id, evaluation_set_id=None):
    """获取指定PKL的车道场景标注"""
    if evaluation_set_id:
        query = """
        SELECT vrulane_annotation, frontlane_annotation, employee_id, created_at, updated_at
        FROM pdp_lane_scene_annotation 
        WHERE pkl_id = %s AND evaluation_set_id = %s
        """
        params = (pkl_id, evaluation_set_id)
    else:
        query = """
        SELECT vrulane_annotation, frontlane_annotation, employee_id, created_at, updated_at
        FROM pdp_lane_scene_annotation 
        WHERE pkl_id = %s
        """
        params = (pkl_id,)

    result = execute_query(query, params, fetch_one=True, return_dict=True)
    return result


def save_lane_scene_annotation(
    pkl_id,
    vrulane_annotation=None,
    frontlane_annotation=None,
    evaluation_set_id=None,
    employee_id=None,
):
    """保存车道场景标注"""
    # 首先检查是否已存在记录
    existing = get_lane_scene_annotation(pkl_id, evaluation_set_id)
    # print('...existing', existing)
    if existing["success"] and existing["data"]:
        # 更新现有记录
        update_fields = []
        params = []

        if vrulane_annotation is not None:
            update_fields.append("vrulane_annotation = %s")
            params.append(vrulane_annotation)

        if frontlane_annotation is not None:
            update_fields.append("frontlane_annotation = %s")
            params.append(frontlane_annotation)

        if employee_id is not None:
            update_fields.append("employee_id = %s")
            params.append(employee_id)

        update_fields.append("updated_at = CURRENT_TIMESTAMP")

        query = f"""
        UPDATE pdp_lane_scene_annotation 
        SET {", ".join(update_fields)}
        WHERE pkl_id = %s AND evaluation_set_id = %s
        """
        params.extend([pkl_id, evaluation_set_id])
    else:
        # 插入新记录
        query = """
        INSERT INTO pdp_lane_scene_annotation 
        (pkl_id, vrulane_annotation, frontlane_annotation, evaluation_set_id, employee_id, created_at, updated_at)
        VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        """
        if vrulane_annotation is None:
            vrulane_annotation = "ignore"
        if frontlane_annotation is None:
            frontlane_annotation = "ignore"
        params = (
            pkl_id,
            vrulane_annotation,
            frontlane_annotation,
            evaluation_set_id,
            employee_id,
        )

    return execute_query(query, params)


def delete_lane_scene_annotation(pkl_id, evaluation_set_id=None, annotation_type=None):
    """删除车道场景标注"""
    if annotation_type and annotation_type in ["vrulane", "frontlane"]:
        # 删除特定类型的标注
        field = f"{annotation_type}_annotation"
        query = f"""
        UPDATE pdp_lane_scene_annotation 
        SET {field} = NULL, updated_at = CURRENT_TIMESTAMP
        WHERE pkl_id = %s AND evaluation_set_id = %s
        """
        params = (pkl_id, evaluation_set_id)
    else:
        # 删除整个记录
        query = """
        DELETE FROM pdp_lane_scene_annotation 
        WHERE pkl_id = %s AND evaluation_set_id = %s
        """
        params = (pkl_id, evaluation_set_id)

    return execute_query(query, params)


@router.get("/pkl/{pkl_id}")
async def get_lane_scene_data(
    pkl_id: int, evaluation_set_id: Optional[int] = Query(None, description="评测集ID")
):
    """获取指定PKL文件的车道场景数据和现有标注"""
    # 查询pkl文件路径
    query = """
    SELECT pkl_dir, pkl_name, dirty_data, vin FROM evaluation_case_pool WHERE id = %s
    """
    result = execute_query(query, (pkl_id,), fetch_one=True)

    if not result["success"] or not result["data"]:
        raise HTTPException(status_code=404, detail="找不到指定的PKL文件")

    pkl_dir, pkl_name, is_dirty, vin = result["data"]
    pickle_path = os.path.join(pkl_dir, pkl_name)

    # 检查文件是否存在
    if not os.path.exists(pickle_path):
        raise HTTPException(status_code=404, detail="PKL文件不存在")

    try:
        # 加载pickle文件
        data = load_pickle(pickle_path)

        # 处理e2e_image图像
        image_data = None
        # image = data.get('e2e_image', None)
        ego_yaw = -1.0 * data.get("feature_obj", None)[0, 0, 10]
        ego_path = data.get("path_point", None)
        ego_path_mask = data.get("path_mask", None)
        ego_path = ego_path * ego_path_mask[:, None]
        fig, ax = plt.subplots(1, 1, figsize=(5, 5))

        lane_turn_sign = data.get("lane_turn_sign", None)
        lane_turn_sign_mask = data.get("lane_turn_sign_mask", None)
        has_lane_sign = False
        if lane_turn_sign is not None:
            draw_lane_turn_sign(
                sp=ax,
                lane_turn_sign=lane_turn_sign,
                lane_turn_sign_mask=lane_turn_sign_mask,
            )
            has_lane_sign = True
        # print(ego_path)
        # print('....length of ego_path:', ego_path.shape if ego_path is not None else 'None')
        if ego_path is not None:
            ego_path = (
                ego_path.tolist() if isinstance(ego_path, np.ndarray) else ego_path
            )
        # if image:
        #     np_image = image.get('image', None)
        #     if np_image is not None and isinstance(np_image, np.ndarray):
        #         try:
        #             if len(np_image.shape) == 3 and np_image.shape[0] == 3:
        #                 np_image = np.transpose(np_image, (1, 2, 0))

        #             if np_image.max() <= 1.0:
        #                 np_image = (np_image * 255).astype(np.uint8)
        #             np_image = cv2.cvtColor(np_image, cv2.COLOR_YUV2BGR)

        #             _, buffer = cv2.imencode('.jpg', np_image)
        #             image_data = base64.b64encode(buffer).decode('utf-8')
        #         except Exception as e:
        #             print(f"处理图像出错: {str(e)}")
        if has_lane_sign:
            try:
                # 保存图形到内存缓冲区
                import io

                buffer = io.BytesIO()
                fig.savefig(buffer, format="png", bbox_inches="tight", dpi=100)
                buffer.seek(0)

                # 转换为base64编码
                lane_sign_plot_data = base64.b64encode(buffer.getvalue()).decode(
                    "utf-8"
                )

                # 清理资源
                buffer.close()
                plt.close(fig)

            except Exception as e:
                print(f"处理车道标志绘图出错: {str(e)}")
                plt.close(fig)
        # 获取现有标注
        annotation_result = get_lane_scene_annotation(pkl_id, evaluation_set_id)
        current_annotation = None
        if annotation_result["success"] and annotation_result["data"]:
            current_annotation = annotation_result["data"]

        # 获取ego2img矩阵
        ego2img_array = calib_reader_dict.get_calib(
            vin=vin, scale=[0.2666, 0.2666], offset_x=0, offset_y=160 * 2160 / 576
        )["front_wide"]["ego2img"]

        return {
            "success": True,
            "pkl_info": {
                "id": pkl_id,
                "pkl_name": pkl_name,
                "pkl_dir": pkl_dir,
                "vin": vin,
                "is_dirty": is_dirty,
            },
            "current_annotation": current_annotation,
            "image_data": lane_sign_plot_data,
            "ego_yaw": ego_yaw,
            "ego_path_data": ego_path,
            "ego2img": ego2img_array.tolist() if ego2img_array is not None else None,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理PKL文件时出错: {str(e)}")


@router.post("/annotate")
async def annotate_lane_scene(
    annotation: LaneSceneAnnotation,
    current_user: Optional[dict] = Depends(get_current_user),
):
    # print('...in api')
    """保存或删除车道场景标注"""
    # 验证标注值
    valid_annotations = ["good", "bad", "ignore"]

    if (
        annotation.vrulane_annotation
        and annotation.vrulane_annotation not in valid_annotations
    ):
        raise HTTPException(
            status_code=400, detail="非机动车道标注值必须是'good'、'bad'或'ignore'"
        )

    if (
        annotation.frontlane_annotation
        and annotation.frontlane_annotation not in valid_annotations
    ):
        raise HTTPException(
            status_code=400, detail="推荐车道标注值必须是'good'、'bad'或'ignore'"
        )

    # 检查pkl_id是否存在
    check_query = "SELECT id FROM evaluation_case_pool WHERE id = %s"
    check_result = execute_query(check_query, (annotation.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    # 如果是删除操作
    if annotation.delete_annotation:
        result = delete_lane_scene_annotation(
            annotation.pkl_id, annotation.evaluation_set_id, annotation.annotation_type
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"删除标注时出错: {result['error']}"
            )

        message_text = "标注已删除"
        if annotation.annotation_type:
            message_text = f"{'非机动车道' if annotation.annotation_type == 'vrulane' else '推荐车道'}标注已删除"

        return {"success": True, "message": message_text}
    else:
        # 保存标注
        employee_id = current_user.get("employee_id", "") if current_user else None
        # print('...',annotation.pkl_id, annotation.vrulane_annotation, annotation.frontlane_annotation, annotation.evaluation_set_id, employee_id)
        result = save_lane_scene_annotation(
            pkl_id=annotation.pkl_id,
            vrulane_annotation=annotation.vrulane_annotation,
            frontlane_annotation=annotation.frontlane_annotation,
            evaluation_set_id=annotation.evaluation_set_id,
            employee_id=employee_id,
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"保存标注时出错: {result['error']}"
            )

        return {"success": True, "message": "标注保存成功"}


@router.post("/selected-centerlines")
async def save_pdp_selected_centerlines(
    request: LaneSceneSelectedCenterlines,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """保存 PKL 文件的选中centerlines列表"""
    # 检查pkl_id是否存在
    check_query = "SELECT id FROM bag_pkl_relation WHERE id = %s"
    check_result = execute_query(check_query, (request.pkl_id,), fetch_one=True)
    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    # 如果选中的centerlines为空，则删除记录
    if not request.selected_centerline_ids:
        delete_query = "DELETE FROM lane_scene_centerlines WHERE pkl_id = %s"
        result = execute_query(delete_query, (request.pkl_id,))
        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"删除空选中centerlines记录时出错: {result['error']}",
            )
        return {"success": True, "message": "空选中centerlines记录已删除"}

    if current_user:
        query = """
        INSERT INTO lane_scene_centerlines
        (pkl_id, selected_centerline_ids, bag_id, employee_id, updated_at)
        VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        selected_centerline_ids = VALUES(selected_centerline_ids),
        updated_at = CURRENT_TIMESTAMP
        """

        params = (
            request.pkl_id,
            json.dumps(request.selected_centerline_ids),
            request.bag_id if request.bag_id is not None else None,
            current_user.get("employee_id", ""),
        )
    else:
        query = """
        INSERT INTO lane_scene_centerlines
        (pkl_id, selected_centerline_ids, bag_id, updated_at)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP)
        ON DUPLICATE KEY UPDATE
        selected_centerline_ids = VALUES(selected_centerline_ids),
        updated_at = CURRENT_TIMESTAMP
        """

        params = (
            request.pkl_id,
            json.dumps(request.selected_centerline_ids),
            request.bag_id if request.bag_id is not None else None,
        )

    result = execute_query(query, params)

    if not result["success"]:
        raise HTTPException(
            status_code=500, detail=f"保存选中centerlines时出错: {result['error']}"
        )

    return {"success": True, "message": "选中centerlines保存成功"}

# 添加场景标签保存API
@router.post("/scene-tags")
async def save_lane_scene_tags(
    request: LaneSceneTags,
    current_user: Optional[dict] = Depends(get_current_user),
):
    """保存或删除场景标签"""
    
    # 检查pkl_id是否存在
        # bag集模式
    check_query = "SELECT id FROM bag_pkl_relation WHERE id = %s"

        
    check_result = execute_query(check_query, (request.pkl_id,), fetch_one=True)

    if not check_result["success"] or not check_result["data"]:
        raise HTTPException(status_code=404, detail="指定的PKL ID不存在")

    try:
        if request.delete_annotation or request.tags is None:
            # 删除标签
            if request.bag_id:
                delete_query = "DELETE FROM lane_scene_tags WHERE pkl_id = %s AND bag_id = %s"
                params = (request.pkl_id, request.bag_id)
            else:
                delete_query = "DELETE FROM lane_scene_tags WHERE pkl_id = %s"
                params = (request.pkl_id,)
                
            result = execute_query(delete_query, params)
            
            if not result["success"]:
                raise HTTPException(
                    status_code=500, detail=f"删除标签时出错: {result['error']}"
                )
            
            return {"success": True, "message": "场景标签已删除"}
        else:
            # 保存标签
            employee_id = current_user.get("employee_id", "") if current_user else None
            
            if request.bag_id:
                # bag集模式
                upsert_query = """
                INSERT INTO lane_scene_tags 
                (pkl_id, tags, bag_id, employee_id, created_at, updated_at)
                VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON DUPLICATE KEY UPDATE
                tags = VALUES(tags),
                employee_id = VALUES(employee_id),
                updated_at = CURRENT_TIMESTAMP
                """
                params = (request.pkl_id, json.dumps(request.tags), request.bag_id, employee_id)
            else:
                # 评测集模式
                upsert_query = """
                INSERT INTO lane_scene_tags 
                (pkl_id, tags, employee_id, created_at, updated_at)
                VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON DUPLICATE KEY UPDATE
                tags = VALUES(tags),
                employee_id = VALUES(employee_id),
                updated_at = CURRENT_TIMESTAMP
                """
                params = (request.pkl_id, json.dumps(request.tags), employee_id)
            
            result = execute_query(upsert_query, params)
            
            if not result["success"]:
                raise HTTPException(
                    status_code=500, detail=f"保存标签时出错: {result['error']}"
                )
            
            return {"success": True, "message": "场景标签保存成功"}
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"操作场景标签时出错: {str(e)}")
@router.get("/export/{evaluation_set_id}")
async def export_lane_scene_annotations(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """导出指定评测集的车道场景标注结果"""
    try:
        # 检查评测集是否存在
        check_set_query = "SELECT id, set_name FROM evaluation_set WHERE id = %s"
        set_result = execute_query(
            check_set_query, (evaluation_set_id,), fetch_one=True
        )

        if not set_result["success"] or not set_result["data"]:
            raise HTTPException(
                status_code=404, detail=f"评测集ID {evaluation_set_id} 不存在"
            )

        set_name = set_result["data"][1]

        # 查询车道场景标注数据
        query = """
        SELECT 
            CONCAT(p.pkl_dir, '/', p.pkl_name) as pickle_path,
            p.vin,
            p.time_ns,
            a.vrulane_annotation,
            a.frontlane_annotation,
            a.employee_id,
            a.created_at,
            a.updated_at,
            COALESCE(escp.is_checked, FALSE) as is_checked,
            escp.checked_at,
            escp.checked_by
        FROM 
            pdp_lane_scene_annotation a
        JOIN 
            evaluation_case_pool p ON a.pkl_id = p.id
        LEFT JOIN 
            evaluation_set_case_pool escp ON a.pkl_id = escp.evaluation_case_id 
            AND escp.evaluation_set_id = %s
        WHERE 
            a.evaluation_set_id = %s
        ORDER BY 
            pickle_path
        """

        result = execute_query(
            query, (evaluation_set_id, evaluation_set_id), fetch_all=True
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据
        annotations = []
        for row in result["data"]:
            annotations.append(
                {
                    "pickle_path": row[0],
                    "vin": row[1],
                    "time_ns": row[2],
                    "vrulane_annotation": row[3],
                    "frontlane_annotation": row[4],
                    "employee_id": row[5],
                    "created_at": row[6].isoformat() if row[6] else None,
                    "updated_at": row[7].isoformat() if row[7] else None,
                    "is_checked": bool(row[8]),
                    "checked_at": row[9].isoformat() if row[9] else None,
                    "checked_by": row[10],
                }
            )

        return {
            "success": True,
            "evaluation_set_id": evaluation_set_id,
            "evaluation_set_name": set_name,
            "total_annotations": len(annotations),
            "export_time": datetime.now().isoformat(),
            "annotations": annotations,
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")


@router.get("/progress/{evaluation_set_id}")
async def get_lane_scene_annotation_progress(
    evaluation_set_id: int = Path(..., description="评测集ID"),
):
    """获取车道场景标注进度"""
    try:
        # 获取评测集中的总PKL数量
        total_query = """
        SELECT COUNT(DISTINCT escp.evaluation_case_id) as total_pkls
        FROM evaluation_set_case_pool escp
        WHERE escp.evaluation_set_id = %s
        """

        # 获取已标注的PKL数量（至少有一个维度被标注）
        annotated_query = """
        SELECT COUNT(DISTINCT a.pkl_id) as annotated_pkls
        FROM pdp_lane_scene_annotation a
        WHERE a.evaluation_set_id = %s 
        AND (a.vrulane_annotation IS NOT NULL OR a.frontlane_annotation IS NOT NULL)
        """

        # 获取完全标注的PKL数量（两个维度都被标注）
        fully_annotated_query = """
        SELECT COUNT(DISTINCT a.pkl_id) as fully_annotated_pkls
        FROM pdp_lane_scene_annotation a
        WHERE a.evaluation_set_id = %s 
        AND a.vrulane_annotation IS NOT NULL 
        AND a.frontlane_annotation IS NOT NULL
        """

        total_result = execute_query(total_query, (evaluation_set_id,), fetch_one=True)
        annotated_result = execute_query(
            annotated_query, (evaluation_set_id,), fetch_one=True
        )
        fully_annotated_result = execute_query(
            fully_annotated_query, (evaluation_set_id,), fetch_one=True
        )

        total_pkls = (
            total_result["data"][0]
            if total_result["success"] and total_result["data"]
            else 0
        )
        annotated_pkls = (
            annotated_result["data"][0]
            if annotated_result["success"] and annotated_result["data"]
            else 0
        )
        fully_annotated_pkls = (
            fully_annotated_result["data"][0]
            if fully_annotated_result["success"] and fully_annotated_result["data"]
            else 0
        )

        return {
            "success": True,
            "evaluation_set_id": evaluation_set_id,
            "total_pkls": total_pkls,
            "annotated_pkls": annotated_pkls,
            "fully_annotated_pkls": fully_annotated_pkls,
            "annotation_progress": round((annotated_pkls / total_pkls * 100), 2)
            if total_pkls > 0
            else 0,
            "full_annotation_progress": round(
                (fully_annotated_pkls / total_pkls * 100), 2
            )
            if total_pkls > 0
            else 0,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标注进度时出错: {str(e)}")
