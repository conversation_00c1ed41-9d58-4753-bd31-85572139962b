import types
from typing import List, Optional, Tuple, Dict, Any
from collections import defaultdict
from api.validity_check.entity import *
from database.db_operations import get_db_connection
import math


class ValidityCheckDAO:
    def __init__(self):
        pass

    def get_connection(self):
        return get_db_connection()

    # PKL 管理
    def get_users_by_role(self, role: Optional[str] = None) -> List[User]:
        """根据角色获取用户列表"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            if role:
                sql = "SELECT * FROM users WHERE role = %s AND is_active = TRUE ORDER BY username"
                cursor.execute(sql, (role,))
            else:
                sql = "SELECT * FROM users WHERE is_active = TRUE ORDER BY username"
                cursor.execute(sql)

            rows = cursor.fetchall()
            return [self._row_to_user(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT * FROM users WHERE id = %s"
            cursor.execute(sql, (user_id,))
            row = cursor.fetchone()
            if row:
                return self._row_to_user(row)
            return None
        finally:
            cursor.close()
            conn.close()

    def get_user_by_employee_id(self, employee_id: str) -> Optional[User]:
        """根据工号获取用户"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT * FROM users WHERE employee_id = %s"
            cursor.execute(sql, (employee_id,))
            row = cursor.fetchone()
            if row:
                return self._row_to_user(row)
            return None
        finally:
            cursor.close()
            conn.close()

    def _row_to_user(self, row: dict) -> User:
        """将数据库行转换为User对象"""
        return User(
            id=row['id'],
            username=row['username'],
            employee_id=row['employee_id'],
            password_hash=row['password_hash'],
            role=row['role'],
            is_active=row['is_active'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )

    def batch_create_pkls(self, pkls: List[ValidityCheckTaskPkl]) -> Tuple[List[int], Dict[int, Optional[int]]]:
        """批量创建PKL，返回PKL ID列表和每个ID对应的set_id映射"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            INSERT IGNORE INTO validity_check_task_pkls 
            (pkl_name, pkl_dir, bag_name, vehicle_type, date, mode, version, scene_tag, set_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            values = []
            pkl_keys = []  # 记录每个PKL的set_id

            for pkl in pkls:
                values.append((
                    pkl.pkl_name, pkl.pkl_dir, pkl.bag_name,
                    pkl.vehicle_type.value if pkl.vehicle_type else None,
                    pkl.date,
                    pkl.mode.value if pkl.mode else None,
                    pkl.version.value if pkl.version else None,
                    pkl.scene_tag,
                    pkl.set_id
                ))
                pkl_keys.append((pkl.pkl_name, pkl.pkl_dir, pkl.set_id))

            cursor.executemany(sql, values)
            conn.commit()

            # 通过查询获取实际插入的ID，使用(pkl_name, pkl_dir)的唯一性
            placeholders = ','.join(['(%s, %s)'] * len(pkl_keys))

            query_sql = f"""
            SELECT id, pkl_name, pkl_dir, set_id 
            FROM validity_check_task_pkls 
            WHERE (pkl_name, pkl_dir) IN ({placeholders})
            ORDER BY created_at DESC
            """

            # 准备查询参数
            query_params = []
            for pkl_name, pkl_dir, _ in pkl_keys:
                query_params.extend([pkl_name, pkl_dir])

            cursor.execute(query_sql, query_params)
            results = cursor.fetchall()

            # 按照原始顺序重新排列
            pkl_ids = []
            id_to_set_id = {}

            for pkl_name, pkl_dir, original_set_id in pkl_keys:
                for result in results:
                    if result['pkl_name'] == pkl_name and result['pkl_dir'] == pkl_dir:
                        pkl_ids.append(result['id'])
                        id_to_set_id[result['id']] = result['set_id']
                        break

            return pkl_ids, id_to_set_id
        finally:
            cursor.close()
            conn.close()

    def get_pkl_by_id(self, pkl_id: int) -> Optional[ValidityCheckTaskPkl]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT * FROM validity_check_task_pkls WHERE id = %s"
            cursor.execute(sql, (pkl_id,))
            row = cursor.fetchone()
            if row:
                return self._row_to_pkl(row)
            return None
        finally:
            cursor.close()
            conn.close()

    def get_or_create_task_by_set_id(self, set_id: Optional[int], creator_id: str) -> int:
        """根据set_id获取或创建任务，返回task_id"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            # 确定任务名称
            if set_id is None:
                task_name = "质检任务-未分组"
                search_condition = "set_id IS NULL"
                search_params = ()
            else:
                task_name = f"质检任务{set_id}"
                search_condition = "set_id = %s"
                search_params = (set_id,)

            # 查找现有任务
            sql = f"SELECT id FROM validity_check_tasks WHERE {search_condition}"
            cursor.execute(sql, search_params)
            existing_task = cursor.fetchone()

            if existing_task:
                return existing_task['id']

            # 创建新任务
            sql = """
            INSERT INTO validity_check_tasks (task_name, description, creator_id, status, set_id)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                task_name,
                f"自动创建的质检任务，关联数据集ID: {set_id if set_id else '未分组'}",
                creator_id,
                'draft',
                set_id
            ))
            conn.commit()
            return cursor.lastrowid
        finally:
            cursor.close()
            conn.close()

    def auto_assign_pkls_to_tasks(self, pkl_ids: List[int], id_to_set_id_map: Dict[int, Optional[int]], creator_id: str) -> Dict[int, List[int]]:
        """自动将PKL分配到对应的任务中，返回{task_id: [pkl_id1, pkl_id2, ...]}"""
        # 按set_id分组
        set_id_groups = {}
        for pkl_id in pkl_ids:
            set_id = id_to_set_id_map.get(pkl_id)
            if set_id not in set_id_groups:
                set_id_groups[set_id] = []
            set_id_groups[set_id].append(pkl_id)

        # 为每个set_id创建或获取任务，并分配PKL
        task_pkl_map = {}
        for set_id, pkl_group in set_id_groups.items():
            task_id = self.get_or_create_task_by_set_id(set_id, creator_id)
            task_pkl_map[task_id] = pkl_group

            # 添加PKL到任务
            self.add_pkls_to_task(task_id, pkl_group)

        return task_pkl_map

    def get_pkls_by_task_id(self, task_id: int) -> List[ValidityCheckTaskPkl]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.* FROM validity_check_task_pkls p
            JOIN validity_check_task_pkl_map m ON p.id = m.pkl_id
            WHERE m.task_id = %s
            ORDER BY p.created_at
            """
            cursor.execute(sql, (task_id,))
            rows = cursor.fetchall()
            return [self._row_to_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    # 任务管理
    def create_task(self, task: ValidityCheckTask) -> int:
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = """
            INSERT INTO validity_check_tasks (task_name, description, creator_id, status)
            VALUES (%s, %s, %s, %s)
            """
            cursor.execute(sql, (task.task_name, task.description,
                           task.creator_id, task.status.value))
            conn.commit()
            return cursor.lastrowid
        finally:
            cursor.close()
            conn.close()

    def update_task_status(self, task_id: int, status: TaskStatus) -> bool:
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = "UPDATE validity_check_tasks SET status = %s WHERE id = %s"
            cursor.execute(sql, (status.value, task_id))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    def get_task_by_id(self, task_id: int) -> Optional[ValidityCheckTask]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            # 方案1：分步查询优化版本
            # 第一步：获取任务基本信息
            sql = """
            SELECT id, task_name, description, creator_id, status, created_at, updated_at, set_id
            FROM validity_check_tasks 
            WHERE id = %s
            """
            cursor.execute(sql, (task_id,))
            task_row = cursor.fetchone()

            if not task_row:
                return None

            # 第二步：获取PKL数量
            sql = """
            SELECT COUNT(*) as pkl_count
            FROM validity_check_task_pkl_map 
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            pkl_result = cursor.fetchone()
            pkl_count = pkl_result['pkl_count'] if pkl_result else 0

            # 第三步：获取分配数量（assignments表相对较小）
            sql = """
            SELECT COUNT(*) as assigned_count
            FROM validity_check_assignments 
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            assigned_result = cursor.fetchone()
            assigned_count = assigned_result['assigned_count'] if assigned_result else 0

            # 第四步：获取完成数量
            sql = """
            SELECT COUNT(*) as completed_count
            FROM validity_check_results 
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            completed_result = cursor.fetchone()
            completed_count = completed_result['completed_count'] if completed_result else 0

            # 组装结果
            task_row['pkl_count'] = pkl_count
            task_row['assigned_count'] = assigned_count
            task_row['completed_count'] = completed_count

            return self._row_to_task(task_row)
        finally:
            cursor.close()
            conn.close()

    def get_all_results_excluding_valid(self, exclude_valid: bool = True) -> List[ValidityCheckResult]:
        """获取所有任务的标注结果，可选择是否排除valid结果"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT r.*, p.pkl_name, p.pkl_dir, p.bag_name, p.vehicle_type, 
                   p.date, p.mode, p.version, p.scene_tag, t.task_name
            FROM validity_check_results r
            JOIN validity_check_task_pkls p ON r.pkl_id = p.id
            JOIN validity_check_tasks t ON r.task_id = t.id
            """

            if exclude_valid:
                sql += " WHERE r.validity != 'valid' OR r.validity IS NULL"

            sql += " ORDER BY t.task_name, r.created_at"

            cursor.execute(sql)
            rows = cursor.fetchall()

            results = []
            for row in rows:
                pkl_info = ValidityCheckTaskPkl(
                    id=row['pkl_id'],
                    pkl_name=row['pkl_name'],
                    pkl_dir=row['pkl_dir'],
                    bag_name=row['bag_name'],
                    vehicle_type=VehicleType(
                        row['vehicle_type']) if row['vehicle_type'] else None,
                    date=row['date'],
                    mode=Mode(row['mode']) if row['mode'] else None,
                    version=Version(
                        row['version']) if row['version'] else None,
                    scene_tag=row['scene_tag']
                )

                result = ValidityCheckResult(
                    id=row['id'],
                    assignment_id=row['assignment_id'],
                    task_id=row['task_id'],
                    pkl_id=row['pkl_id'],
                    annotator_id=row['annotator_id'],
                    validity=ValidityResult(
                        row['validity']) if row['validity'] else None,
                    reviewer_id=row['reviewer_id'],
                    review_status=ReviewStatus(row['review_status']),
                    review_time=row['review_time'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    pkl_info=pkl_info
                )
                # 添加任务名称到结果中
                # result.task_name = row['task_name']
                results.append(result)

            return results
        finally:
            cursor.close()
            conn.close()

    def get_tasks_with_pagination(self, creator_id: Optional[str] = None,
                                  status: Optional[TaskStatus] = None,
                                  page_size: Optional[int] = None, page_num: Optional[int] = None):
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)

            count_sql = """
                SELECT status, COUNT(*) AS count
                FROM validity_check_tasks
                WHERE 
                    1=1
                    {creator_clause}
                    {status_clause}
                GROUP BY status
                UNION ALL
                SELECT 'total' as status, COUNT(*) as count
                FROM validity_check_tasks
                WHERE 
                    1=1
                    {creator_clause}
                    {status_clause}
                """
            creator_clause = "AND creator_id = %s" if creator_id is not None else ""
            status_clause = "AND status = %s" if status is not None else ""
            count_sql = count_sql.format(
                creator_clause=creator_clause, status_clause=status_clause)
            count_params = []
            if creator_id is not None:
                count_params.extend([creator_id, creator_id])
            if status is not None:
                count_params.extend([status.value, status.value])

            # 执行计数查询
            total = 0
            count = {}
            cursor.execute(count_sql, count_params)
            rows = cursor.fetchall()
            for row in rows:
                count[row["status"]] = row["count"]
                if row["status"] == "total":
                    total = row["count"]

            # 计算分页元数据
            if page_size is None or page_num is None:
                page = 1
                page_size = total
                total_pages = 1
            else:
                page = page_num
                total_pages = math.ceil(
                    total / page_size) if page_size > 0 else 1
            return {
                "success": True,
                "tasks": self.get_tasks(creator_id, status, page_size, page_num),
                "status": count,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_pages": total_pages
                }
            }
        finally:
            cursor.close()
            conn.close()

    def get_tasks(self, creator_id: Optional[str] = None, status: Optional[TaskStatus] = None,
                  page_size: Optional[int] = None, page_num: Optional[int] = None) -> List[ValidityCheckTask]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT 
                t.id,
                t.task_name,
                t.description,
                t.creator_id,
                t.status,
                t.set_id,
                t.created_at,
                t.updated_at,
                COALESCE(m.pkl_count, 0) AS pkl_count,
                COALESCE(a.assigned_count, 0) AS assigned_count,
                COALESCE(c.completed_count, 0) AS completed_count
            FROM 
                validity_check_tasks t
            LEFT JOIN (
                SELECT 
                    task_id, 
                    COUNT(pkl_id) AS pkl_count
                FROM 
                    validity_check_task_pkl_map
                GROUP BY 
                    task_id
            ) m ON t.id = m.task_id
            LEFT JOIN (
                SELECT 
                    task_id,
                    COUNT(DISTINCT pkl_id) AS assigned_count
                FROM 
                    validity_check_annotator_pkl_map
                GROUP BY 
                    task_id
            ) a ON t.id = a.task_id
            LEFT JOIN (
                SELECT 
                    task_id,
                    COUNT(DISTINCT id) AS completed_count
                FROM 
                    validity_check_results
                GROUP BY 
                    task_id
            ) c ON t.id = c.task_id
            WHERE 
                1=1
                {creator_clause}
                {status_clause}
            ORDER BY 
                t.created_at DESC
            {pagination_clause}
            """
            creator_clause = "AND creator_id = %s" if creator_id is not None else ""
            status_clause = "AND status = %s" if status is not None else ""
            offset = None
            if page_size is not None and page_num is not None:
                offset = (page_num - 1) * page_size

            pagination_clause = "LIMIT %s OFFSET %s" if page_size is not None and page_num is not None else ""
            query = sql.format(creator_clause=creator_clause,
                               status_clause=status_clause, pagination_clause=pagination_clause)

            # 准备查询参数
            params = []
            if creator_id is not None:
                params.extend([creator_id])
            if status is not None:
                params.extend([status.value])
            if page_size is not None and offset is not None:
                params.extend([page_size, offset])

            # 执行主查询
            cursor.execute(query, params)
            tasks = cursor.fetchall()
            return [self._row_to_task(row) for row in tasks]
        finally:
            cursor.close()
            conn.close()

    def add_pkls_to_task(self, task_id: int, pkl_ids: List[int]) -> bool:
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = "INSERT IGNORE INTO validity_check_task_pkl_map (task_id, pkl_id) VALUES (%s, %s)"
            values = [(task_id, pkl_id) for pkl_id in pkl_ids]
            cursor.executemany(sql, values)
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    # 分配管理
    def create_assignment(self, assignment: ValidityCheckAssignment) -> int:
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = """
            INSERT IGNORE INTO validity_check_assignments (task_id, annotator_id, assigned_by)
            VALUES (%s, %s, %s)
            """
            cursor.execute(
                sql, (assignment.task_id, assignment.annotator_id, assignment.assigned_by))
            conn.commit()
            return cursor.lastrowid
        finally:
            cursor.close()
            conn.close()

    def get_assignments_by_task_id(self, task_id: int) -> List[ValidityCheckAssignment]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT a.*,
                   COUNT(DISTINCT m.pkl_id) as pkl_count,
                   COUNT(DISTINCT r.id) as completed_pkl_count
            FROM validity_check_assignments a
            LEFT JOIN validity_check_task_pkl_map m ON a.task_id = m.task_id
            LEFT JOIN validity_check_results r ON a.id = r.assignment_id
            WHERE a.task_id = %s
            GROUP BY a.id
            ORDER BY a.assigned_at
            """
            cursor.execute(sql, (task_id,))
            rows = cursor.fetchall()
            return [self._row_to_assignment(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_assignments_by_annotator(self, annotator_id: str) -> List[ValidityCheckAssignment]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            # 方案1：分步查询，避免复杂的JOIN
            # 第一步：获取基本的assignment信息
            sql = """
            SELECT a.*
            FROM validity_check_assignments a
            WHERE a.annotator_id = %s
            ORDER BY a.assigned_at DESC
            """
            cursor.execute(sql, (annotator_id,))
            assignments = cursor.fetchall()

            if not assignments:
                return []

            # 获取所有相关的task_id
            task_ids = [row['task_id'] for row in assignments]
            task_ids_placeholder = ','.join(['%s'] * len(task_ids))

            # 第二步：批量获取每个任务的PKL统计
            pkl_sql = f"""
            SELECT task_id, COUNT(pkl_id) as pkl_count
            FROM validity_check_task_pkl_map 
            WHERE task_id IN ({task_ids_placeholder})
            GROUP BY task_id
            """
            cursor.execute(pkl_sql, task_ids)
            pkl_stats = {row['task_id']: row['pkl_count']
                         for row in cursor.fetchall()}

            # 第三步：批量获取每个assignment的完成统计
            assignment_ids = [row['id'] for row in assignments]
            assignment_ids_placeholder = ','.join(['%s'] * len(assignment_ids))

            completed_sql = f"""
            SELECT assignment_id, COUNT(id) as completed_count
            FROM validity_check_results 
            WHERE assignment_id IN ({assignment_ids_placeholder})
            GROUP BY assignment_id
            """
            cursor.execute(completed_sql, assignment_ids)
            completed_stats = {
                row['assignment_id']: row['completed_count'] for row in cursor.fetchall()}

            # 第四步：组装结果
            result = []
            for assignment_row in assignments:
                assignment_row['pkl_count'] = pkl_stats.get(
                    assignment_row['task_id'], 0)
                assignment_row['completed_pkl_count'] = completed_stats.get(
                    assignment_row['id'], 0)
                result.append(self._row_to_assignment(assignment_row))

            return result
        finally:
            cursor.close()
            conn.close()

    def update_assignment_start_time(self, assignment_id: int) -> bool:
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = "UPDATE validity_check_assignments SET started_at = NOW() WHERE id = %s AND started_at IS NULL"
            cursor.execute(sql, (assignment_id,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    # 标注结果管理
    def create_result(self, result: ValidityCheckResult) -> int:
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = """
            INSERT INTO validity_check_results 
            (assignment_id, task_id, pkl_id, annotator_id, validity)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                result.assignment_id, result.task_id, result.pkl_id,
                result.annotator_id, result.validity.value if result.validity else None
            ))
            conn.commit()
            return cursor.lastrowid
        finally:
            cursor.close()
            conn.close()

    def update_result(self, result_id: int, validity: ValidityResult) -> bool:
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = "UPDATE validity_check_results SET validity = %s WHERE id = %s"
            cursor.execute(sql, (validity.value, result_id))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    def get_results_by_task_id(self, task_id: int) -> List[ValidityCheckResult]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT r.*, p.pkl_name, p.pkl_dir, p.bag_name, p.vehicle_type, 
                   p.date, p.mode, p.version, p.scene_tag
            FROM validity_check_results r
            JOIN validity_check_task_pkls p ON r.pkl_id = p.id
            WHERE r.task_id = %s
            ORDER BY r.created_at
            """
            cursor.execute(sql, (task_id,))
            rows = cursor.fetchall()
            return [self._row_to_result_with_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_results_by_assignment_id(self, assignment_id: int) -> List[ValidityCheckResult]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT r.*, p.pkl_name, p.pkl_dir, p.bag_name, p.vehicle_type, 
                   p.date, p.mode, p.version, p.scene_tag
            FROM validity_check_results r
            JOIN validity_check_task_pkls p ON r.pkl_id = p.id
            WHERE r.assignment_id = %s
            ORDER BY r.created_at
            """
            cursor.execute(sql, (assignment_id,))
            rows = cursor.fetchall()
            return [self._row_to_result_with_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_unassigned_pkls_for_annotator(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取标注员未完成标注的PKL列表"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.* FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            WHERE apm.task_id = %s AND apm.annotator_id = %s 
            AND apm.is_completed = FALSE
            ORDER BY p.created_at
            """
            cursor.execute(sql, (task_id, annotator_id))
            rows = cursor.fetchall()
            return [self._row_to_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    # 进度统计
    def get_task_progress(self, task_id: int) -> Optional[TaskProgress]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            # 获取任务基本信息
            sql = """
            SELECT t.task_name,
                   COUNT(DISTINCT m.pkl_id) as total_pkls,
                   COUNT(DISTINCT a.id) as assigned_count,
                   COUNT(DISTINCT r.id) as completed_pkls
            FROM validity_check_tasks t
            LEFT JOIN validity_check_task_pkl_map m ON t.id = m.task_id
            LEFT JOIN validity_check_assignments a ON t.id = a.task_id
            LEFT JOIN validity_check_results r ON t.id = r.task_id
            WHERE t.id = %s
            GROUP BY t.id
            """
            cursor.execute(sql, (task_id,))
            task_row = cursor.fetchone()

            if not task_row:
                return None

            # 获取标注员进度
            sql = """
            SELECT a.annotator_id,
                   COUNT(DISTINCT m.pkl_id) as assigned_pkls,
                   COUNT(DISTINCT r.id) as completed_pkls
            FROM validity_check_assignments a
            LEFT JOIN validity_check_task_pkl_map m ON a.task_id = m.task_id
            LEFT JOIN validity_check_results r ON a.id = r.assignment_id
            WHERE a.task_id = %s
            GROUP BY a.annotator_id
            """
            cursor.execute(sql, (task_id,))
            annotator_rows = cursor.fetchall()

            annotator_progresses = []
            for row in annotator_rows:
                assigned = row['assigned_pkls'] or 0
                completed = row['completed_pkls'] or 0
                progress_pct = (completed / assigned *
                                100) if assigned > 0 else 0

                annotator_progresses.append(AnnotatorProgress(
                    annotator_id=row['annotator_id'],
                    assigned_pkls=assigned,
                    completed_pkls=completed,
                    progress_percentage=progress_pct
                ))

            total_pkls = task_row['total_pkls'] or 0
            completed_pkls = task_row['completed_pkls'] or 0
            progress_pct = (completed_pkls / total_pkls *
                            100) if total_pkls > 0 else 0

            return TaskProgress(
                task_id=task_id,
                task_name=task_row['task_name'],
                total_pkls=total_pkls,
                assigned_pkls=task_row['assigned_count'] or 0,
                completed_pkls=completed_pkls,
                progress_percentage=progress_pct,
                annotator_progresses=annotator_progresses
            )
        finally:
            cursor.close()
            conn.close()

    # 辅助方法
    def _row_to_pkl(self, row: dict) -> ValidityCheckTaskPkl:
        return ValidityCheckTaskPkl(
            id=row['id'],
            pkl_name=row['pkl_name'],
            pkl_dir=row['pkl_dir'],
            bag_name=row['bag_name'],
            vehicle_type=VehicleType(
                row['vehicle_type']) if row['vehicle_type'] else None,
            date=row['date'],
            mode=Mode(row['mode']) if row['mode'] else None,
            version=Version(row['version']) if row['version'] else None,
            scene_tag=row['scene_tag'],
            set_id=row.get('set_id'),
            is_checked=row['is_checked'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )

    def _row_to_brief_pkl(self, row: dict) -> ValidityCheckBriefTaskPkl:
        return ValidityCheckBriefTaskPkl(
            id=row['id'],
            bag_name=row['bag_name']
        )

    def _row_to_task(self, row: dict) -> ValidityCheckTask:
        return ValidityCheckTask(
            id=row['id'],
            task_name=row['task_name'],
            description=row['description'],
            creator_id=row['creator_id'],
            status=TaskStatus(row['status']),
            created_at=row['created_at'],
            updated_at=row['updated_at'],
            pkl_count=row.get('pkl_count', 0),
            assigned_count=row.get('assigned_count', 0),
            completed_count=row.get('completed_count', 0)
        )

    def _row_to_assignment(self, row: dict) -> ValidityCheckAssignment:
        return ValidityCheckAssignment(
            id=row['id'],
            task_id=row['task_id'],
            annotator_id=row['annotator_id'],
            assigned_by=row['assigned_by'],
            assigned_at=row['assigned_at'],
            started_at=row['started_at'],
            completed_at=row['completed_at'],
            created_at=row['created_at'],
            updated_at=row['updated_at'],
            pkl_count=row.get('pkl_count', 0),
            completed_pkl_count=row.get('completed_pkl_count', 0)
        )

    def _row_to_result_with_pkl(self, row: dict) -> ValidityCheckResult:
        pkl_info = ValidityCheckTaskPkl(
            id=row['pkl_id'],
            pkl_name=row['pkl_name'],
            pkl_dir=row['pkl_dir'],
            bag_name=row['bag_name'],
            vehicle_type=VehicleType(
                row['vehicle_type']) if row['vehicle_type'] else None,
            date=row['date'],
            mode=Mode(row['mode']) if row['mode'] else None,
            version=Version(row['version']) if row['version'] else None,
            scene_tag=row['scene_tag']
        )

        return ValidityCheckResult(
            id=row['id'],
            assignment_id=row['assignment_id'],
            task_id=row['task_id'],
            pkl_id=row['pkl_id'],
            annotator_id=row['annotator_id'],
            validity=ValidityResult(
                row['validity']) if row['validity'] else None,
            reviewer_id=row['reviewer_id'],
            review_status=ReviewStatus(row['review_status']),
            review_time=row['review_time'],
            created_at=row['created_at'],
            updated_at=row['updated_at'],
            pkl_info=pkl_info
        )

    def get_unassigned_pkls(self) -> List[ValidityCheckTaskPkl]:
        """获取未分配到任务的PKL列表"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.* FROM validity_check_task_pkls p
            LEFT JOIN validity_check_task_pkl_map m ON p.id = m.pkl_id
            WHERE m.pkl_id IS NULL
            ORDER BY p.created_at DESC
            """
            cursor.execute(sql)
            rows = cursor.fetchall()
            return [self._row_to_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def count_pkls_in_task(self, task_id: int) -> int:
        """计算任务中的PKL总数"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT COUNT(*) as count FROM validity_check_task_pkl_map 
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def count_assigned_pkls_in_task(self, task_id: int) -> int:
        """计算任务中已分配的PKL数量（有标注员分配的）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT COUNT(DISTINCT m.pkl_id) as count 
            FROM validity_check_task_pkl_map m
            WHERE m.task_id = %s 
            AND EXISTS (
                SELECT 1 FROM validity_check_assignments a 
                WHERE a.task_id = m.task_id
            )
            """
            cursor.execute(sql, (task_id,))
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def count_completed_pkls_in_task(self, task_id: int) -> int:
        """计算任务中已完成标注的PKL数量"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT COUNT(DISTINCT pkl_id) as count 
            FROM validity_check_results 
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def count_completed_pkls_by_annotator(self, task_id: int, annotator_id: str) -> int:
        """计算指定标注员已完成的PKL数量"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT COUNT(*) as count
            FROM validity_check_annotator_pkl_map
            WHERE task_id = %s AND annotator_id = %s AND is_completed = TRUE
            """
            cursor.execute(sql, (task_id, annotator_id))
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def count_assigned_pkls_for_annotator(self, task_id: int, annotator_id: str) -> int:
        """计算分配给指定标注员的PKL数量"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT COUNT(*) as count
            FROM validity_check_annotator_pkl_map
            WHERE task_id = %s AND annotator_id = %s
            """
            cursor.execute(sql, (task_id, annotator_id))
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def get_assignment_by_id(self, assignment_id: int) -> Optional[ValidityCheckAssignment]:
        """根据ID获取分配任务"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT a.*,
                   COUNT(DISTINCT m.pkl_id) as pkl_count,
                   COUNT(DISTINCT r.id) as completed_pkl_count
            FROM validity_check_assignments a
            LEFT JOIN validity_check_task_pkl_map m ON a.task_id = m.task_id
            LEFT JOIN validity_check_results r ON a.id = r.assignment_id
            WHERE a.id = %s
            GROUP BY a.id
            """
            cursor.execute(sql, (assignment_id,))
            row = cursor.fetchone()
            if row:
                return self._row_to_assignment(row)
            return None
        finally:
            cursor.close()
            conn.close()

    def update_assignment_completed_time(self, assignment_id: int) -> bool:
        """更新分配任务的完成时间"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = "UPDATE validity_check_assignments SET completed_at = NOW() WHERE id = %s AND completed_at IS NULL"
            cursor.execute(sql, (assignment_id,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    def count_total_pkls(self) -> int:
        """计算总PKL数量"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT COUNT(*) as count FROM validity_check_task_pkls"
            cursor.execute(sql)
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def count_total_completed_annotations(self) -> int:
        """计算总的已完成标注数量"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT COUNT(*) as count FROM validity_check_results"
            cursor.execute(sql)
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def is_pkl_assigned_to_annotator(self, task_id: int, pkl_id: int, annotator_id: str) -> bool:
        """检查PKL是否已分配给指定标注员"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT COUNT(*) as count
            FROM validity_check_task_pkl_map m
            JOIN validity_check_assignments a ON m.task_id = a.task_id
            WHERE m.task_id = %s AND m.pkl_id = %s AND a.annotator_id = %s
            """
            cursor.execute(sql, (task_id, pkl_id, annotator_id))
            result = cursor.fetchone()
            return (result['count'] if result else 0) > 0
        finally:
            cursor.close()
            conn.close()

    def assign_pkl_to_annotator(self, task_id: int, pkl_id: int, annotator_id: str) -> bool:
        """将PKL分配给标注员（这里主要是确保assignment存在）"""
        # 这个方法在当前的数据结构下可能不需要，因为assignment是按任务分配的
        # 如果需要更细粒度的分配控制，可能需要调整数据库结构
        pass

    def get_unassigned_pkls_for_annotator_grouped(self, task_id: int, annotator_id: str) -> Dict[str, List[ValidityCheckTaskPkl]]:
        """按bag_name分组获取标注员的未标注PKL"""
        pkls = self.get_unassigned_pkls_for_annotator(task_id, annotator_id)
        grouped = {}
        for pkl in pkls:
            bag_name = pkl.bag_name or 'unknown'
            if bag_name not in grouped:
                grouped[bag_name] = []
            grouped[bag_name].append(pkl)
        return grouped

    def assign_pkls_to_annotators(self, task_id: int, annotator_assignments: Dict[str, List[int]]) -> bool:
        """将PKL平均分配给标注员
        Args:
            task_id: 任务ID
            annotator_assignments: {annotator_id: [pkl_id1, pkl_id2, ...]}
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            # 批量插入PKL分配
            sql = """
            INSERT INTO validity_check_annotator_pkl_map 
            (task_id, annotator_id, pkl_id) 
            VALUES (%s, %s, %s)
            """
            values = []
            for annotator_id, pkl_ids in annotator_assignments.items():
                for pkl_id in pkl_ids:
                    values.append((task_id, annotator_id, pkl_id))

            if values:
                cursor.executemany(sql, values)
                conn.commit()
                return True
            return False
        finally:
            cursor.close()
            conn.close()

    def get_assigned_pkls_for_annotator(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取分配给指定标注员的PKL列表"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.* FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            WHERE apm.task_id = %s AND apm.annotator_id = %s
            ORDER BY p.created_at
            """
            cursor.execute(sql, (task_id, annotator_id))
            rows = cursor.fetchall()
            return [self._row_to_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def mark_pkl_completed(self, task_id: int, pkl_id: int, annotator_id: str) -> bool:
        """标记PKL为已完成"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = """
            UPDATE validity_check_annotator_pkl_map 
            SET is_completed = TRUE, updated_at = NOW()
            WHERE task_id = %s AND pkl_id = %s AND annotator_id = %s
            """
            cursor.execute(sql, (task_id, pkl_id, annotator_id))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    def get_annotator_progress_detailed(self, task_id: int, annotator_id: str) -> Dict[str, Any]:
        """获取标注员详细进度"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT 
                COUNT(*) as total_assigned,
                SUM(CASE WHEN is_completed = TRUE THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN is_completed = FALSE THEN 1 ELSE 0 END) as remaining
            FROM validity_check_annotator_pkl_map
            WHERE task_id = %s AND annotator_id = %s
            """
            cursor.execute(sql, (task_id, annotator_id))
            result = cursor.fetchone()

            if result and result['total_assigned'] > 0:
                progress_pct = (result['completed'] /
                                result['total_assigned']) * 100
                return {
                    'total_assigned': result['total_assigned'],
                    'completed': result['completed'],
                    'remaining': result['remaining'],
                    'progress_percentage': round(progress_pct, 2)
                }
            return {
                'total_assigned': 0,
                'completed': 0,
                'remaining': 0,
                'progress_percentage': 0.0
            }
        finally:
            cursor.close()
            conn.close()

    def get_task_overall_progress(self, task_id: int) -> Dict[str, Any]:
        """获取任务总体进度"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT 
                COUNT(*) as total_pkls,
                SUM(CASE WHEN is_completed = TRUE THEN 1 ELSE 0 END) as completed_pkls,
                COUNT(DISTINCT annotator_id) as total_annotators
            FROM validity_check_annotator_pkl_map
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            result = cursor.fetchone()

            if result and result['total_pkls'] > 0:
                progress_pct = (result['completed_pkls'] /
                                result['total_pkls']) * 100
                return {
                    'total_pkls': result['total_pkls'],
                    'completed_pkls': result['completed_pkls'],
                    'remaining_pkls': result['total_pkls'] - result['completed_pkls'],
                    'total_annotators': result['total_annotators'],
                    'progress_percentage': round(progress_pct, 2)
                }
            return {
                'total_pkls': 0,
                'completed_pkls': 0,
                'remaining_pkls': 0,
                'total_annotators': 0,
                'progress_percentage': 0.0
            }
        finally:
            cursor.close()
            conn.close()

    def get_task_assignment_status(self, task_id: int) -> Dict[str, Any]:
        """获取任务的分配状态详情"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            # 获取任务总PKL数
            sql = """
            SELECT COUNT(*) as total_pkls
            FROM validity_check_task_pkl_map
            WHERE task_id = %s
            """
            cursor.execute(sql, (task_id,))
            total_result = cursor.fetchone()
            total_pkls = total_result['total_pkls'] if total_result else 0

            # 获取已分配的PKL数（有标注员分配记录的）
            sql = """
            SELECT COUNT(DISTINCT m.pkl_id) as assigned_pkls
            FROM validity_check_task_pkl_map m
            INNER JOIN validity_check_annotator_pkl_map apm ON m.pkl_id = apm.pkl_id AND m.task_id = apm.task_id
            WHERE m.task_id = %s
            """
            cursor.execute(sql, (task_id,))
            assigned_result = cursor.fetchone()
            assigned_pkls = assigned_result['assigned_pkls'] if assigned_result else 0

            # 获取未分配的PKL数
            unassigned_pkls = total_pkls - assigned_pkls

            # 获取当前分配的标注员信息
            sql = """
            SELECT DISTINCT a.annotator_id, COUNT(DISTINCT apm.pkl_id) as pkl_count
            FROM validity_check_assignments a
            LEFT JOIN validity_check_annotator_pkl_map apm ON a.task_id = apm.task_id AND a.annotator_id = apm.annotator_id
            WHERE a.task_id = %s
            GROUP BY a.annotator_id
            """
            cursor.execute(sql, (task_id,))
            annotator_assignments = cursor.fetchall()

            return {
                'task_id': task_id,
                'total_pkls': total_pkls,
                'assigned_pkls': assigned_pkls,
                'unassigned_pkls': unassigned_pkls,
                'annotator_assignments': annotator_assignments or []
            }
        finally:
            cursor.close()
            conn.close()

    def get_unassigned_pkl_ids_in_task(self, task_id: int) -> List[int]:
        """获取任务中未分配的PKL ID列表"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT m.pkl_id
            FROM validity_check_task_pkl_map m
            LEFT JOIN validity_check_annotator_pkl_map apm ON m.pkl_id = apm.pkl_id AND m.task_id = apm.task_id
            WHERE m.task_id = %s AND apm.pkl_id IS NULL
            ORDER BY m.pkl_id
            """
            cursor.execute(sql, (task_id,))
            results = cursor.fetchall()
            return [row['pkl_id'] for row in results]
        finally:
            cursor.close()
            conn.close()

    def get_uncompleted_pkls_for_annotator(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取标注员未完成标注的PKL列表"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.* FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            WHERE apm.task_id = %s AND apm.annotator_id = %s 
            AND apm.is_completed = FALSE
            ORDER BY p.created_at
            """
            cursor.execute(sql, (task_id, annotator_id))
            rows = cursor.fetchall()
            return [self._row_to_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_completed_pkls_for_annotator(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取标注员已完成标注的PKL列表"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.* FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            WHERE apm.task_id = %s AND apm.annotator_id = %s 
            AND apm.is_completed = TRUE
            ORDER BY p.created_at
            """
            cursor.execute(sql, (task_id, annotator_id))
            rows = cursor.fetchall()
            return [self._row_to_pkl(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_all_assigned_pkls_for_annotator(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取分配给标注员的所有PKL列表（包括已完成和未完成）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.*, apm.is_completed
            FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            WHERE apm.task_id = %s AND apm.annotator_id = %s
            ORDER BY p.created_at
            """
            cursor.execute(sql, (task_id, annotator_id))
            rows = cursor.fetchall()
            pkls = []
            for row in rows:
                pkl = self._row_to_pkl(row)
                pkl.is_completed = row['is_completed']  # 添加完成状态
                pkls.append(pkl)
            return pkls
        finally:
            cursor.close()
            conn.close()

    def get_all_assigned_pkls_for_annotator_with_results(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取分配给标注员的所有PKL列表（包括已完成和未完成，以及标注结果）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.*, apm.is_completed, r.validity, r.created_at as annotation_time
            FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            LEFT JOIN validity_check_results r ON p.id = r.pkl_id AND r.annotator_id = %s AND r.task_id = %s
            WHERE apm.task_id = %s AND apm.annotator_id = %s
            ORDER BY p.created_at
            """
            cursor.execute(sql, (annotator_id, task_id, task_id, annotator_id))
            rows = cursor.fetchall()
            pkls = []
            for row in rows:
                pkl = self._row_to_pkl(row)
                pkl.is_completed = row['is_completed']
                pkl.annotation_result = row['validity'] if row['validity'] else None
                pkl.annotation_time = row['annotation_time']
                pkls.append(pkl)
            return pkls
        finally:
            cursor.close()
            conn.close()

    def get_all_assigned_pkls_complete_with_results(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取分配给标注员的所有PKL列表（包括已完成和未完成，以及标注结果）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.bag_name, apm.is_completed, r.validity, r.created_at as annotation_time
            FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            LEFT JOIN validity_check_results r ON p.id = r.pkl_id AND r.annotator_id = %s AND r.task_id = %s
            WHERE apm.task_id = %s AND apm.annotator_id = %s
            ORDER BY p.created_at
            """
            cursor.execute(sql, (annotator_id, task_id, task_id, annotator_id))
            rows = cursor.fetchall()
            pkls = []
            for row in rows:
                pkl = types.SimpleNamespace()
                pkl.bag_name = row['bag_name']
                pkl.is_completed = row['is_completed']
                pkl.annotation_result = row['validity'] if row['validity'] else None
                pkl.annotation_time = row['annotation_time']
                pkls.append(pkl)
            return pkls
        finally:
            cursor.close()
            conn.close()

    def get_limit_assigned_pkls_for_annotator_with_results(self, task_id: int, annotator_id: str, start: int = 0, limit: int = 20) -> List[ValidityCheckTaskPkl]:
        """获取分配给标注员的所有PKL列表（包括已完成和未完成，以及标注结果）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.*, apm.is_completed, r.validity, r.created_at as annotation_time
            FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            LEFT JOIN validity_check_results r ON p.id = r.pkl_id AND r.annotator_id = %s AND r.task_id = %s
            WHERE apm.task_id = %s AND apm.annotator_id = %s
            ORDER BY p.created_at
            limit %s,%s
            """
            cursor.execute(sql, (annotator_id, task_id,
                           task_id, annotator_id, start, limit))
            rows = cursor.fetchall()
            pkls = []
            for row in rows:
                pkl = self._row_to_pkl(row)
                pkl.is_completed = row['is_completed']
                pkl.annotation_result = row['validity'] if row['validity'] else None
                pkl.annotation_time = row['annotation_time']
                pkls.append(pkl)
            return pkls
        finally:
            cursor.close()
            conn.close()

    def get_brief_limit_assigned_pkls_for_annotator_with_results(self, task_id: int,
                                                                 annotator_id: str) -> List[ValidityCheckBriefTaskPkl]:
        """获取分配给标注员的所有PKL列表（简要信息）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.id, p.bag_name, apm.is_completed
            FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            LEFT JOIN validity_check_results r ON p.id = r.pkl_id AND r.annotator_id = %s AND r.task_id = %s
            WHERE apm.task_id = %s AND apm.annotator_id = %s
            ORDER BY p.created_at
            """
            cursor.execute(sql, (annotator_id, task_id,
                           task_id, annotator_id))
            rows = cursor.fetchall()
            pkls = []
            for row in rows:
                pkl = self._row_to_brief_pkl(row)
                pkl.is_completed = row['is_completed']
                pkls.append(pkl)
            return pkls
        finally:
            cursor.close()
            conn.close()

    def get_assigned_pkls_detail(self, pkl_id: int) -> Optional[ValidityCheckTaskPkl]:
        """获取pkl详细信息"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.*, apm.is_completed, r.validity, r.created_at as annotation_time
            FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            LEFT JOIN validity_check_results r ON p.id = r.pkl_id
            WHERE p.id = %s
            ORDER BY r.created_at desc
            LIMIT 1
            """
            cursor.execute(sql, (pkl_id,))
            result = cursor.fetchone()
            if result:
                pkl = self._row_to_pkl(result)
                pkl.is_completed = result['is_completed']
                pkl.annotation_result = result['validity'] if result['validity'] else None
                pkl.annotation_time = result['annotation_time']
                return pkl
            return None
        finally:
            cursor.close()
            conn.close()

    def get_active_annotators_in_date_range(self, start_date: str, end_date: str) -> List[User]:
        """获取在指定日期范围内有标注活动的标注员"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT DISTINCT u.* FROM users u
            JOIN validity_check_assignments a ON u.id = a.annotator_id
            WHERE a.assigned_at >= %s AND a.assigned_at <= %s
            AND u.is_active = TRUE
            ORDER BY u.username
            """
            cursor.execute(sql, (start_date, end_date + ' 23:59:59'))
            rows = cursor.fetchall()
            return [self._row_to_user(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_annotator_assignments_with_task_info(self, annotator_id: str) -> List[Dict]:
        """获取标注员的所有任务分配（包含任务信息）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT a.*, t.task_name
            FROM validity_check_assignments a
            JOIN validity_check_tasks t ON a.task_id = t.id
            WHERE a.annotator_id = %s
            ORDER BY a.assigned_at DESC
            """
            cursor.execute(sql, (annotator_id,))
            rows = cursor.fetchall()
            return rows
        finally:
            cursor.close()
            conn.close()

    def get_annotator_task_progress(self, task_id: int, annotator_id: str) -> Dict:
        """获取标注员在特定任务中的进度"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT 
                COUNT(*) as total_assigned,
                SUM(CASE WHEN is_completed = TRUE THEN 1 ELSE 0 END) as completed
            FROM validity_check_annotator_pkl_map
            WHERE task_id = %s AND annotator_id = %s
            """
            cursor.execute(sql, (task_id, annotator_id))
            result = cursor.fetchone()

            if result and result['total_assigned'] > 0:
                progress_percentage = round(
                    (result['completed'] / result['total_assigned']) * 100, 2)
                return {
                    'total_assigned': result['total_assigned'],
                    'completed': result['completed'],
                    'progress_percentage': progress_percentage
                }
            return {
                'total_assigned': 0,
                'completed': 0,
                'progress_percentage': 0.0
            }
        finally:
            cursor.close()
            conn.close()

    def get_annotator_bag_groups_progress(self, task_id: int, annotator_id: str) -> List[Dict]:
        """获取标注员在特定任务中的BagGroup进度"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT p.bag_name, apm.is_completed
            FROM validity_check_task_pkls p
            JOIN validity_check_annotator_pkl_map apm ON p.id = apm.pkl_id
            WHERE apm.task_id = %s AND apm.annotator_id = %s
            ORDER BY p.bag_name, p.created_at
            """
            cursor.execute(sql, (task_id, annotator_id))
            rows = cursor.fetchall()

            # 按bag_name分组统计
            bag_groups = {}
            for row in rows:
                bag_name = row['bag_name'] or 'unknown'
                if bag_name not in bag_groups:
                    bag_groups[bag_name] = {
                        'bag_name': bag_name,
                        'total_pkls': 0,
                        'completed_pkls': 0,
                        'is_completed': False
                    }

                bag_groups[bag_name]['total_pkls'] += 1
                if row['is_completed']:
                    bag_groups[bag_name]['completed_pkls'] += 1

            # 判断每个bag_group是否完成
            for bag_name, group_data in bag_groups.items():
                group_data['is_completed'] = (
                    group_data['completed_pkls'] == group_data['total_pkls']
                    and group_data['total_pkls'] > 0
                )

            return list(bag_groups.values())
        finally:
            cursor.close()
            conn.close()

    def get_annotator_daily_progress(self, annotator_id: str, start_date: str, end_date: str) -> List[Dict]:
        """获取标注员每日进度"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT 
                DATE(r.created_at) as date,
                COUNT(DISTINCT r.pkl_id) as completed_pkls
            FROM validity_check_results r
            WHERE r.annotator_id = %s
            AND r.created_at >= %s
            AND r.created_at <= %s
            GROUP BY DATE(r.created_at)
            ORDER BY date
            """
            cursor.execute(
                sql, (annotator_id, start_date, end_date + ' 23:59:59'))
            rows = cursor.fetchall()

            return [
                {
                    'date': str(row['date']),
                    'completed_pkls': row['completed_pkls']
                }
                for row in rows
            ]
        finally:
            cursor.close()
            conn.close()

    def get_annotator_assignments(self, annotator_id: str) -> List[Dict]:
        """获取标注员的所有任务分配"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = """
            SELECT a.*, t.task_name, t.description, t.status as task_status
            FROM validity_check_assignments a
            JOIN validity_check_tasks t ON a.task_id = t.id
            WHERE a.annotator_id = %s
            ORDER BY a.assigned_at DESC
            """
            cursor.execute(sql, (annotator_id,))
            rows = cursor.fetchall()
            return rows
        finally:
            cursor.close()
            conn.close()


    def get_annotator_tasks_aggregated_progress(self, annotator_id: str, start_date: str = None, end_date: str = None) -> \
        Dict[int, Dict]:
        """获取标注员所有任务的进度聚合"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            time_conditions = []
            params = [annotator_id]

            if start_date:
                time_conditions.append("DATE(created_at) >= %s")
                params.append(start_date)
            if end_date:
                time_conditions.append("DATE(created_at) <= %s")
                params.append(end_date + ' 23:59:59')

            time_filter = ""
            if time_conditions:
                time_filter = " AND " + " AND ".join(time_conditions)

            sql = f"""
                  SELECT task_id, 
                         COUNT(*)                                             as total_assigned,
                         SUM(CASE WHEN is_completed = TRUE THEN 1 ELSE 0 END) as completed
                  FROM validity_check_annotator_pkl_map
                  WHERE annotator_id = %s{time_filter}
                  GROUP BY task_id
                  """
            cursor.execute(sql, tuple(params))
            rows = cursor.fetchall()
            tasks_data = {}
            for row in rows:
                total = row['total_assigned']
                completed = row['completed']
                progress_percentage = round((completed / total) * 100, 2) if total > 0 else 0.0
                tasks_data[row['task_id']] = {
                    'total_assigned': total,
                    'completed': completed,
                    'progress_percentage': progress_percentage
                }
            return tasks_data
        finally:
            cursor.close()
            conn.close()

    def get_annotator_bag_groups_aggregated(self, annotator_id: str, start_date: str = None, end_date: str = None) -> \
    Dict[int, List[Dict]]:
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            time_conditions = []
            params = [annotator_id]

            if start_date:
                time_conditions.append("DATE(created_at) >= %s")
                params.append(start_date)
            if end_date:
                time_conditions.append("DATE(created_at) <= %s")
                params.append(end_date + ' 23:59:59')

            time_filter = ""
            if time_conditions:
                time_filter = " AND " + " AND ".join(time_conditions)

            sql = f"""
                    WITH filter AS (
                        SELECT
                            task_id,
                            pkl_id,
                            is_completed
                        FROM validity_check_annotator_pkl_map
                        WHERE annotator_id = %s{time_filter}
                    )
                    SELECT
                        f.task_id,
                        COALESCE(p.bag_name, 'unknown') AS bag_name,
                        COUNT(*) AS total_pkls,
                        SUM(CASE WHEN f.is_completed = TRUE THEN 1 ELSE 0 END) AS completed_pkls
                    FROM filter f
                             JOIN validity_check_task_pkls p ON f.pkl_id = p.id
                    GROUP BY f.task_id, p.bag_name;
                  """
            cursor.execute(sql, tuple(params))
            bag_groups_per_task = defaultdict(list)
            for row in cursor.fetchall():
                task_id = row['task_id']
                bag_data = {
                    'bag_name': row['bag_name'],
                    'total_pkls': row['total_pkls'],
                    'completed_pkls': row['completed_pkls'],
                    'is_completed': row['completed_pkls'] == row['total_pkls'] and row['total_pkls'] > 0
                }
                bag_groups_per_task[task_id].append(bag_data)
            return dict(bag_groups_per_task)
        finally:
            cursor.close()
            conn.close()
