from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, Response
from fastapi.responses import StreamingResponse
from typing import List, Optional, Dict, Any
import io
import csv
from datetime import datetime
from api.validity_check.service import ValidityCheckService
from api.validity_check.entity import *
from api.auth.auth import get_current_user

router = APIRouter(
    prefix="/api/validity-check",
    tags=["validity-check"]
)

service = ValidityCheckService()


@router.post("/pkls/upload", response_model=BaseResponse)
async def upload_pkls(
    file: Optional[UploadFile] = File(None),
    pkls: Optional[List[PklUploadRequest]] = None,
    current_user: dict = Depends(get_current_user)
):
    """上传PKL文件信息"""
    try:
        if file:
            # 处理CSV文件上传
            content = await file.read()
            csv_content = content.decode('utf-8')
            pkls_data, invalid_rows = service.parse_csv_file(csv_content)

            # 为每个PKL设置creator信息
            for pkl_data in pkls_data:
                pkl_data['creator_id'] = current_user.get(
                    'employee_id', 'unknown')

            result = service.upload_pkls(pkls_data)

            response_data = {
                'success': result['success'],
                'message': result['message'],
                'invalid_rows': invalid_rows,
                'invalid_count': len(invalid_rows),
                'valid_count': len(pkls_data),
                # 添加任务分配信息
                'task_assignments': result.get('task_assignments', {})
            }
            return response_data
        elif pkls:
            # 处理直接上传的PKL列表
            pkls_data = []
            for pkl in pkls:
                pkl_dict = pkl.dict()
                pkl_dict['creator_id'] = current_user.get(
                    'employee_id', 'unknown')
                pkls_data.append(pkl_dict)

            result = service.upload_pkls(pkls_data)

            if result['success']:
                return BaseResponse(
                    success=True,
                    message=result['message'],
                    data={'task_assignments': result.get(
                        'task_assignments', {})}
                )
            else:
                raise HTTPException(status_code=400, detail=result['message'])
        else:
            raise HTTPException(status_code=400, detail="请提供文件或PKL数据")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/activate", response_model=BaseResponse)
async def activate_task(
    task_id: int,
    current_user: dict = Depends(get_current_user)
):
    """激活任务"""
    try:
        result = service.activate_task(task_id)

        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", response_model=TaskListResponse)
async def get_tasks(
    creator_id: Optional[str] = Query(None),
    status: Optional[TaskStatus] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """获取任务列表"""
    try:
        tasks = service.get_tasks(creator_id, status.value if status else None)

        return TaskListResponse(
            success=True,
            message="获取成功",
            tasks=tasks
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskDetailResponse)
async def get_task_detail(
    task_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取任务详情"""
    try:
        task = service.get_task_detail(task_id)

        if task:
            return TaskDetailResponse(
                success=True,
                message="获取成功",
                task=task
            )
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/assignments", response_model=AssignmentListResponse)
async def get_task_assignments(
    task_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取任务分配情况"""
    try:
        assignments = service.get_task_assignments(task_id)

        return AssignmentListResponse(
            success=True,
            message="获取成功",
            assignments=assignments
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/assignments/annotator/{annotator_id}", response_model=AssignmentListResponse)
async def get_annotator_assignments(
    annotator_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取标注员的任务分配"""
    try:
        assignments = service.get_annotator_assignments(annotator_id)

        return AssignmentListResponse(
            success=True,
            message="获取成功",
            assignments=assignments
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/assignments/{assignment_id}/start", response_model=BaseResponse)
async def start_annotation(
    assignment_id: int,
    current_user: dict = Depends(get_current_user)
):
    """开始标注"""
    try:
        result = service.start_annotation(assignment_id)

        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/annotations", response_model=BaseResponse)
async def submit_annotation(
    annotation_request: SubmitAnnotationRequest,
    current_user: dict = Depends(get_current_user)
):
    """提交标注结果"""
    try:
        result = service.submit_annotation(
            assignment_id=annotation_request.assignment_id,
            task_id=annotation_request.task_id,
            pkl_id=annotation_request.pkl_id,
            annotator_id=annotation_request.annotator_id,
            validity=annotation_request.validity.value
        )

        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/annotations/pkls", response_model=PklListResponse)
async def get_annotation_pkls(
    task_id: int = Query(..., description="任务ID"),
    annotator_id: str = Query(..., description="标注员ID"),
    current_user: dict = Depends(get_current_user)
):
    """获取需要标注的PKL列表"""
    try:
        pkls = service.get_annotation_pkls(task_id, annotator_id)

        return PklListResponse(
            success=True,
            message="获取成功",
            pkls=pkls
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/annotations/results/{assignment_id}", response_model=ResultListResponse)
async def get_annotation_results(
    assignment_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取标注结果"""
    try:
        results = service.get_annotation_results(assignment_id)

        return ResultListResponse(
            success=True,
            message="获取成功",
            results=results
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/progress", response_model=ProgressResponse)
async def get_task_progress(
    task_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取任务进度"""
    try:
        progress = service.get_task_progress(task_id)

        if progress:
            return ProgressResponse(
                success=True,
                message="获取成功",
                progress=progress
            )
        else:
            raise HTTPException(status_code=404, detail="任务不存在")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/progress", response_model=List[TaskProgress])
async def get_all_tasks_progress(
    creator_id: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """获取所有任务进度"""
    try:
        progresses = service.get_all_tasks_progress(creator_id)
        return progresses

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/export")
async def export_results(
    task_id: int,
    current_user: dict = Depends(get_current_user)
):
    """导出标注结果"""
    try:
        csv_content = service.export_results_to_csv(task_id)

        # 创建文件流
        output = io.StringIO(csv_content)

        # 返回流式响应
        response = StreamingResponse(
            io.BytesIO(csv_content.encode('utf-8')),
            media_type='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=validity_check_results_task_{task_id}.csv'
            }
        )

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics", response_model=StatisticsResponse)
async def get_statistics(
    task_id: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """获取统计信息"""
    try:
        stats = service.get_statistics(task_id)

        return StatisticsResponse(
            success=True,
            message="获取成功",
            statistics=stats
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 健康检查接口


@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now()}
# 在 validity_check.py 中添加


@router.get("/pkls/unassigned", response_model=PklListResponse)
async def get_unassigned_pkls(
    current_user: dict = Depends(get_current_user)
):
    """获取未分配到任务的PKL列表"""
    try:
        unassigned_pkls = service.get_unassigned_pkls()
        return PklListResponse(
            success=True,
            message="获取成功",
            pkls=unassigned_pkls
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/assignments/{assignment_id}/start", response_model=BaseResponse)
async def start_assignment(
    assignment_id: int,
    current_user: dict = Depends(get_current_user)
):
    """开始标注任务"""
    try:
        result = service.start_annotation(assignment_id)
        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/users", response_model=UserListResponse)
async def get_users(
    role: Optional[str] = Query(None, description="用户角色筛选"),
    current_user: dict = Depends(get_current_user)
):
    """获取用户列表"""
    try:
        users = service.get_users(role)

        # 移除敏感信息
        for user in users:
            user.password_hash = None

        return UserListResponse(
            success=True,
            message="获取成功",
            users=users
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/users/annotators", response_model=UserListResponse)
async def get_annotators(
    current_user: dict = Depends(get_current_user)
):
    """获取所有标注员"""
    try:
        annotators = service.get_annotators()

        # 移除敏感信息
        for annotator in annotators:
            annotator.password_hash = None

        return UserListResponse(
            success=True,
            message="获取成功",
            users=annotators
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/users/{employee_id}", response_model=UserDetailResponse)
async def get_user_by_employee_id(
    employee_id: str,
    current_user: dict = Depends(get_current_user)
):
    """根据工号获取用户信息"""
    try:
        user = service.get_user_by_employee_id(employee_id)

        if user:
            # 移除敏感信息
            user.password_hash = None
            return UserDetailResponse(
                success=True,
                message="获取成功",
                user=user
            )
        else:
            raise HTTPException(status_code=404, detail="用户不存在")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/annotations/pkls/{task_id}/{annotator_id}", response_model=PklListResponse)
async def get_annotator_pkls(
    task_id: int,
    annotator_id: str,
    completed: Optional[bool] = Query(
        None, description="是否已完成标注：true=已完成，false=未完成，null=全部"),
    current_user: dict = Depends(get_current_user)
):
    """获取标注员分配的PKL列表"""
    try:
        if completed is False:
            # 获取未完成标注的PKL
            pkls = service.dao.get_uncompleted_pkls_for_annotator(
                task_id, annotator_id)
        elif completed is True:
            # 获取已完成标注的PKL
            pkls = service.dao.get_completed_pkls_for_annotator(
                task_id, annotator_id)
        else:
            # 获取所有分配给该标注员的PKL
            pkls = service.dao.get_all_assigned_pkls_for_annotator(
                task_id, annotator_id)

        return PklListResponse(
            success=True,
            message="获取成功",
            pkls=pkls
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
@router.get("/export-all-results")
async def export_all_results(
    exclude_valid: bool = Query(True, description="是否排除validity=valid的结果"),
    current_user: dict = Depends(get_current_user)
):
    """导出所有任务的标注结果"""
    try:
        csv_content = service.export_all_results_to_csv(exclude_valid)

        # 创建文件流
        response = StreamingResponse(
            io.BytesIO(csv_content.encode('utf-8')),
            media_type='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=all_validity_check_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            }
        )

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/annotations/progress/{task_id}/{annotator_id}")
async def get_annotator_progress(
    task_id: int,
    annotator_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取标注员的详细进度"""
    try:
        progress = service.get_annotator_detailed_progress(
            task_id, annotator_id)

        return {
            "success": True,
            "message": "获取成功",
            "data": progress
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/overall-progress")
async def get_task_overall_progress(
    task_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取任务总体进度"""
    try:
        progress = service.dao.get_task_overall_progress(task_id)

        return {
            "success": True,
            "message": "获取成功",
            "data": progress
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/assignment-status")
async def get_task_assignment_status(
    task_id: int,
    current_user: dict = Depends(get_current_user)
):
    """获取任务的分配状态"""
    try:
        status = service.get_task_assignment_status(task_id)
        return {
            "success": True,
            "message": "获取成功",
            "data": status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/assign-incremental", response_model=BaseResponse)
async def assign_task_incremental(
    task_id: int,
    assign_request: AssignTaskRequest,
    current_user: dict = Depends(get_current_user)
):
    """增量分配任务给标注员（只分配未分配的PKL）"""
    try:
        result = service.assign_task_to_annotators_with_incremental_distribution(
            task_id=task_id,
            annotator_ids=assign_request.annotator_ids,
            assigned_by=assign_request.assigned_by,
            pkl_count=assign_request.pkl_count
        )

        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/annotations/pkls/grouped")
async def get_annotation_pkls_grouped_by_bag(
    task_id: int = Query(...),
    annotator_id: str = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """按bag_name分组获取所有分配的PKL列表（包括标注结果）"""
    try:
        grouped_pkls = service.get_annotation_pkls_with_results(
            task_id, annotator_id)

        return {
            'success': True,
            'message': '获取成功',
            'bag_groups': grouped_pkls
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/annotators/progress")
async def get_annotators_progress(
    start_date: str = Query(..., description="开始日期 YYYY-MM-DD"),
    end_date: str = Query(..., description="结束日期 YYYY-MM-DD"),
    current_user: dict = Depends(get_current_user)
):
    """获取所有标注员的进度统计"""
    try:
        annotator_progresses = service.get_annotators_progress(
            start_date, end_date)

        return {
            'success': True,
            'message': '获取成功',
            'annotator_progresses': annotator_progresses
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
