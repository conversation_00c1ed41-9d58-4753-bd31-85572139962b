from pydantic import BaseModel, Field
from datetime import datetime, date
from typing import Optional, List
from enum import Enum

from api.common.entity import VehicleType, Mode, Version


class TaskStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ValidityResult(str, Enum):
    VALID = "valid"
    BAD_GT = "bad_gt"
    NOT_CENTERED = "not_centered"
    ON_OPPOSITE_ROAD = "on_opposite_road"
    CROSS_BOUNDARY = "cross_boundary"
    DEVIATE_FROM_NAVI = "deviate_from_navi"
    UNKNOWN = "unknown"


class ReviewStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVISION = "needs_revision"

# Request Models


class PklUploadRequest(BaseModel):
    pkl_name: str
    pkl_dir: str
    bag_name: Optional[str] = None
    vehicle_type: Optional[VehicleType] = None
    date: Optional[str] = None  # 格式: YYYY-MM-DD
    mode: Optional[Mode] = None
    version: Optional[Version] = None
    scene_tag: Optional[str] = None
    set_id: Optional[int] = None


class CreateTaskRequest(BaseModel):
    task_name: str
    description: Optional[str] = None
    creator_id: int
    pkl_ids: List[int] = []


class AssignTaskRequest(BaseModel):
    annotator_ids: List[int]
    assigned_by: int
    pkl_count: Optional[int] = None


class SubmitAnnotationRequest(BaseModel):
    assignment_id: int
    task_id: int
    pkl_id: int
    annotator_id: int
    validity: ValidityResult

# Response Models


class ValidityCheckTaskPkl(BaseModel):
    id: Optional[int] = None
    pkl_name: str
    pkl_dir: str
    bag_name: Optional[str] = None
    vehicle_type: Optional[VehicleType] = None
    date: Optional[datetime] = None
    mode: Optional[Mode] = None
    version: Optional[Version] = None
    scene_tag: Optional[str] = None
    set_id: Optional[int] = None
    is_checked: bool = False
    is_completed: Optional[bool] = None
    annotation_result: Optional[str] = None
    annotation_time: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ValidityCheckBriefTaskPkl(BaseModel):
    id: Optional[int] = None
    bag_name: Optional[str] = None
    is_completed: Optional[bool] = None

    class Config:
        from_attributes = True


class ValidityCheckTask(BaseModel):
    id: Optional[int] = None
    task_name: str
    description: Optional[str] = None
    creator_id: str
    status: TaskStatus = TaskStatus.DRAFT
    set_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    pkl_count: int = 0
    assigned_count: int = 0
    completed_count: int = 0

    class Config:
        from_attributes = True


class ValidityCheckAssignment(BaseModel):
    id: Optional[int] = None
    task_id: int
    annotator_id: int
    assigned_by: int
    assigned_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    pkl_count: int = 0
    completed_pkl_count: int = 0

    class Config:
        from_attributes = True


class ValidityCheckResult(BaseModel):
    id: Optional[int] = None
    assignment_id: int
    task_id: int
    pkl_id: int
    annotator_id: int
    validity: Optional[ValidityResult] = None
    reviewer_id: Optional[int] = None
    review_status: ReviewStatus = ReviewStatus.PENDING
    review_time: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    pkl_info: Optional[ValidityCheckTaskPkl] = None

    class Config:
        from_attributes = True


class AnnotatorProgress(BaseModel):
    annotator_id: str
    assigned_pkls: int
    completed_pkls: int
    progress_percentage: float


class TaskProgress(BaseModel):
    task_id: int
    task_name: str
    total_pkls: int
    assigned_pkls: int
    completed_pkls: int
    progress_percentage: float
    annotator_progresses: List[AnnotatorProgress]

# Standard Response Models


class BaseResponse(BaseModel):
    success: bool
    message: str


class TaskListResponse(BaseResponse):
    tasks: List[ValidityCheckTask] = []


class TaskDetailResponse(BaseResponse):
    task: Optional[ValidityCheckTask] = None


class AssignmentListResponse(BaseResponse):
    assignments: List[ValidityCheckAssignment] = []


class PklListResponse(BaseResponse):
    pkls: List[ValidityCheckTaskPkl] = []


class ResultListResponse(BaseResponse):
    results: List[ValidityCheckResult] = []


class ProgressResponse(BaseResponse):
    progress: Optional[TaskProgress] = None


class StatisticsResponse(BaseResponse):
    statistics: dict = {}


class User(BaseModel):
    id: Optional[int] = None
    username: str
    employee_id: str
    password_hash: Optional[str] = None
    role: str = 'user'  # 'admin', 'user', 'annotator'
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# 用户相关的请求和响应模型


class UserListResponse(BaseResponse):
    users: List[User] = []


class UserDetailResponse(BaseResponse):
    user: Optional[User] = None
