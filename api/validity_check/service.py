import csv
import io
from typing import List, Optional, Dict, Any
from datetime import datetime
from api.validity_check.dao import ValidityCheckDAO
from api.validity_check.entity import *
import re


class ValidityCheckService:
    def __init__(self):
        self.dao = ValidityCheckDAO()
        self.vehicle_types = ['B10', 'C10', 'B01', 'C01', 'C11', 'C16']

    def upload_pkls(self, pkls_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量上传PKL文件信息"""
        try:
            pkls = []
            for data in pkls_data:
                pkl = ValidityCheckTaskPkl(
                    pkl_name=data['pkl_name'],
                    pkl_dir=data['pkl_dir'],
                    bag_name=data.get('bag_name'),
                    vehicle_type=VehicleType(data['vehicle_type']) if data.get(
                        'vehicle_type') else None,
                    date=data.get('date') if data.get('date') else None,
                    mode=Mode(data['mode'].upper()) if data.get(
                        'mode') else None,  # 转成全大写
                    version=Version(data['version']) if data.get(
                        'version') else None,
                    scene_tag=data.get('scene_tag'),
                    set_id=data.get('set_id')  # 添加set_id字段
                )
                pkls.append(pkl)

            pkl_ids, id_to_set_id_map = self.dao.batch_create_pkls(pkls)

            # 自动分配PKL到对应任务
            creator_id = "system"  # 或者从上下文获取当前用户ID
            task_pkl_map = self.dao.auto_assign_pkls_to_tasks(
                pkl_ids, id_to_set_id_map, creator_id)

            return {
                'success': True,
                'message': f'成功上传 {len(pkl_ids)} 个PKL文件，自动分配到 {len(task_pkl_map)} 个任务',
                'pkl_ids': pkl_ids,
                'task_assignments': task_pkl_map
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'上传失败: {str(e)}'
            }

    def parse_csv_file(self, csv_content: str) -> List[Dict[str, Any]]:
        """解析CSV文件内容"""
        pkls_data = []
        csv_file = io.StringIO(csv_content)
        reader = csv.DictReader(csv_file)
        bag_name_pattern = r"[A-Z0-9]+_record_data_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}"
        # YYYY_MM_DD_HH_MM_SS 格式的日期
        # 例如：2023_10_01_12_30_00
        # 注意：日期格式在CSV中可能是字符串，需要转换为datetime对象
        date_pattern = r"\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}"
        # vehicle type 一位大写字母+两位数字 一定出现在字符串的开头
        vehicle_type_pattern = r'(' + '|'.join(self.vehicle_types) + r')'
        invalid_rows = []
        for row in reader:
            if row.get('pkl') is None:
                invalid_rows.append(row)
                continue
            bag_name_match = re.search(bag_name_pattern, row['pkl'])

            if not bag_name_match:
                invalid_rows.append(row)
                continue
            # 提取bag_name
            bag_name = bag_name_match.group(0)

            vehicle_type_match = re.search(
                vehicle_type_pattern, row['pkl'])
            # 提取vehicle_type
            if not vehicle_type_match:
                invalid_rows.append(row)
                continue
            vehicle_type = vehicle_type_match.group(0)
            pkl_name = row['pkl'].split('/')[-1]
            pkl_dir = '/'.join(row['pkl'].split('/')[:-1])
            # 处理日期
            date_match = re.search(date_pattern, bag_name)
            if date_match:
                date_str = date_match.group(0)
                # 将日期字符串转换为datetime对象
                date = datetime.strptime(date_str, '%Y_%m_%d_%H_%M_%S')
            else:
                invalid_rows.append(row)
                continue

            pkls_data.append({
                'pkl_name': pkl_name,
                'pkl_dir': pkl_dir,
                'bag_name': bag_name,
                'vehicle_type': vehicle_type,
                'date': date,
                'mode': row.get('mode', ''),
                'version': row.get('version', ''),
                'scene_tag': row.get('scene_tag', ''),
                'set_id': row.get('setid', '')
            })

        return pkls_data, invalid_rows

    def activate_task(self, task_id: int) -> Dict[str, Any]:
        """激活任务"""
        try:
            success = self.dao.update_task_status(task_id, TaskStatus.ACTIVE)
            if success:
                return {'success': True, 'message': '任务已激活'}
            else:
                return {'success': False, 'message': '任务激活失败'}
        except Exception as e:
            return {'success': False, 'message': f'任务激活失败: {str(e)}'}

    def get_tasks(self, creator_id: Optional[str] = None, status: Optional[str] = None) -> List[ValidityCheckTask]:
        """获取任务列表"""
        status_enum = TaskStatus(status) if status else None
        return self.dao.get_tasks(creator_id, status_enum)

    def get_task_detail(self, task_id: int) -> Optional[ValidityCheckTask]:
        """获取任务详情"""
        return self.dao.get_task_by_id(task_id)

    def get_task_assignments(self, task_id: int) -> List[ValidityCheckAssignment]:
        """获取任务的分配情况"""
        return self.dao.get_assignments_by_task_id(task_id)

    def get_annotator_assignments(self, annotator_id: str) -> List[ValidityCheckAssignment]:
        """获取标注员的任务分配"""
        return self.dao.get_assignments_by_annotator(annotator_id)

    def start_annotation(self, assignment_id: int) -> Dict[str, Any]:
        """开始标注"""
        try:
            success = self.dao.update_assignment_start_time(assignment_id)
            if success:
                return {'success': True, 'message': '开始标注'}
            else:
                return {'success': False, 'message': '开始标注失败'}
        except Exception as e:
            return {'success': False, 'message': f'开始标注失败: {str(e)}'}

    def submit_annotation(self, assignment_id: int, task_id: int, pkl_id: int,
                          annotator_id: str, validity: str) -> Dict[str, Any]:
        """提交标注结果"""
        try:
            # 1. 创建标注结果
            result = ValidityCheckResult(
                assignment_id=assignment_id,
                task_id=task_id,
                pkl_id=pkl_id,
                annotator_id=annotator_id,
                validity=ValidityResult(validity)
            )
            result_id = self.dao.create_result(result)

            # 2. 标记PKL为已完成
            self.dao.mark_pkl_completed(task_id, pkl_id, annotator_id)

            return {
                'success': True,
                'message': '标注结果提交成功',
                'result_id': result_id
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'标注结果提交失败: {str(e)}'
            }

    def get_annotation_pkls(self, task_id: int, annotator_id: str) -> List[ValidityCheckTaskPkl]:
        """获取标注员需要标注的PKL列表"""
        return self.dao.get_unassigned_pkls_for_annotator(task_id, annotator_id)

    def get_annotation_results(self, assignment_id: int) -> List[ValidityCheckResult]:
        """获取标注结果"""
        return self.dao.get_results_by_assignment_id(assignment_id)

    def get_task_progress(self, task_id: int) -> Optional[TaskProgress]:
        """获取任务进度"""
        return self.dao.get_task_progress(task_id)

    def get_all_tasks_progress(self, creator_id: Optional[str] = None) -> List[TaskProgress]:
        """获取所有任务进度"""
        tasks = self.get_tasks(creator_id)
        progresses = []
        for task in tasks:
            progress = self.get_task_progress(task.id)
            if progress:
                progresses.append(progress)
        return progresses

    def export_results_to_csv(self, task_id: int) -> str:
        """导出标注结果为CSV"""
        results = self.dao.get_results_by_task_id(task_id)

        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow([
            'PKL名称', 'PKL目录', 'Bag名称', '车辆类型', '日期', '模式', '版本',
            '场景标签', '标注员', '标注结果', '标注时间'
        ])

        # 写入数据
        for result in results:
            pkl = result.pkl_info
            writer.writerow([
                pkl.pkl_name,
                pkl.pkl_dir,
                pkl.bag_name or '',
                pkl.vehicle_type.value if pkl.vehicle_type else '',
                pkl.date.strftime('%Y-%m-%d') if pkl.date else '',
                pkl.mode.value if pkl.mode else '',
                pkl.version.value if pkl.version else '',
                pkl.scene_tag or '',
                result.annotator_id,
                result.validity.value if result.validity else '',
                result.created_at.strftime(
                    '%Y-%m-%d %H:%M:%S') if result.created_at else ''
            ])

        return output.getvalue()

    def get_statistics(self, task_id: Optional[int] = None) -> Dict[str, Any]:
        """获取统计信息"""
        if task_id:
            progress = self.get_task_progress(task_id)
            results = self.dao.get_results_by_task_id(task_id)

            validity_counts = {}
            for result in results:
                if result.validity:
                    validity = result.validity.value
                    validity_counts[validity] = validity_counts.get(
                        validity, 0) + 1

            return {
                'task_progress': progress.dict() if progress else None,
                'validity_distribution': validity_counts,
                'total_results': len(results)
            }
        else:
            all_tasks = self.get_tasks()
            total_tasks = len(all_tasks)
            active_tasks = len(
                [t for t in all_tasks if t.status == TaskStatus.ACTIVE])
            completed_tasks = len(
                [t for t in all_tasks if t.status == TaskStatus.COMPLETED])

            return {
                'total_tasks': total_tasks,
                'active_tasks': active_tasks,
                'completed_tasks': completed_tasks,
                'draft_tasks': total_tasks - active_tasks - completed_tasks
            }

    def get_unassigned_pkls(self) -> List[ValidityCheckTaskPkl]:
        """获取未分配到任务的PKL列表"""
        return self.dao.get_unassigned_pkls()

    def get_annotation_pkls_grouped_by_bag(self, task_id: int, annotator_id: str) -> Dict[str, List[ValidityCheckTaskPkl]]:
        """按bag_name分组获取待标注的PKL列表"""
        pkls = self.dao.get_unassigned_pkls_for_annotator(
            task_id, annotator_id)

        # 按bag_name分组
        grouped_pkls = {}
        for pkl in pkls:
            bag_name = pkl.bag_name or 'unknown'
            if bag_name not in grouped_pkls:
                grouped_pkls[bag_name] = []
            grouped_pkls[bag_name].append(pkl)

        return grouped_pkls

    def get_annotator_assignments_with_progress(self, annotator_id: str) -> List[Dict[str, Any]]:
        """获取标注员的任务分配（包含进度信息）"""
        assignments = self.dao.get_assignments_by_annotator(annotator_id)
        result = []

        for assignment in assignments:
            # 获取任务详情
            task = self.dao.get_task_by_id(assignment.task_id)
            if not task:
                continue

            # 计算PKL总数
            total_pkls = self.dao.count_pkls_in_task(assignment.task_id)

            # 计算已完成的PKL数量
            completed_pkls = self.dao.count_completed_pkls_by_annotator(
                assignment.task_id, annotator_id
            )

            result.append({
                'id': assignment.id,
                'task_id': assignment.task_id,
                'task_name': task.task_name,
                'assigned_at': assignment.assigned_at,
                'started_at': assignment.started_at,
                'completed_at': assignment.completed_at,
                'pkl_count': total_pkls,
                'completed_pkl_count': completed_pkls
            })

        return result

    def get_tasks_with_stats(self, creator_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取任务列表（包含统计信息）"""
        tasks = self.get_tasks(creator_id)
        result = []

        for task in tasks:
            # 获取PKL数量
            pkl_count = self.dao.count_pkls_in_task(task.id)

            # 获取已分配数量（有assignment的PKL数量）
            assigned_count = self.dao.count_assigned_pkls_in_task(task.id)

            # 获取已完成数量
            completed_count = self.dao.count_completed_pkls_in_task(task.id)

            result.append({
                'id': task.id,
                'task_name': task.task_name,
                'description': task.description,
                'creator_id': task.creator_id,
                'status': task.status.value,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'pkl_count': pkl_count,
                'assigned_count': assigned_count,
                'completed_count': completed_count
            })

        return result

    def get_task_progress_detailed(self, task_id: int) -> Dict[str, Any]:
        """获取任务的详细进度信息"""
        try:
            # 获取任务基本信息
            task = self.dao.get_task_by_id(task_id)
            if not task:
                return {'success': False, 'message': '任务不存在'}

            # 获取任务分配情况
            assignments = self.dao.get_assignments_by_task_id(task_id)

            # 获取PKL总数
            total_pkls = self.dao.count_pkls_in_task(task_id)

            # 获取已分配PKL数量
            assigned_pkls = self.dao.count_assigned_pkls_in_task(task_id)

            # 获取已完成PKL数量
            completed_pkls = self.dao.count_completed_pkls_in_task(task_id)

            # 计算每个标注员的进度
            annotator_progresses = []
            for assignment in assignments:
                annotator_completed = self.dao.count_completed_pkls_by_annotator(
                    task_id, assignment.annotator_id
                )
                annotator_assigned = self.dao.count_assigned_pkls_for_annotator(
                    task_id, assignment.annotator_id
                )

                progress_percentage = (
                    annotator_completed / annotator_assigned * 100) if annotator_assigned > 0 else 0

                annotator_progresses.append({
                    'annotator_id': assignment.annotator_id,
                    'assigned_pkls': annotator_assigned,
                    'completed_pkls': annotator_completed,
                    'progress_percentage': progress_percentage
                })

            progress_percentage = (
                completed_pkls / total_pkls * 100) if total_pkls > 0 else 0

            return {
                'success': True,
                'data': {
                    'task_id': task_id,
                    'task_name': task.task_name,
                    'total_pkls': total_pkls,
                    'assigned_pkls': assigned_pkls,
                    'completed_pkls': completed_pkls,
                    'progress_percentage': progress_percentage,
                    'annotator_progresses': annotator_progresses
                }
            }

        except Exception as e:
            return {'success': False, 'message': f'获取进度失败: {str(e)}'}

    def check_annotation_completion(self, assignment_id: int) -> Dict[str, Any]:
        """检查标注任务是否完成"""
        try:
            assignment = self.dao.get_assignment_by_id(assignment_id)
            if not assignment:
                return {'success': False, 'message': '分配任务不存在'}

            # 获取该标注员需要标注的PKL总数
            total_pkls = self.dao.count_assigned_pkls_for_annotator(
                assignment.task_id, assignment.annotator_id
            )

            # 获取已完成的PKL数量
            completed_pkls = self.dao.count_completed_pkls_by_annotator(
                assignment.task_id, assignment.annotator_id
            )

            is_completed = completed_pkls >= total_pkls

            # 如果完成，更新assignment的完成时间
            if is_completed and not assignment.completed_at:
                self.dao.update_assignment_completed_time(assignment_id)

            return {
                'success': True,
                'is_completed': is_completed,
                'total_pkls': total_pkls,
                'completed_pkls': completed_pkls
            }

        except Exception as e:
            return {'success': False, 'message': f'检查完成状态失败: {str(e)}'}

    def get_overall_statistics(self) -> Dict[str, Any]:
        """获取整体统计信息"""
        try:
            # 获取所有任务统计
            all_tasks = self.get_tasks()
            total_tasks = len(all_tasks)

            active_tasks = len(
                [t for t in all_tasks if t.status == TaskStatus.ACTIVE])
            completed_tasks = len(
                [t for t in all_tasks if t.status == TaskStatus.COMPLETED])
            draft_tasks = len(
                [t for t in all_tasks if t.status == TaskStatus.DRAFT])

            # 获取未分配PKL数量
            unassigned_pkls = len(self.get_unassigned_pkls())

            # 获取总PKL数量
            total_pkls = self.dao.count_total_pkls()

            # 获取已完成标注的PKL数量
            total_completed_annotations = self.dao.count_total_completed_annotations()

            return {
                'success': True,
                'data': {
                    'total_tasks': total_tasks,
                    'active_tasks': active_tasks,
                    'completed_tasks': completed_tasks,
                    'draft_tasks': draft_tasks,
                    'unassigned_pkls': unassigned_pkls,
                    'total_pkls': total_pkls,
                    'total_completed_annotations': total_completed_annotations
                }
            }

        except Exception as e:
            return {'success': False, 'message': f'获取统计信息失败: {str(e)}'}

    def get_users(self, role: Optional[str] = None) -> List[User]:
        """获取用户列表"""
        return self.dao.get_users_by_role(role)

    def get_annotators(self) -> List[User]:
        """获取所有标注员"""
        return self.dao.get_users_by_role('annotator')

    def get_user_by_employee_id(self, employee_id: str) -> Optional[User]:
        """根据工号获取用户"""
        return self.dao.get_user_by_employee_id(employee_id)

    def _distribute_pkls(self, pkls: List[ValidityCheckTaskPkl], annotator_ids: List[str]) -> Dict[str, List[int]]:
        """平均分配PKL给标注员"""
        pkl_ids = [pkl.id for pkl in pkls]
        annotator_count = len(annotator_ids)

        # 初始化分配字典
        distribution = {annotator_id: [] for annotator_id in annotator_ids}

        # 平均分配
        for i, pkl_id in enumerate(pkl_ids):
            annotator_index = i % annotator_count
            annotator_id = annotator_ids[annotator_index]
            distribution[annotator_id].append(pkl_id)

        return distribution
    def export_all_results_to_csv(self, exclude_valid: bool = True) -> str:
        """导出所有任务的标注结果为CSV"""
        results = self.dao.get_all_results_excluding_valid(exclude_valid)

        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        writer.writerow([
            '任务名称', 'PKL名称', 'PKL目录', 'Bag名称', '车辆类型', '日期', '模式', '版本',
            '场景标签', '标注员', '标注结果', '标注时间'
        ])

        # 写入数据
        for result in results:
            pkl = result.pkl_info
            writer.writerow([
                getattr(result, 'task_name', ''),
                pkl.pkl_name,
                pkl.pkl_dir,
                pkl.bag_name or '',
                pkl.vehicle_type.value if pkl.vehicle_type else '',
                pkl.date.strftime('%Y-%m-%d') if pkl.date else '',
                pkl.mode.value if pkl.mode else '',
                pkl.version.value if pkl.version else '',
                pkl.scene_tag or '',
                result.annotator_id,
                result.validity.value if result.validity else '',
                result.created_at.strftime('%Y-%m-%d %H:%M:%S') if result.created_at else ''
            ])

        return output.getvalue()
    def get_annotator_detailed_progress(self, task_id: int, annotator_id: str) -> Dict[str, Any]:
        """获取标注员的详细进度信息"""
        try:
            progress = self.dao.get_annotator_progress_detailed(
                task_id, annotator_id)
            task = self.dao.get_task_by_id(task_id)

            return {
                'task_id': task_id,
                'task_name': task.task_name if task else 'Unknown',
                'annotator_id': annotator_id,
                **progress
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'获取进度失败: {str(e)}'
            }

    def get_task_assignment_status(self, task_id: int) -> Dict[str, Any]:
        """获取任务分配状态"""
        return self.dao.get_task_assignment_status(task_id)

    def assign_task_to_annotators_with_incremental_distribution(
        self,
        task_id: int,
        annotator_ids: List[str],
        assigned_by: str,
        pkl_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """增量分配任务给标注员，只分配未分配的PKL"""
        try:
            # 1. 获取当前分配状态
            status = self.dao.get_task_assignment_status(task_id)
            unassigned_pkl_ids = self.dao.get_unassigned_pkl_ids_in_task(
                task_id)

            if not unassigned_pkl_ids:
                return {
                    'success': False,
                    'message': '没有未分配的PKL可以分配'
                }

            # 2. 确定要分配的PKL数量
            if pkl_count is not None:
                pkl_count = min(pkl_count, len(unassigned_pkl_ids))
                pkls_to_assign = unassigned_pkl_ids[:pkl_count]
            else:
                pkls_to_assign = unassigned_pkl_ids

            # 3. 为新的标注员创建分配记录（如果不存在）
            existing_annotators = set(a['annotator_id']
                                      for a in status['annotator_assignments'])
            new_annotators = [
                aid for aid in annotator_ids if aid not in existing_annotators]

            for annotator_id in new_annotators:
                assignment = ValidityCheckAssignment(
                    task_id=task_id,
                    annotator_id=annotator_id,
                    assigned_by=assigned_by
                )
                self.dao.create_assignment(assignment)

            # 4. 平均分配PKL给所有标注员（包括已有的）
            all_annotators = list(
                set(annotator_ids + [a['annotator_id'] for a in status['annotator_assignments']]))
            annotator_assignments = self._distribute_pkls_to_list(
                pkls_to_assign, all_annotators)

            # 5. 保存PKL分配关系
            success = self.dao.assign_pkls_to_annotators(
                task_id, annotator_assignments)

            if success:
                return {
                    'success': True,
                    'message': f'成功增量分配 {len(pkls_to_assign)} 个PKL给 {len(all_annotators)} 个标注员',
                    'assigned_pkl_count': len(pkls_to_assign),
                    'total_annotators': len(all_annotators),
                    'pkl_distribution': {
                        annotator_id: len(pkl_ids)
                        for annotator_id, pkl_ids in annotator_assignments.items()
                    }
                }
            else:
                return {
                    'success': False,
                    'message': 'PKL分配失败'
                }

        except Exception as e:
            return {
                'success': False,
                'message': f'分配失败: {str(e)}'
            }

    def _distribute_pkls_to_list(self, pkl_ids: List[int], annotator_ids: List[str]) -> Dict[str, List[int]]:
        """将PKL列表平均分配给标注员列表"""
        if not annotator_ids:
            return {}

        result = {annotator_id: [] for annotator_id in annotator_ids}

        for i, pkl_id in enumerate(pkl_ids):
            annotator_id = annotator_ids[i % len(annotator_ids)]
            result[annotator_id].append(pkl_id)

        return result

    def get_annotator_assignments_with_progress(self, annotator_id: str) -> List[Dict[str, Any]]:
        """获取标注员的任务分配（带进度信息和标注结果）"""
        assignments = self.dao.get_assignments_by_annotator(annotator_id)
        result = []

        for assignment in assignments:
            # 获取分配给该标注员的PKL（包括标注结果）
            pkls = self.dao.get_all_assigned_pkls_for_annotator_with_results(
                assignment.task_id, annotator_id
            )

            # 按bag_name分组
            grouped_by_bag = {}
            for pkl in pkls:
                bag_name = pkl.bag_name or 'unknown'
                if bag_name not in grouped_by_bag:
                    grouped_by_bag[bag_name] = []
                grouped_by_bag[bag_name].append(pkl)

            result.append({
                'assignment_id': assignment.id,
                'task_id': assignment.task_id,
                'task_name': f'任务{assignment.task_id}',
                'annotator_id': assignment.annotator_id,
                'assigned_at': assignment.assigned_at.strftime('%Y-%m-%d %H:%M:%S') if assignment.assigned_at else None,
                'started_at': assignment.started_at.strftime('%Y-%m-%d %H:%M:%S') if assignment.started_at else None,
                'completed_at': assignment.completed_at.strftime('%Y-%m-%d %H:%M:%S') if assignment.completed_at else None,
                'total_pkls': len(pkls),
                'completed_pkls': len([pkl for pkl in pkls if pkl.is_completed]),
                'bag_groups': grouped_by_bag
            })

        return result

    def get_annotation_pkls_with_results(self, task_id: int, annotator_id: str) -> Dict[str, List[Dict]]:
        """按bag_name分组获取待标注的PKL列表（包括标注结果）"""
        pkls = self.dao.get_all_assigned_pkls_for_annotator_with_results(
            task_id, annotator_id)

        grouped = {}
        for pkl in pkls:
            bag_name = pkl.bag_name or 'unknown'
            if bag_name not in grouped:
                grouped[bag_name] = []

            pkl_dict = {
                'id': pkl.id,
                'pkl_name': pkl.pkl_name,
                'pkl_dir': pkl.pkl_dir,
                'bag_name': pkl.bag_name,
                'vehicle_type': pkl.vehicle_type.value if pkl.vehicle_type else None,
                'date': pkl.date.strftime('%Y-%m-%d') if pkl.date else None,
                'mode': pkl.mode.value if pkl.mode else None,
                'version': pkl.version.value if pkl.version else None,
                'scene_tag': pkl.scene_tag,
                'is_completed': pkl.is_completed,
                'annotation_result': pkl.annotation_result,
                'annotation_time': pkl.annotation_time.strftime('%Y-%m-%d %H:%M:%S') if pkl.annotation_time else None
            }
            grouped[bag_name].append(pkl_dict)

        return grouped

    def get_annotators_progress(self, start_date: str, end_date: str) -> List[Dict]:
        """获取所有标注员的进度统计"""
        annotator_progresses = []

        # 获取在指定日期范围内有标注活动的所有标注员
        active_annotators = self.dao.get_active_annotators_in_date_range(
            start_date, end_date)

        for annotator in active_annotators:
            annotator_id = annotator.id

            # 获取标注员的所有任务分配
            assignments = self.dao.get_annotator_assignments(annotator_id)

            tasks_progress = []
            total_assigned_pkls = 0
            total_completed_pkls = 0
            total_bag_groups = 0
            total_completed_bag_groups = 0

            for assignment in assignments:
                task_id = assignment['task_id']

                # 获取该标注员在该任务中的PKL进度
                task_progress = self.dao.get_annotator_task_progress(
                    task_id, annotator_id)

                # 获取BagGroup进度
                bag_groups_progress = self.dao.get_annotator_bag_groups_progress(
                    task_id, annotator_id)

                completed_bag_groups = sum(
                    1 for bg in bag_groups_progress if bg['is_completed'])

                task_data = {
                    'task_id': task_id,
                    'task_name': assignment['task_name'],
                    'total_assigned_pkls': task_progress['total_assigned'],
                    'completed_pkls': task_progress['completed'],
                    'total_bag_groups': len(bag_groups_progress),
                    'completed_bag_groups': completed_bag_groups,
                    'progress_percentage': task_progress['progress_percentage'],
                    'bag_groups': bag_groups_progress
                }

                tasks_progress.append(task_data)

                total_assigned_pkls += task_progress['total_assigned']
                total_completed_pkls += task_progress['completed']
                total_bag_groups += len(bag_groups_progress)
                total_completed_bag_groups += completed_bag_groups

            # 获取每日进度
            daily_progress = self.dao.get_annotator_daily_progress(
                annotator_id, start_date, end_date)

            overall_progress_percentage = (
                round((total_completed_pkls / total_assigned_pkls) * 100)
                if total_assigned_pkls > 0 else 0
            )

            annotator_data = {
                'annotator_id': str(annotator_id),
                'annotator_name': annotator.username,
                'employee_id': annotator.employee_id,
                'tasks': tasks_progress,
                'daily_progress': daily_progress,
                'total_assigned_pkls': total_assigned_pkls,
                'total_completed_pkls': total_completed_pkls,
                'total_bag_groups': total_bag_groups,
                'total_completed_bag_groups': total_completed_bag_groups,
                'overall_progress_percentage': overall_progress_percentage
            }

            annotator_progresses.append(annotator_data)

        return annotator_progresses
