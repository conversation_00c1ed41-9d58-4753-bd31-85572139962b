from pydantic import BaseModel, Field
from datetime import datetime
from datetime import date as date_
from typing import Optional, List
from enum import Enum

from api.common.entity import Mode, Version


class PickleUtilityEnum(str, Enum):
    FULL = "全量"
    SCORER = "打分器"
    ANNOTATION = "打点"
    PREDICTION = "预测"


# Request Models
class BagInfoCreateRequest(BaseModel):
    bag_name: str = Field(..., max_length=255, description="bag文件名称，保证唯一")
    translated_dir: Optional[str] = Field(
        None, max_length=500, description="翻译目录路径")
    img_dir: Optional[str] = Field(None, max_length=500, description="图像目录路径")
    set_id: Optional[str] = Field(None, max_length=100, description="数据集ID")
    date: date_ = Field(..., description="数据日期")
    vin: str = Field(..., max_length=100, description="车辆识别号")
    version: Version = Field(..., description="版本号")
    mode: Mode = Field(..., description="模式")
    redmine_tag: Optional[str] = Field(
        None, max_length=200, description="Redmine标签")
    origin_dir: str = Field(..., max_length=500, description="原始目录路径")


class BagInfoUpdateRequest(BaseModel):
    translated_dir: Optional[str] = Field(
        None, max_length=500, description="翻译目录路径")
    img_dir: Optional[str] = Field(None, max_length=500, description="图像目录路径")
    set_id: Optional[str] = Field(None, max_length=100, description="数据集ID")
    date: Optional[date_] = Field(None, description="数据日期")
    vin: Optional[str] = Field(None, max_length=100, description="车辆识别号")
    version: Optional[Version] = Field(None, description="版本号")
    mode: Optional[Mode] = Field(None, description="模式")
    redmine_tag: Optional[str] = Field(
        None, max_length=200, description="Redmine标签")
    origin_dir: Optional[str] = Field(
        None, max_length=500, description="原始目录路径")


class PickleInfoCreateRequest(BaseModel):
    bag_name: str = Field(..., max_length=255, description="关联的bag文件名称")
    pkl_dir: str = Field(..., max_length=500, description="pickle文件目录路径")
    pkl_utility: PickleUtilityEnum = Field(..., description="pickle文件用途")
    date: date_ = Field(..., description="数据日期")
    version: Optional[str] = Field(None, max_length=255, description="版本号")


class PickleInfoUpdateRequest(BaseModel):
    pkl_dir: Optional[str] = Field(
        None, max_length=500, description="pickle文件目录路径")
    pkl_utility: Optional[PickleUtilityEnum] = Field(
        None, description="pickle文件用途")
    date: Optional[date_] = Field(None, description="数据日期")
    version: Optional[str] = Field(None, max_length=255, description="版本号")


# Entity Models
class BagInfo(BaseModel):
    id: Optional[int] = None
    bag_name: str = Field(..., max_length=255, description="bag文件名称，保证唯一")
    translated_dir: Optional[str] = Field(
        None, max_length=500, description="翻译目录路径")
    img_dir: Optional[str] = Field(None, max_length=500, description="图像目录路径")
    set_id: Optional[str] = Field(None, max_length=100, description="数据集ID")
    date: date_ = Field(..., description="数据日期")
    vin: str = Field(..., max_length=100, description="车辆识别号")
    version: Version = Field(..., description="版本号")
    mode: Mode = Field(..., description="模式")
    redmine_tag: Optional[str] = Field(
        None, max_length=200, description="Redmine标签")
    origin_dir: str = Field(..., max_length=500, description="原始目录路径")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PickleInfo(BaseModel):
    id: Optional[int] = None
    bag_name: str = Field(..., max_length=255, description="关联的bag文件名称")
    pkl_dir: str = Field(..., max_length=500, description="pickle文件目录路径")
    pkl_utility: PickleUtilityEnum = Field(..., description="pickle文件用途")
    date: date_ = Field(..., description="数据日期")
    version: Optional[str] = Field(None, max_length=255, description="版本号")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# Response Models
class BaseResponse(BaseModel):
    success: bool
    message: str


class BagInfoListResponse(BaseResponse):
    data: List[BagInfo] = []
    total: int = 0
    page: int = 1
    page_size: int = 20


class BagInfoDetailResponse(BaseResponse):
    data: Optional[BagInfo] = None


class PickleInfoListResponse(BaseResponse):
    data: List[PickleInfo] = []
    total: int = 0
    page: int = 1
    page_size: int = 20


class PickleInfoDetailResponse(BaseResponse):
    data: Optional[PickleInfo] = None


# 视图查询结果模型
class BagPickleViewItem(BaseModel):
    bag_name: str
    translated_dir: Optional[str] = None
    img_dir: Optional[str] = None
    set_id: Optional[str] = None
    bag_date: date_
    vin: str
    bag_version: Version
    mode: Mode
    redmine_tag: Optional[str] = None
    origin_dir: str
    pkl_dir: Optional[str] = None
    pkl_utility: Optional[PickleUtilityEnum] = None
    pkl_date: Optional[date_] = None
    pkl_version: Optional[str] = None


class BagPickleViewResponse(BaseResponse):
    data: List[BagPickleViewItem] = []
    total: int = 0
    page: int = 1
    page_size: int = 20
