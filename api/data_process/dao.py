from typing import List, Optional, Dict, Any
from datetime import datetime, date
from .entity import *
from database.db_operations import get_db_connection


class DataProcessDAO:
    def __init__(self):
        pass

    def get_connection(self):
        return get_db_connection(database='autonomous_driving_data',)

    # BagInfo 相关操作
    def create_bag_info(self, bag_info: BagInfoCreateRequest) -> int:
        """创建bag信息记录"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = """
            INSERT INTO bag_infos (bag_name, translated_dir, img_dir, set_id, date, 
                                 vin, version, mode, redmine_tag, origin_dir)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                bag_info.bag_name, bag_info.translated_dir, bag_info.img_dir,
                bag_info.set_id, bag_info.date, bag_info.vin, bag_info.version.value,
                bag_info.mode.value, bag_info.redmine_tag, bag_info.origin_dir
            ))
            conn.commit()
            return cursor.lastrowid
        finally:
            cursor.close()
            conn.close()

    def get_bag_info_by_id(self, bag_id: int) -> Optional[BagInfo]:
        """根据ID获取bag信息"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT * FROM bag_infos WHERE id = %s"
            cursor.execute(sql, (bag_id,))
            row = cursor.fetchone()
            return self._row_to_bag_info(row) if row else None
        finally:
            cursor.close()
            conn.close()

    def get_bag_info_by_name(self, bag_name: str) -> Optional[BagInfo]:
        """根据bag名称获取信息"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT * FROM bag_infos WHERE bag_name = %s"
            cursor.execute(sql, (bag_name,))
            row = cursor.fetchone()
            return self._row_to_bag_info(row) if row else None
        finally:
            cursor.close()
            conn.close()

    def get_bag_infos(self, offset: int = 0, limit: int = 20, 
                     version: str = None, mode: str = None, vin: str = None,
                     date_from: str = None, date_to: str = None) -> List[BagInfo]:
        """获取bag信息列表（支持分页和筛选）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            conditions = []
            params = []
            
            # 构建查询条件
            if version:
                conditions.append("version = %s")
                params.append(version)
            if mode:
                conditions.append("mode = %s")
                params.append(mode)
            if vin:
                conditions.append("vin LIKE %s")
                params.append(f"%{vin}%")
            if date_from:
                conditions.append("date >= %s")
                params.append(date_from)
            if date_to:
                conditions.append("date <= %s")
                params.append(date_to)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            sql = f"""
            SELECT * FROM bag_infos 
            WHERE {where_clause} 
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            return [self._row_to_bag_info(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def count_bag_infos(self, version: str = None, mode: str = None, vin: str = None,
                       date_from: str = None, date_to: str = None) -> int:
        """计算bag信息总数"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            conditions = []
            params = []
            
            if version:
                conditions.append("version = %s")
                params.append(version)
            if mode:
                conditions.append("mode = %s")
                params.append(mode)
            if vin:
                conditions.append("vin LIKE %s")
                params.append(f"%{vin}%")
            if date_from:
                conditions.append("date >= %s")
                params.append(date_from)
            if date_to:
                conditions.append("date <= %s")
                params.append(date_to)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            sql = f"SELECT COUNT(*) as count FROM bag_infos WHERE {where_clause}"
            cursor.execute(sql, params)
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def update_bag_info(self, bag_id: int, bag_info: BagInfoUpdateRequest) -> bool:
        """更新bag信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            update_fields = []
            values = []
            
            for field, value in bag_info.dict(exclude_unset=True).items():
                if value is not None:
                    update_fields.append(f"{field} = %s")
                    if hasattr(value, 'value'):  # 处理枚举类型
                        values.append(value.value)
                    else:
                        values.append(value)
            
            if not update_fields:
                return False
            
            values.append(bag_id)
            
            sql = f"UPDATE bag_infos SET {', '.join(update_fields)} WHERE id = %s"
            cursor.execute(sql, values)
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    def delete_bag_info(self, bag_id: int) -> bool:
        """删除bag信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = "DELETE FROM bag_infos WHERE id = %s"
            cursor.execute(sql, (bag_id,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    # PickleInfo 相关操作
    def create_pickle_info(self, pickle_info: PickleInfoCreateRequest) -> int:
        """创建pickle信息记录"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = """
            INSERT INTO pickle_infos (bag_name, pkl_dir, pkl_utility, date, version)
            VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                pickle_info.bag_name, pickle_info.pkl_dir, 
                pickle_info.pkl_utility.value, pickle_info.date, 
                pickle_info.version
            ))
            conn.commit()
            return cursor.lastrowid
        finally:
            cursor.close()
            conn.close()

    def get_pickle_info_by_id(self, pickle_id: int) -> Optional[PickleInfo]:
        """根据ID获取pickle信息"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT * FROM pickle_infos WHERE id = %s"
            cursor.execute(sql, (pickle_id,))
            row = cursor.fetchone()
            return self._row_to_pickle_info(row) if row else None
        finally:
            cursor.close()
            conn.close()

    def get_pickle_infos_by_bag_name(self, bag_name: str) -> List[PickleInfo]:
        """根据bag名称获取所有相关pickle信息"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            sql = "SELECT * FROM pickle_infos WHERE bag_name = %s ORDER BY created_at DESC"
            cursor.execute(sql, (bag_name,))
            rows = cursor.fetchall()
            return [self._row_to_pickle_info(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def get_pickle_infos(self, offset: int = 0, limit: int = 20,
                        bag_name: str = None, pkl_utility: str = None,
                        version: str = None, date_from: str = None, 
                        date_to: str = None) -> List[PickleInfo]:
        """获取pickle信息列表（支持分页和筛选）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            conditions = []
            params = []
            
            if bag_name:
                conditions.append("bag_name LIKE %s")
                params.append(f"%{bag_name}%")
            if pkl_utility:
                conditions.append("pkl_utility = %s")
                params.append(pkl_utility)
            if version:
                conditions.append("version = %s")
                params.append(version)
            if date_from:
                conditions.append("date >= %s")
                params.append(date_from)
            if date_to:
                conditions.append("date <= %s")
                params.append(date_to)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            sql = f"""
            SELECT * FROM pickle_infos 
            WHERE {where_clause} 
            ORDER BY created_at DESC 
            LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            return [self._row_to_pickle_info(row) for row in rows]
        finally:
            cursor.close()
            conn.close()

    def count_pickle_infos(self, bag_name: str = None, pkl_utility: str = None,
                          version: str = None, date_from: str = None, 
                          date_to: str = None) -> int:
        """计算pickle信息总数"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            conditions = []
            params = []
            
            if bag_name:
                conditions.append("bag_name LIKE %s")
                params.append(f"%{bag_name}%")
            if pkl_utility:
                conditions.append("pkl_utility = %s")
                params.append(pkl_utility)
            if version:
                conditions.append("version = %s")
                params.append(version)
            if date_from:
                conditions.append("date >= %s")
                params.append(date_from)
            if date_to:
                conditions.append("date <= %s")
                params.append(date_to)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            sql = f"SELECT COUNT(*) as count FROM pickle_infos WHERE {where_clause}"
            cursor.execute(sql, params)
            result = cursor.fetchone()
            return result['count'] if result else 0
        finally:
            cursor.close()
            conn.close()

    def update_pickle_info(self, pickle_id: int, pickle_info: PickleInfoUpdateRequest) -> bool:
        """更新pickle信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            update_fields = []
            values = []
            
            for field, value in pickle_info.dict(exclude_unset=True).items():
                if value is not None:
                    update_fields.append(f"{field} = %s")
                    if hasattr(value, 'value'):  # 处理枚举类型
                        values.append(value.value)
                    else:
                        values.append(value)
            
            if not update_fields:
                return False
            
            values.append(pickle_id)
            
            sql = f"UPDATE pickle_infos SET {', '.join(update_fields)} WHERE id = %s"
            cursor.execute(sql, values)
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    def delete_pickle_info(self, pickle_id: int) -> bool:
        """删除pickle信息"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            sql = "DELETE FROM pickle_infos WHERE id = %s"
            cursor.execute(sql, (pickle_id,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            cursor.close()
            conn.close()

    # 视图查询
    def get_bag_pickle_view(self, offset: int = 0, limit: int = 20,
                           bag_name: str = None, version: str = None, mode: str = None,
                           vin: str = None, pkl_utility: str = None,
                           date_from: str = None, date_to: str = None) -> List[BagPickleViewItem]:
        """从视图获取bag和pickle信息（支持分页和筛选）"""
        conn = self.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        try:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if bag_name:
                where_conditions.append("bag_name LIKE %s")
                params.append(f"%{bag_name}%")
            
            if version:
                where_conditions.append("bag_version = %s")
                params.append(version)
            
            if mode:
                where_conditions.append("mode = %s")
                params.append(mode)
            
            if vin:
                where_conditions.append("vin LIKE %s")
                params.append(f"%{vin}%")
            
            if pkl_utility:
                where_conditions.append("pkl_utility = %s")
                params.append(pkl_utility)
            
            if date_from:
                where_conditions.append("bag_date >= %s")
                params.append(date_from)
            
            if date_to:
                where_conditions.append("bag_date <= %s")
                params.append(date_to)
            
            # 构建SQL查询
            query = "SELECT * FROM bag_pickle_view"
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            
            query += " ORDER BY bag_date DESC LIMIT %s OFFSET %s"
            params.extend([limit, offset])
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            return [BagPickleViewItem(**result) for result in results]
            
        finally:
            cursor.close()
            conn.close()
    
    def get_bag_pickle_view_count(self, bag_name: str = None, version: str = None, mode: str = None,
                                 vin: str = None, pkl_utility: str = None,
                                 date_from: str = None, date_to: str = None) -> int:
        """获取视图查询结果总数（支持筛选）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if bag_name:
                where_conditions.append("bag_name LIKE %s")
                params.append(f"%{bag_name}%")
            
            if version:
                where_conditions.append("bag_version = %s")
                params.append(version)
            
            if mode:
                where_conditions.append("mode = %s")
                params.append(mode)
            
            if vin:
                where_conditions.append("vin LIKE %s")
                params.append(f"%{vin}%")
            
            if pkl_utility:
                where_conditions.append("pkl_utility = %s")
                params.append(pkl_utility)
            
            if date_from:
                where_conditions.append("bag_date >= %s")
                params.append(date_from)
            
            if date_to:
                where_conditions.append("bag_date <= %s")
                params.append(date_to)
            
            # 构建SQL查询
            query = "SELECT COUNT(*) FROM bag_pickle_view"
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
            
            cursor.execute(query, params)
            count = cursor.fetchone()[0]
            
            return count
            
        finally:
            cursor.close()
            conn.close()
    # 辅助方法
    def _row_to_bag_info(self, row: dict) -> BagInfo:
        """将数据库行转换为BagInfo对象"""
        return BagInfo(
            id=row['id'],
            bag_name=row['bag_name'],
            translated_dir=row['translated_dir'],
            img_dir=row['img_dir'],
            set_id=row['set_id'],
            date=row['date'],
            vin=row['vin'],
            version=VersionEnum(row['version']),
            mode=ModeEnum(row['mode']),
            redmine_tag=row['redmine_tag'],
            origin_dir=row['origin_dir'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )

    def _row_to_pickle_info(self, row: dict) -> PickleInfo:
        """将数据库行转换为PickleInfo对象"""
        return PickleInfo(
            id=row['id'],
            bag_name=row['bag_name'],
            pkl_dir=row['pkl_dir'],
            pkl_utility=PickleUtilityEnum(row['pkl_utility']),
            date=row['date'],
            version=row['version'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )