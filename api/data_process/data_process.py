from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional
from datetime import date
from .service import DataProcessService
from .entity import *

router = APIRouter(prefix="/api/data_process", tags=["data_process"])

# 初始化服务
service = DataProcessService()

# BagInfo 相关接口


@router.post("/bag_infos", response_model=BaseResponse)
async def create_bag_info(bag_info: BagInfoCreateRequest):
    """创建bag信息"""
    try:
        result = service.create_bag_info(bag_info)
        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bag_infos/{bag_id}", response_model=BagInfoDetailResponse)
async def get_bag_info(bag_id: int):
    """获取bag信息"""
    try:
        result = service.get_bag_info(bag_id)
        if result['success']:
            return BagInfoDetailResponse(
                success=True,
                message=result['message'],
                data=result['data']
            )
        else:
            raise HTTPException(status_code=404, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bag_infos/name/{bag_name}", response_model=BagInfoDetailResponse)
async def get_bag_info_by_name(bag_name: str):
    """根据名称获取bag信息"""
    try:
        result = service.get_bag_info_by_name(bag_name)
        if result['success']:
            return BagInfoDetailResponse(
                success=True,
                message=result['message'],
                data=result['data']
            )
        else:
            raise HTTPException(status_code=404, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bag_infos", response_model=BagInfoListResponse)
async def list_bag_infos(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    version: Optional[str] = Query(None),
    mode: Optional[str] = Query(None),
    vin: Optional[str] = Query(None),
    date_from: Optional[date] = Query(None),
    date_to: Optional[date] = Query(None)
):
    """获取bag信息列表"""
    try:
        result = service.list_bag_infos(
            page=page, page_size=page_size,
            version=version, mode=mode, vin=vin,
            date_from=str(date_from) if date_from else None,
            date_to=str(date_to) if date_to else None
        )

        return BagInfoListResponse(
            success=True,
            message=result['message'],
            data=result['data'],
            total=result['total'],
            page=result['page'],
            page_size=result['page_size']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/bag_infos/{bag_id}", response_model=BaseResponse)
async def update_bag_info(bag_id: int, bag_info: BagInfoUpdateRequest):
    """更新bag信息"""
    try:
        result = service.update_bag_info(bag_id, bag_info)
        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/bag_infos/{bag_id}", response_model=BaseResponse)
async def delete_bag_info(bag_id: int):
    """删除bag信息"""
    try:
        result = service.delete_bag_info(bag_id)
        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=404, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# PickleInfo 相关接口
@router.post("/pickle_infos", response_model=BaseResponse)
async def create_pickle_info(pickle_info: PickleInfoCreateRequest):
    """创建pickle信息"""
    try:
        result = service.create_pickle_info(pickle_info)
        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pickle_infos/{pickle_id}", response_model=PickleInfoDetailResponse)
async def get_pickle_info(pickle_id: int):
    """获取pickle信息"""
    try:
        result = service.get_pickle_info(pickle_id)
        if result['success']:
            return PickleInfoDetailResponse(
                success=True,
                message=result['message'],
                data=result['data']
            )
        else:
            raise HTTPException(status_code=404, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pickle_infos/bag/{bag_name}", response_model=PickleInfoListResponse)
async def get_pickles_by_bag(bag_name: str):
    """根据bag名称获取所有相关pickle信息"""
    try:
        result = service.get_pickles_by_bag(bag_name)
        return PickleInfoListResponse(
            success=True,
            message=result['message'],
            data=result['data'],
            total=len(result['data'])
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pickle_infos", response_model=PickleInfoListResponse)
async def list_pickle_infos(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    bag_name: Optional[str] = Query(None),
    pkl_utility: Optional[str] = Query(None),
    version: Optional[str] = Query(None),
    date_from: Optional[date] = Query(None),
    date_to: Optional[date] = Query(None)
):
    """获取pickle信息列表"""
    try:
        result = service.list_pickle_infos(
            page=page, page_size=page_size,
            bag_name=bag_name, pkl_utility=pkl_utility,
            version=version,
            date_from=str(date_from) if date_from else None,
            date_to=str(date_to) if date_to else None
        )

        return PickleInfoListResponse(
            success=True,
            message=result['message'],
            data=result['data'],
            total=result['total'],
            page=result['page'],
            page_size=result['page_size']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/pickle_infos/{pickle_id}", response_model=BaseResponse)
async def update_pickle_info(pickle_id: int, pickle_info: PickleInfoUpdateRequest):
    """更新pickle信息"""
    try:
        result = service.update_pickle_info(pickle_id, pickle_info)
        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=400, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/pickle_infos/{pickle_id}", response_model=BaseResponse)
async def delete_pickle_info(pickle_id: int):
    """删除pickle信息"""
    try:
        result = service.delete_pickle_info(pickle_id)
        if result['success']:
            return BaseResponse(success=True, message=result['message'])
        else:
            raise HTTPException(status_code=404, detail=result['message'])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 视图相关接口
@router.get("/bag_pickle_view", response_model=BagPickleViewResponse)
async def get_bag_pickle_view(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    bag_name: Optional[str] = Query(None),
    version: Optional[str] = Query(None),
    mode: Optional[str] = Query(None)
):
    """获取bag和pickle组合视图"""
    try:
        result = service.get_bag_pickle_view(
            page=page, page_size=page_size,
            bag_name=bag_name, version=version, mode=mode
        )

        return BagPickleViewResponse(
            success=True,
            message=result['message'],
            data=result['data'],
            total=result['total'],
            page=result['page'],
            page_size=result['page_size']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "data_process"}
