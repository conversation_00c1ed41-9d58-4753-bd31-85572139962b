from typing import List, Optional, Dict, Any
from .dao import DataProcessDAO
from .entity import *


class DataProcessService:
    def __init__(self):
        self.dao = DataProcessDAO()

    # BagInfo 服务方法
    def create_bag_info(self, bag_info: BagInfoCreateRequest) -> Dict[str, Any]:
        """创建bag信息"""
        try:
            # 检查bag_name是否已存在
            existing = self.dao.get_bag_info_by_name(bag_info.bag_name)
            if existing:
                return {
                    "success": False, 
                    "message": f"Bag name '{bag_info.bag_name}' already exists"
                }
            
            bag_id = self.dao.create_bag_info(bag_info)
            return {
                "success": True, 
                "data": {"id": bag_id}, 
                "message": "Bag info created successfully"
            }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to create bag info: {str(e)}"
            }

    def get_bag_info(self, bag_id: int) -> Dict[str, Any]:
        """获取bag信息"""
        try:
            bag_info = self.dao.get_bag_info_by_id(bag_id)
            if bag_info:
                return {
                    "success": True, 
                    "data": bag_info, 
                    "message": "Bag info retrieved successfully"
                }
            else:
                return {
                    "success": False, 
                    "message": "Bag info not found"
                }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to get bag info: {str(e)}"
            }

    def get_bag_info_by_name(self, bag_name: str) -> Dict[str, Any]:
        """根据名称获取bag信息"""
        try:
            bag_info = self.dao.get_bag_info_by_name(bag_name)
            if bag_info:
                return {
                    "success": True, 
                    "data": bag_info, 
                    "message": "Bag info retrieved successfully"
                }
            else:
                return {
                    "success": False, 
                    "message": "Bag info not found"
                }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to get bag info: {str(e)}"
            }

    def list_bag_infos(self, page: int = 1, page_size: int = 20,
                      version: str = None, mode: str = None, vin: str = None,
                      date_from: str = None, date_to: str = None) -> Dict[str, Any]:
        """获取bag信息列表"""
        try:
            offset = (page - 1) * page_size
            bag_infos = self.dao.get_bag_infos(
                offset=offset, limit=page_size,
                version=version, mode=mode, vin=vin,
                date_from=date_from, date_to=date_to
            )
            total = self.dao.count_bag_infos(
                version=version, mode=mode, vin=vin,
                date_from=date_from, date_to=date_to
            )
            
            return {
                "success": True,
                "data": bag_infos,
                "total": total,
                "page": page,
                "page_size": page_size,
                "message": "Bag infos retrieved successfully"
            }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to list bag infos: {str(e)}"
            }

    def update_bag_info(self, bag_id: int, bag_info: BagInfoUpdateRequest) -> Dict[str, Any]:
        """更新bag信息"""
        try:
            # 检查记录是否存在
            existing = self.dao.get_bag_info_by_id(bag_id)
            if not existing:
                return {
                    "success": False, 
                    "message": "Bag info not found"
                }
            
            success = self.dao.update_bag_info(bag_id, bag_info)
            if success:
                return {
                    "success": True, 
                    "message": "Bag info updated successfully"
                }
            else:
                return {
                    "success": False, 
                    "message": "No changes made"
                }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to update bag info: {str(e)}"
            }

    def delete_bag_info(self, bag_id: int) -> Dict[str, Any]:
        """删除bag信息"""
        try:
            # 检查记录是否存在
            existing = self.dao.get_bag_info_by_id(bag_id)
            if not existing:
                return {
                    "success": False, 
                    "message": "Bag info not found"
                }
            
            success = self.dao.delete_bag_info(bag_id)
            if success:
                return {
                    "success": True, 
                    "message": "Bag info deleted successfully"
                }
            else:
                return {
                    "success": False, 
                    "message": "Failed to delete bag info"
                }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to delete bag info: {str(e)}"
            }

    # PickleInfo 服务方法
    def create_pickle_info(self, pickle_info: PickleInfoCreateRequest) -> Dict[str, Any]:
        """创建pickle信息"""
        try:
            # 检查关联的bag_name是否存在
            bag_exists = self.dao.get_bag_info_by_name(pickle_info.bag_name)
            if not bag_exists:
                return {
                    "success": False, 
                    "message": f"Referenced bag '{pickle_info.bag_name}' does not exist"
                }
            
            pickle_id = self.dao.create_pickle_info(pickle_info)
            return {
                "success": True, 
                "data": {"id": pickle_id}, 
                "message": "Pickle info created successfully"
            }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to create pickle info: {str(e)}"
            }

    def get_pickle_info(self, pickle_id: int) -> Dict[str, Any]:
        """获取pickle信息"""
        try:
            pickle_info = self.dao.get_pickle_info_by_id(pickle_id)
            if pickle_info:
                return {
                    "success": True, 
                    "data": pickle_info, 
                    "message": "Pickle info retrieved successfully"
                }
            else:
                return {
                    "success": False, 
                    "message": "Pickle info not found"
                }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to get pickle info: {str(e)}"
            }

    def get_pickles_by_bag(self, bag_name: str) -> Dict[str, Any]:
        """根据bag名称获取所有相关pickle信息"""
        try:
            pickle_infos = self.dao.get_pickle_infos_by_bag_name(bag_name)
            return {
                "success": True,
                "data": pickle_infos,
                "message": f"Found {len(pickle_infos)} pickle infos for bag '{bag_name}'"
            }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to get pickle infos: {str(e)}"
            }

    def list_pickle_infos(self, page: int = 1, page_size: int = 20,
                         bag_name: str = None, pkl_utility: str = None,
                         version: str = None, date_from: str = None, 
                         date_to: str = None) -> Dict[str, Any]:
        """获取pickle信息列表"""
        try:
            offset = (page - 1) * page_size
            pickle_infos = self.dao.get_pickle_infos(
                offset=offset, limit=page_size,
                bag_name=bag_name, pkl_utility=pkl_utility,
                version=version, date_from=date_from, date_to=date_to
            )
            total = self.dao.count_pickle_infos(
                bag_name=bag_name, pkl_utility=pkl_utility,
                version=version, date_from=date_from, date_to=date_to
            )
            
            return {
                "success": True,
                "data": pickle_infos,
                "total": total,
                "page": page,
                "page_size": page_size,
                "message": "Pickle infos retrieved successfully"
            }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to list pickle infos: {str(e)}"
            }

    def update_pickle_info(self, pickle_id: int, pickle_info: PickleInfoUpdateRequest) -> Dict[str, Any]:
        """更新pickle信息"""
        try:
            # 检查记录是否存在
            existing = self.dao.get_pickle_info_by_id(pickle_id)
            if not existing:
                return {
                    "success": False, 
                    "message": "Pickle info not found"
                }
            
            success = self.dao.update_pickle_info(pickle_id, pickle_info)
            if success:
                return {
                    "success": True, 
                    "message": "Pickle info updated successfully"
                }
            else:
                return {
                    "success": False, 
                    "message": "No changes made"
                }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to update pickle info: {str(e)}"
            }

    def delete_pickle_info(self, pickle_id: int) -> Dict[str, Any]:
        """删除pickle信息"""
        try:
            # 检查记录是否存在
            existing = self.dao.get_pickle_info_by_id(pickle_id)
            if not existing:
                return {
                    "success": False, 
                    "message": "Pickle info not found"
                }
            
            success = self.dao.delete_pickle_info(pickle_id)
            if success:
                return {
                    "success": True, 
                    "message": "Pickle info deleted successfully"
                }
            else:
                return {
                    "success": False, 
                    "message": "Failed to delete pickle info"
                }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to delete pickle info: {str(e)}"
            }

    # 视图服务方法
    def get_bag_pickle_view(self, page: int = 1, page_size: int = 20,
                           bag_name: str = None, version: str = None,
                           mode: str = None) -> Dict[str, Any]:
        """获取bag和pickle组合视图"""
        try:
            offset = (page - 1) * page_size
            view_data = self.dao.get_bag_pickle_view(
                offset=offset, limit=page_size,
                bag_name=bag_name, version=version, mode=mode
            )
            total = self.dao.count_bag_pickle_view(
                bag_name=bag_name, version=version, mode=mode
            )
            
            return {
                "success": True,
                "data": view_data,
                "total": total,
                "page": page,
                "page_size": page_size,
                "message": "Bag pickle view retrieved successfully"
            }
        except Exception as e:
            return {
                "success": False, 
                "message": f"Failed to get bag pickle view: {str(e)}"
            }