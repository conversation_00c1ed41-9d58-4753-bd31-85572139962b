# api/auth/auth.py
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timedelta
import jwt
import secrets
import mysql.connector
from database.config import EVAL_DB_CONFIG

router = APIRouter(prefix="/api/auth", tags=["authentication"])
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT配置
SECRET_KEY = "your-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 6 * 30 * 24  # 6个月


class UserCreate(BaseModel):
    username: str
    employee_id: str
    password: str


class UserLogin(BaseModel):
    username: str
    password: str


class UserResponse(BaseModel):
    id: int
    username: str
    employee_id: str
    role: str
    is_active: bool


def get_db_connection():
    config = EVAL_DB_CONFIG.copy()
    config['database'] = 'my_eval_db'
    return mysql.connector.connect(**config)


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


# ...existing code...
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer(auto_error=False))):
    if credentials is None or not credentials.credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    try:
        token = credentials.credentials
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的token",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.PyJWTError as e:
        print(f"JWT decode error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute(
            "SELECT * FROM users WHERE id = %s AND is_active = TRUE", (user_id,))
        user = cursor.fetchone()
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已禁用",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user
    finally:
        cursor.close()
        conn.close()
# ...existing code...


@router.post("/register")
async def register(user: UserCreate):
    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # 检查用户名或工号是否存在
        cursor.execute("SELECT id FROM users WHERE username = %s OR employee_id = %s",
                       (user.username, user.employee_id))
        if cursor.fetchone():
            raise HTTPException(status_code=400, detail="用户名或工号已存在")

        # 创建用户
        hashed_password = get_password_hash(user.password)
        cursor.execute("""
            INSERT INTO users (username, employee_id, password_hash)
            VALUES (%s, %s, %s)
        """, (user.username, user.employee_id, hashed_password))

        conn.commit()
        return {"success": True, "message": "用户注册成功"}
    finally:
        cursor.close()
        conn.close()


@router.post("/login")
async def login(user_login: UserLogin):
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    try:
        cursor.execute("SELECT * FROM users WHERE username = %s AND is_active = TRUE",
                       (user_login.username,))
        user = cursor.fetchone()

        if not user or not verify_password(user_login.password, user['password_hash']):
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 创建访问令牌
        access_token = create_access_token(data={"sub": user['id']})

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user['id'],
                "username": user['username'],
                "employee_id": user['employee_id'],
                "role": user['role']
            }
        }
    finally:
        cursor.close()
        conn.close()


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    return UserResponse(**current_user)


@router.post("/logout")
async def logout():
    return {"message": "登出成功"}
