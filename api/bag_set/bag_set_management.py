"""
bag集管理相关的API端点
"""

import os
import json
from typing import List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Query, Path
from pydantic import BaseModel
from database.db_operations import execute_query
import numpy as np
import matplotlib.pyplot as plt
from api.visualize.pickle_visualize import load_pickle
from api.visualize.draw import draw_lane_turn_sign
import base64
import asyncio
from concurrent.futures import ThreadPoolExecutor
import cv2

router = APIRouter()


@router.get("/bag-sets/test-db")
async def test_database_connection():
    """测试数据库连接和表结构"""
    try:
        # 测试bag_set表
        result1 = execute_query("SELECT COUNT(*) FROM bag_set", fetch_one=True)
        bag_set_count = (
            result1["data"][0]
            if result1["success"]
            else f"错误: {result1.get('error', '未知')}"
        )

        # 测试bag表
        result2 = execute_query("SELECT COUNT(*) FROM bag", fetch_one=True)
        bag_count = (
            result2["data"][0]
            if result2["success"]
            else f"错误: {result2.get('error', '未知')}"
        )

        # 测试bag_pkl_relation表
        result3 = execute_query("SELECT COUNT(*) FROM bag_pkl_relation", fetch_one=True)
        pkl_count = (
            result3["data"][0]
            if result3["success"]
            else f"错误: {result3.get('error', '未知')}"
        )

        return {
            "success": True,
            "message": "数据库连接测试",
            "data": {
                "bag_set_count": bag_set_count,
                "bag_count": bag_count,
                "pkl_relation_count": pkl_count,
            },
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


class BagSetCreate(BaseModel):
    set_name: str
    creator_name: str
    description: Optional[str] = None


class BagSetResponse(BaseModel):
    id: int
    set_name: str
    creator_name: str
    description: Optional[str] = None
    bag_count: int
    total_pkl_count: int
    created_at: str
    updated_at: str


class BagResponse(BaseModel):
    id: int
    bag_name: str
    bag_path: str
    pkl_count: int
    created_at: str


class PklResponse(BaseModel):
    id: int
    pkl_path: str
    pkl_name: str
    created_at: str


@router.get("/bag-sets")
async def get_bag_sets(page: int = 1, per_page: int = 10, search: Optional[str] = None):
    """获取bag集列表"""
    try:
        # 构建查询条件
        where_conditions = []
        params = []

        if search:
            where_conditions.append(
                "(bs.set_name LIKE %s OR bs.creator_name LIKE %s OR bs.description LIKE %s)"
            )
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        where_clause = (
            " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        )

        # 查询总数
        count_query = f"""
        SELECT COUNT(DISTINCT bs.id) as total
        FROM bag_set bs
        {where_clause}
        """
        count_result = execute_query(count_query, params, fetch_one=True)
        total = (
            count_result["data"][0]
            if count_result["success"] and count_result["data"]
            else 0
        )

        # 查询bag集列表
        offset = (page - 1) * per_page
        query = f"""
        SELECT 
            bs.id,
            bs.set_name,
            bs.creator_name,
            bs.description,
            bs.created_at,
            bs.updated_at,
            COUNT(DISTINCT b.id) as bag_count,
            COALESCE(SUM(b.pkl_count), 0) as total_pkl_count  -- 使用pkl_count冗余字段
        FROM bag_set bs
        LEFT JOIN bag b ON bs.id = b.bag_set_id
        {where_clause}
        GROUP BY bs.id, bs.set_name, bs.creator_name, bs.description, bs.created_at, bs.updated_at
        ORDER BY bs.created_at DESC
        LIMIT %s OFFSET %s
        """

        params.extend([per_page, offset])
        result = execute_query(query, params, fetch_all=True)

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"查询失败: {result['error']}")

        bag_sets = []
        for row in result["data"]:
            bag_sets.append(
                {
                    "id": row[0],
                    "set_name": row[1],
                    "creator_name": row[2],
                    "description": row[3],
                    "created_at": row[4].isoformat() if row[4] else None,
                    "updated_at": row[5].isoformat() if row[5] else None,
                    "bag_count": row[6] or 0,
                    "total_pkl_count": row[7] or 0,
                }
            )

        return {
            "success": True,
            "data": bag_sets,
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取bag集列表失败: {str(e)}")


@router.post("/bag-sets")
async def create_bag_set(bag_set: BagSetCreate):
    """创建新的bag集"""
    try:
        # 检查名称是否已存在
        check_query = "SELECT id FROM bag_set WHERE set_name = %s"
        check_result = execute_query(check_query, (bag_set.set_name,), fetch_one=True)

        if check_result["success"] and check_result["data"]:
            raise HTTPException(status_code=400, detail="bag集名称已存在")

        # 创建bag集
        insert_query = """
        INSERT INTO bag_set (set_name, creator_name, description)
        VALUES (%s, %s, %s)
        """
        result = execute_query(
            insert_query, (bag_set.set_name, bag_set.creator_name, bag_set.description)
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"创建失败: {result['error']}")

        return {"success": True, "message": "bag集创建成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建bag集失败: {str(e)}")


@router.get("/bag-sets/{bag_set_id}")
async def get_bag_set_detail(
    bag_set_id: int,
    page: int = 1,
    per_page: int = 50,
    year: Optional[str] = None,
    month: Optional[str] = None,
    day: Optional[str] = None,
):
    print("month:"  f"{month}")
    """获取bag集详情，包括所有bag和pkl"""
    try:
        # 获取bag集基本信息
        bag_set_query = """
        SELECT id, set_name, creator_name, description, created_at, updated_at
        FROM bag_set WHERE id = %s
        """
        bag_set_result = execute_query(bag_set_query, (bag_set_id,), fetch_one=True)

        if not bag_set_result["success"] or not bag_set_result["data"]:
            raise HTTPException(status_code=404, detail="bag集不存在")

        bag_set_data = bag_set_result["data"]
        offset = (page - 1) * per_page
        # 构建过滤条件
        filters = []
        params = [bag_set_id]

        if year:
            filters.append("SUBSTRING(b.bag_name, -19, 4) = %s")  # 提取年份
            params.append(year)

        if month:
            filters.append("SUBSTRING(b.bag_name, -14, 2) = %s")
            params.append(month.zfill(2))
        if day:
            filters.append("SUBSTRING(b.bag_name, -11, 2) = %s")  # 提取日期
            params.append(day.zfill(2))  # 确保日期是两位数

        filter_clause = " AND " + " AND ".join(filters) if filters else ""
        # 获取bag总数
        bag_count_query = f"""
        SELECT COUNT(*) FROM bag b WHERE bag_set_id = %s {filter_clause}
        """
        print(bag_count_query,params)
        bag_count_result = execute_query(bag_count_query, params, fetch_one=True)
        total_bags = bag_count_result["data"][0] if bag_count_result["success"] else 0
        # 获取bag列表
        bags_query = f"""
        SELECT 
            b.id,
            b.bag_name,
            b.bag_path,
            b.created_at,
            b.pkl_count
        FROM bag b
        WHERE b.bag_set_id = %s {filter_clause}
        ORDER BY b.bag_name
        LIMIT %s OFFSET %s
        """
        params.extend([per_page, offset])
        bags_result = execute_query(
            bags_query, params, fetch_all=True
        )
        print(bags_query,params)
        bags = []
        if bags_result["success"] and bags_result["data"]:
            for row in bags_result["data"]:
                bags.append(
                    {
                        "id": row[0],
                        "bag_name": row[1],
                        "bag_path": row[2],
                        "created_at": row[3].isoformat() if row[3] else None,
                        "pkl_count": row[4] or 0,
                    }
                )

        return {
            "success": True,
            "bag_set": {
                "id": bag_set_data[0],
                "set_name": bag_set_data[1],
                "creator_name": bag_set_data[2],
                "description": bag_set_data[3],
                "created_at": bag_set_data[4].isoformat() if bag_set_data[4] else None,
                "updated_at": bag_set_data[5].isoformat() if bag_set_data[5] else None,
            },
            "bags": bags,
            "total_bags": total_bags,
            "total_pkls": sum(bag["pkl_count"] for bag in bags),
            "page": page,
            "per_page": per_page,
            "pages": (total_bags + per_page - 1) // per_page,  # 总页数
            "filters": {
                "year": year,
                "month": month,
                "day": day,
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取bag集详情失败: {str(e)}")


@router.get("/bag-sets/{bag_set_id}/bags/{bag_id}/pkls")
async def get_bag_pkls(
    bag_set_id: int,
    bag_id: int,
):
    """获取指定bag下的所有pkl文件，返回格式与evaluation_set_with_cases对齐"""
    try:
        # 验证bag是否属于指定的bag集，同时获取bag和bag集信息
        verify_query = """
        SELECT b.id, b.bag_name, bs.set_name, bs.id as bag_set_id
        FROM bag b
        JOIN bag_set bs ON b.bag_set_id = bs.id
        WHERE b.id = %s AND bs.id = %s
        """
        verify_result = execute_query(
            verify_query, (bag_id, bag_set_id), fetch_one=True
        )

        if not verify_result["success"]:
            error_msg = f"验证bag失败: {verify_result.get('error', '未知错误')}"
            print(f"❌ {error_msg}")
            raise HTTPException(status_code=500, detail=error_msg)

        if not verify_result["data"]:
            raise HTTPException(status_code=404, detail="bag不存在或不属于指定的bag集")

        bag_data = verify_result["data"]

        # 计算分页偏移量

        # 获取总数
        count_query = """
        SELECT COUNT(*) FROM bag_pkl_relation WHERE bag_id = %s
        """
        count_result = execute_query(count_query, (bag_id,), fetch_one=True)
        total_count = (
            count_result["data"][0]
            if count_result["success"] and count_result["data"]
            else 0
        )

        # 获取bag下的pkl文件（分页）
        pkls_query = """
        SELECT id, pkl_path, pkl_name, created_at
        FROM bag_pkl_relation
        WHERE bag_id = %s
        ORDER BY pkl_name
       
        """
        pkls_result = execute_query(pkls_query, (bag_id,), fetch_all=True)

        if not pkls_result["success"]:
            error_msg = f"查询pkl文件失败: {pkls_result.get('error', '未知错误')}"
            print(f"❌ {error_msg}")
            print(f"   SQL: {pkls_query}")
            raise HTTPException(status_code=500, detail=error_msg)

        # 构造与evaluation_set_with_cases相同的数据格式
        cases = []
        if pkls_result["data"]:
            for row in pkls_result["data"]:
                case_data = {
                    "id": row[0],
                    "pkl_name": row[2],
                    "pkl_dir": os.path.dirname(row[1]),  # 从完整路径提取目录
                    "pkl_path": row[1],  # 完整路径
                    "vehicle_type": None,  # bag集模式下暂时没有这个信息
                    "vin": None,  # bag集模式下暂时没有这个信息
                    "time_ns": None,  # bag集模式下暂时没有这个信息
                    "key_obs_id": None,  # bag集模式下暂时没有这个信息
                    "dirty_data": False,  # bag集模式下默认为False
                    "is_checked": False,  # bag集模式下默认为False
                    "checked_at": None,
                    "checked_by": None,
                    "bag_name": bag_data[1],  # 添加bag名称
                    "created_at": row[3],
                    "updated_at": row[3],  # 使用created_at作为updated_at
                }
                cases.append(case_data)

        # 返回与evaluation_set_with_cases相同的格式
        return {
            "success": True,
            "evaluation_set": {  # 使用bag集信息模拟evaluation_set
                "id": bag_data[3],  # bag_set_id
                "name": f"{bag_data[2]} - {bag_data[1]}",  # "bag集名称 - bag名称"
                "creator_name": "bag集",
                "description": f"bag集: {bag_data[2]}, bag: {bag_data[1]}",
                "created_at": None,
            },
            "cases": cases,
            "case_count": total_count,
            "filter_info": {
                "bag_name_filter": None,
                "tag_filter": None,
                "time_ns_filter": None,
                "time_range_filter": None,
                "employee_id_filter": None,
                "bag_time_filter_active": False,
                "sort_by": "pkl_name_asc",
            },
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取pkl列表失败: {str(e)}")


@router.get("/bag-sets/{bag_set_id}/bags/{bag_id}/pkl/{pkl_id}")
async def get_bag_pkl_detail(bag_set_id: int, bag_id: int, pkl_id: int):
    try:
        # 合并查询
        verify_query = """
        SELECT 
            bpr.id, bpr.pkl_path, bpr.pkl_name, lsc.selected_centerline_ids, lst.tags
        FROM bag_pkl_relation bpr
        LEFT JOIN lane_scene_centerlines lsc ON bpr.id = lsc.pkl_id 
        LEFT JOIN lane_scene_tags lst ON bpr.id = lst.pkl_id
        WHERE bpr.id = %s 
        """
        verify_result = execute_query(verify_query, (pkl_id,), fetch_one=True)

        if not verify_result["success"] or not verify_result["data"]:
            raise HTTPException(
                status_code=404, detail="pkl文件不存在或不属于指定的bag集"
            )

        pkl_data = verify_result["data"]
        pkl_path = pkl_data[1]
        pkl_name = pkl_data[2]
        selected_centerline_ids = json.loads(pkl_data[3]) if pkl_data[3] else []
        scene_tags = json.loads(pkl_data[4]) if pkl_data[4] else []

        # 检查文件是否存在
        if not os.path.exists(pkl_path):
            raise HTTPException(status_code=404, detail="PKL文件不存在")

        # 加载pickle文件
        data = load_pickle(pkl_path)

        # 处理图像数据（复用原有逻辑）
        sign_image = None
        ego_yaw = -1.0 * data.get("feature_obj", None)[0, 0, 10]
        ego_path = data.get("path_point", None)
        ego_path_mask = data.get("path_mask", None)
        ego_path = ego_path * ego_path_mask[:, None]

        fig, ax = plt.subplots(1, 1, figsize=(5, 5))

        lane_turn_sign = data.get("lane_turn_sign", None)
        lane_turn_sign_mask = data.get("lane_turn_sign_mask", None)
        has_lane_sign = False

        ego_image_data = None
        ego_image = data.get("e2e_image", None)
        if ego_image:
            np_image = ego_image.get("image", None)
            if np_image is not None and isinstance(np_image, np.ndarray):
                # 将numpy数组格式转换为适合前端显示的格式
                try:
                    # 转换为RGB图像
                    if len(np_image.shape) == 3 and np_image.shape[0] == 3:
                        # 如果是(3, height, width)格式，转换为(height, width, 3)
                        np_image = np.transpose(np_image, (1, 2, 0))

                    # 确保图像数据在0-255范围内
                    if np_image.max() <= 1.0:
                        np_image = (np_image * 255).astype(np.uint8)
                    np_image = cv2.cvtColor(np_image, cv2.COLOR_YUV2BGR)
                    # print('...imgs',np_image.shape)
                    # np_image = cv2.cvtColor(np_image, cv2.COLOR_BGR2RGB)
                    # 编码为JPEG图像
                    _, buffer = cv2.imencode(".jpg", np_image)
                    ego_image_data = base64.b64encode(buffer).decode("utf-8")
                except Exception as e:
                    print(f"处理图像出错: {str(e)}")
        if lane_turn_sign is not None:
            draw_lane_turn_sign(
                sp=ax,
                lane_turn_sign=lane_turn_sign,
                lane_turn_sign_mask=lane_turn_sign_mask,
            )
            has_lane_sign = True

        if ego_path is not None:
            ego_path = (
                ego_path.tolist() if isinstance(ego_path, np.ndarray) else ego_path
            )

        if has_lane_sign:
            try:
                import io

                # 保存图形到内存缓冲区
                buffer = io.BytesIO()
                fig.savefig(buffer, format="png", bbox_inches="tight", dpi=100)
                buffer.seek(0)

                # 转换为base64编码
                sign_image = base64.b64encode(buffer.getvalue()).decode("utf-8")

                # 清理资源
                buffer.close()
                plt.close(fig)

            except Exception as e:
                print(f"处理车道标志绘图出错: {str(e)}")
                plt.close(fig)

        # # 暂时跳过标注数据查询，专注于图片显示
        # current_annotation = None
        # selected_centerline_ids = []
        # centerlines_query = """
        # SELECT selected_centerline_ids
        # FROM lane_scene_centerlines WHERE pkl_id = %s and bag_id = %s
        # """
        # centerlines_result = execute_query(
        #     centerlines_query, (pkl_id, bag_id), fetch_one=True
        # )
        # if centerlines_result["success"] and centerlines_result["data"]:
        #     selected_centerline_ids = (
        #         json.loads(centerlines_result["data"][0])
        #         if centerlines_result["data"][0]
        #         else []
        #     )
        # # 获取场景标签
        # tags_query = """
        # SELECT tags
        # FROM lane_scene_tags
        # WHERE pkl_id = %s
        # """
        # tags_result = execute_query(tags_query, (pkl_id,), fetch_one=True)
        # scene_tags = []
        # if tags_result["success"] and tags_result["data"] and tags_result["data"][0]:
        #     scene_tags = json.loads(tags_result["data"][0])
        return {
            "success": True,
            "pkl_info": {
                "id": pkl_data[0],
                "pkl_path": pkl_data[1],
                "pkl_dir": os.path.dirname(pkl_data[1]),
                "pkl_name": pkl_data[2],
                "bag_name": pkl_data[3],
                "bag_set_name": pkl_data[4],
                "is_dirty": False,  # bag集模式下暂时不支持dirty标记
            },
            "sign_image": sign_image,
            "scene_tags": scene_tags,
            "ego_img_data": ego_image_data,  # 暂时不支持ego2img数据
            "ego_yaw": ego_yaw,
            "ego_path_data": ego_path,
            "selected_centerline_ids": selected_centerline_ids,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取pkl详情失败: {str(e)}")


@router.delete("/bag-sets/{bag_set_id}")
async def delete_bag_set(bag_set_id: int):
    """删除bag集"""
    try:
        # 检查bag集是否存在
        check_query = "SELECT id FROM bag_set WHERE id = %s"
        check_result = execute_query(check_query, (bag_set_id,), fetch_one=True)

        if not check_result["success"] or not check_result["data"]:
            raise HTTPException(status_code=404, detail="bag集不存在")

        # 删除bag集（级联删除会自动删除相关的bag和pkl关联）
        delete_query = "DELETE FROM bag_set WHERE id = %s"
        result = execute_query(delete_query, (bag_set_id,))

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"删除失败: {result['error']}")

        return {"success": True, "message": "bag集删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除bag集失败: {str(e)}")


# 添加导出API端点
@router.get("/bag-sets/export-bag-set/{bag_set_id}")
async def export_bag_set_lane_scene_annotations(
    bag_set_id: int = Path(..., description="bag集ID"),
):
    """导出bag集的车道场景标注数据（centerlines和tags）"""
    try:
        # 检查bag集是否存在
        check_set_query = "SELECT id, set_name FROM bag_set WHERE id = %s"
        set_result = execute_query(check_set_query, (bag_set_id,), fetch_one=True)

        if not set_result["success"] or not set_result["data"]:
            raise HTTPException(status_code=404, detail=f"bag集ID {bag_set_id} 不存在")

        set_name = set_result["data"][1]

        # 查询bag集的车道场景标注数据
        query = """
        WITH filtered_pkl_ids AS (
            SELECT DISTINCT pkl_id
            FROM lane_scene_centerlines
            UNION
            SELECT DISTINCT pkl_id
            FROM lane_scene_tags
        )
        SELECT
            bpr.pkl_path,
            lsc.selected_centerline_ids,
            lst.tags
        FROM 
            bag_pkl_relation bpr
        JOIN 
            bag b ON bpr.bag_id = b.id
        LEFT JOIN 
            lane_scene_centerlines lsc ON bpr.id = lsc.pkl_id AND lsc.bag_id = b.id
        LEFT JOIN 
            lane_scene_tags lst ON bpr.id = lst.pkl_id AND lst.bag_id = b.id
        WHERE 
            b.bag_set_id = %s
            AND bpr.id IN (SELECT pkl_id FROM filtered_pkl_ids)
        """

        result = execute_query(query, (bag_set_id,), fetch_all=True)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"查询标注数据时出错: {result['error']}"
            )

        # 组织返回数据
        # annotations = []
        # for row in result["data"]:
        #     pkl_path = row[0]
        #     centerline_ids_json = row[1]
        #     tags_json = row[2]

        #     # 解析centerline IDs
        #     centerline_ids = []
        #     if centerline_ids_json:
        #         try:
        #             centerline_ids = json.loads(centerline_ids_json)
        #         except (json.JSONDecodeError, TypeError):
        #             centerline_ids = []

        #     # 解析tags
        #     tags = []
        #     if tags_json:
        #         try:
        #             tags = json.loads(tags_json)
        #         except (json.JSONDecodeError, TypeError):
        #             if isinstance(tags_json, str):
        #                 tags = [tags_json]
        #             else:
        #                 tags = []

        #     # 只有当centerline_ids或tags不为空时才添加到结果中
        #     if centerline_ids or tags:
        #         annotations.append([pkl_path, centerline_ids, tags])
        annotations = []

        # 批量解析数据
        pkl_paths = [row[0] for row in result["data"]]
        centerline_ids_jsons = [row[1] for row in result["data"]]
        tags_jsons = [row[2] for row in result["data"]]

        # 批量解析 centerline_ids 和 tags
        centerline_ids_list = []
        tags_list = []

        for centerline_ids_json in centerline_ids_jsons:
            try:
                centerline_ids_list.append(
                    json.loads(centerline_ids_json) if centerline_ids_json else []
                )
            except (json.JSONDecodeError, TypeError):
                centerline_ids_list.append([])

        for tags_json in tags_jsons:
            try:
                tags_list.append(json.loads(tags_json) if tags_json else [])
            except (json.JSONDecodeError, TypeError):
                tags_list.append([tags_json] if isinstance(tags_json, str) else [])

        # 组合数据
        annotations = [
            [pkl_path, centerline_ids, tags]
            for pkl_path, centerline_ids, tags in zip(
                pkl_paths, centerline_ids_list, tags_list
            )
            if centerline_ids or tags
        ]
        return {
            "success": True,
            "annotations_count": len(annotations),
            "data": annotations,
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"导出标注数据时出错: {str(e)}")


@router.post("/bag-sets/{bag_set_id}/upload")
async def upload_bag_set_txt_optimized(bag_set_id: int, file: UploadFile = File(...)):
    """超大规模数据上传优化版本"""
    try:
        # 检查bag集是否存在
        check_query = "SELECT id FROM bag_set WHERE id = %s"
        check_result = execute_query(check_query, (bag_set_id,), fetch_one=True)

        if not check_result["success"] or not check_result["data"]:
            raise HTTPException(status_code=404, detail="bag集不存在")

        # 检查文件类型
        if not file.filename.endswith(".txt"):
            raise HTTPException(status_code=400, detail="只支持txt文件")

        # 读取文件内容
        content = await file.read()
        try:
            text_content = content.decode("utf-8")
        except UnicodeDecodeError:
            try:
                text_content = content.decode("gbk")
            except UnicodeDecodeError:
                raise HTTPException(
                    status_code=400, detail="文件编码不支持，请使用UTF-8或GBK编码"
                )

        # 解析文件内容
        lines = [line.strip() for line in text_content.split("\n") if line.strip()]

        if not lines:
            raise HTTPException(status_code=400, detail="文件内容为空")

        # 启动异步处理
        total_bags = len(lines)
        print(f"开始处理 {total_bags} 个bag文件...")

        # 使用线程池处理
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=16) as executor:  # 限制并发数
            result = await loop.run_in_executor(
                executor, process_large_upload, lines, bag_set_id
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


def process_large_upload(lines, bag_set_id):
    """在线程池中处理大规模上传"""
    success_count = 0
    error_count = 0
    errors = []
    total_pkl_count = 0

    # 超大批次处理
    SUPER_BATCH_SIZE = 500  # 每次处理500个bag

    for batch_start in range(0, len(lines), SUPER_BATCH_SIZE):
        batch_end = min(batch_start + SUPER_BATCH_SIZE, len(lines))
        batch_lines = lines[batch_start:batch_end]

        print(
            f"处理批次 {batch_start // SUPER_BATCH_SIZE + 1}/{(len(lines) - 1) // SUPER_BATCH_SIZE + 1}"
        )

        try:
            batch_result = process_bag_batch_optimized(
                batch_lines, bag_set_id, batch_start
            )
            success_count += batch_result["success_count"]
            error_count += batch_result["error_count"]
            total_pkl_count += batch_result["pkl_count"]
            errors.extend(batch_result["errors"])

            # 记录进度
            progress = ((batch_start + len(batch_lines)) / len(lines)) * 100
            print(
                f"已完成 {progress:.1f}% - 成功: {success_count}, 失败: {error_count}, PKL总数: {total_pkl_count}"
            )

        except Exception as e:
            print(f"批次处理失败: {str(e)}")
            error_count += len(batch_lines)
            errors.append(f"批次 {batch_start}-{batch_end} 处理失败: {str(e)}")

    return {
        "success": True,
        "message": f"上传完成，成功处理{success_count}个bag，失败{error_count}个，总PKL数量: {total_pkl_count}",
        "details": {
            "success_count": success_count,
            "error_count": error_count,
            "total_pkl_count": total_pkl_count,
            "errors": errors[:20],  # 只返回前20个错误
        },
    }


def process_bag_batch_optimized(batch_lines, bag_set_id, batch_offset):
    """优化的批次处理函数"""
    success_count = 0
    error_count = 0
    errors = []
    total_pkl_count = 0

    # 第一步：预处理和验证
    valid_bags = []

    for idx, bag_txt_path in enumerate(batch_lines):
        line_num = batch_offset + idx + 1
        try:
            if not os.path.exists(bag_txt_path):
                errors.append(f"第{line_num}行: bag文件不存在: {bag_txt_path}")
                error_count += 1
                continue

            bag_name = os.path.basename(bag_txt_path).replace(".txt", "")

            # 快速检查文件是否可读
            try:
                with open(bag_txt_path, "r", encoding="utf-8") as f:
                    first_line = f.readline()
                if not first_line.strip():
                    errors.append(f"第{line_num}行: bag文件为空: {bag_txt_path}")
                    error_count += 1
                    continue
            except:
                try:
                    with open(bag_txt_path, "r", encoding="gbk") as f:
                        first_line = f.readline()
                    if not first_line.strip():
                        errors.append(f"第{line_num}行: bag文件为空: {bag_txt_path}")
                        error_count += 1
                        continue
                except Exception as e:
                    errors.append(f"第{line_num}行: 无法读取bag文件: {str(e)}")
                    error_count += 1
                    continue

            valid_bags.append(
                {"bag_name": bag_name, "bag_path": bag_txt_path, "line_num": line_num}
            )

        except Exception as e:
            errors.append(f"第{line_num}行: 预处理失败: {str(e)}")
            error_count += 1

    if not valid_bags:
        return {
            "success_count": 0,
            "error_count": error_count,
            "pkl_count": 0,
            "errors": errors,
        }

    # 第二步：批量检查已存在的bags
    try:
        bag_paths = [bag["bag_path"] for bag in valid_bags]

        # 使用更高效的查询
        placeholders = ",".join(["%s"] * len(bag_paths))
        existing_bags_query = f"""
        SELECT bag_path FROM bag 
        WHERE bag_path IN ({placeholders}) AND bag_set_id = %s
        """

        existing_bags_result = execute_query(
            existing_bags_query, bag_paths + [bag_set_id], fetch_all=True
        )

        existing_bag_paths = set()
        if existing_bags_result["success"] and existing_bags_result["data"]:
            existing_bag_paths = {row[0] for row in existing_bags_result["data"]}

        # 过滤出需要插入的bags
        new_bags = [
            bag for bag in valid_bags if bag["bag_path"] not in existing_bag_paths
        ]

        if not new_bags:
            print(f"批次中所有bag都已存在，跳过...")
            return {
                "success_count": len(valid_bags),
                "error_count": error_count,
                "pkl_count": 0,
                "errors": errors,
            }

    except Exception as e:
        errors.append(f"检查已存在bag失败: {str(e)}")
        return {
            "success_count": 0,
            "error_count": error_count + len(valid_bags),
            "pkl_count": 0,
            "errors": errors,
        }

    # 第三步：超高效批量插入bags
    try:
        if new_bags:
            # 构建批量插入SQL
            bag_values = []
            for bag in new_bags:
                bag_values.extend([bag["bag_name"], bag["bag_path"], bag_set_id])

            placeholders = ",".join(["(%s, %s, %s)"] * len(new_bags))
            batch_bag_insert_query = f"""
            INSERT INTO bag (bag_name, bag_path, bag_set_id) VALUES {placeholders}
            """

            bag_batch_result = execute_query(batch_bag_insert_query, bag_values)

            if not bag_batch_result["success"]:
                raise Exception(f"批量插入bag失败: {bag_batch_result['error']}")

    except Exception as e:
        errors.append(f"插入bag失败: {str(e)}")
        return {
            "success_count": 0,
            "error_count": error_count + len(new_bags),
            "pkl_count": 0,
            "errors": errors,
        }

    # 第四步：获取所有bag的ID映射
    try:
        all_bag_paths = [bag["bag_path"] for bag in valid_bags]
        placeholders = ",".join(["%s"] * len(all_bag_paths))
        bag_id_query = f"""
        SELECT bag_path, id FROM bag 
        WHERE bag_path IN ({placeholders}) AND bag_set_id = %s
        """

        bag_id_result = execute_query(
            bag_id_query, all_bag_paths + [bag_set_id], fetch_all=True
        )

        if not bag_id_result["success"]:
            raise Exception(f"获取bag ID失败: {bag_id_result['error']}")

        bag_path_to_id = {row[0]: row[1] for row in bag_id_result["data"]}

    except Exception as e:
        errors.append(f"获取bag ID失败: {str(e)}")
        return {
            "success_count": 0,
            "error_count": error_count + len(valid_bags),
            "pkl_count": 0,
            "errors": errors,
        }

    # 第五步：超高效批量处理PKL文件
    try:
        all_pkl_data = []

        for bag in valid_bags:
            bag_id = bag_path_to_id.get(bag["bag_path"])
            if not bag_id:
                continue

            try:
                # 高效读取PKL路径列表
                pkl_paths = read_pkl_paths_fast(bag["bag_path"])

                for pkl_path in pkl_paths:
                    if pkl_path.strip():
                        pkl_name = os.path.basename(pkl_path.strip())
                        all_pkl_data.append((bag_id, pkl_path.strip(), pkl_name))
                        total_pkl_count += 1

                success_count += 1

            except Exception as e:
                errors.append(f"第{bag['line_num']}行: 读取PKL列表失败: {str(e)}")
                error_count += 1

        # 超大批量插入PKL数据
        if all_pkl_data:
            insert_pkl_data_mega_batch(all_pkl_data)

    except Exception as e:
        errors.append(f"处理PKL数据失败: {str(e)}")

    return {
        "success_count": success_count,
        "error_count": error_count,
        "pkl_count": total_pkl_count,
        "errors": errors,
    }


def read_pkl_paths_fast(bag_txt_path):
    """高效读取PKL路径列表"""
    try:
        with open(bag_txt_path, "r", encoding="utf-8", buffering=8192) as f:
            return f.readlines()
    except UnicodeDecodeError:
        with open(bag_txt_path, "r", encoding="gbk", buffering=8192) as f:
            return f.readlines()


def insert_pkl_data_mega_batch(all_pkl_data):
    """超大批量插入PKL数据"""
    MEGA_BATCH_SIZE = 10000  # 每次插入1万条记录

    for i in range(0, len(all_pkl_data), MEGA_BATCH_SIZE):
        batch = all_pkl_data[i : i + MEGA_BATCH_SIZE]

        try:
            # 使用REPLACE INTO避免重复键错误，同时提高性能
            placeholders = ",".join(["(%s, %s, %s)"] * len(batch))
            pkl_insert_query = f"""
            REPLACE INTO bag_pkl_relation (bag_id, pkl_path, pkl_name) 
            VALUES {placeholders}
            """

            pkl_values = [item for sublist in batch for item in sublist]
            pkl_result = execute_query(pkl_insert_query, pkl_values)

            if not pkl_result["success"]:
                print(
                    f"PKL批次 {i // MEGA_BATCH_SIZE + 1} 插入失败: {pkl_result['error']}"
                )
            else:
                print(
                    f"PKL批次 {i // MEGA_BATCH_SIZE + 1} 插入成功，{len(batch)} 条记录"
                )

        except Exception as e:
            print(f"PKL批次 {i // MEGA_BATCH_SIZE + 1} 处理异常: {str(e)}")


# @router.post("/bag-sets/{bag_set_id}/upload")
# async def upload_bag_set_txt(bag_set_id: int, file: UploadFile = File(...)):
#     """优化版本的上传函数"""
#     try:
#         # 检查bag集是否存在
#         check_query = "SELECT id FROM bag_set WHERE id = %s"
#         check_result = execute_query(check_query, (bag_set_id,), fetch_one=True)

#         if not check_result["success"] or not check_result["data"]:
#             raise HTTPException(status_code=404, detail="bag集不存在")

#         # 检查文件类型
#         if not file.filename.endswith(".txt"):
#             raise HTTPException(status_code=400, detail="只支持txt文件")

#         # 读取文件内容
#         content = await file.read()
#         try:
#             text_content = content.decode("utf-8")
#         except UnicodeDecodeError:
#             try:
#                 text_content = content.decode("gbk")
#             except UnicodeDecodeError:
#                 raise HTTPException(
#                     status_code=400, detail="文件编码不支持，请使用UTF-8或GBK编码"
#                 )

#         # 解析文件内容
#         lines = [line.strip() for line in text_content.split("\n") if line.strip()]

#         if not lines:
#             raise HTTPException(status_code=400, detail="文件内容为空")

#         success_count = 0
#         error_count = 0
#         errors = []
#         # 预处理所有数据
#         bags_to_insert = []
#         pkls_to_insert = []

#         # 第一遍：收集所有需要插入的数据
#         for line_num, bag_txt_path in enumerate(lines, 1):
#             try:
#                 if not os.path.exists(bag_txt_path):
#                     errors.append(f"第{line_num}行: bag文件不存在: {bag_txt_path}")
#                     error_count += 1
#                     continue

#                 bag_name = os.path.basename(bag_txt_path).replace(".txt", "")

#                 # 读取pkl文件列表
#                 try:
#                     with open(bag_txt_path, "r", encoding="utf-8") as f:
#                         pkl_lines = [
#                             line.strip() for line in f.readlines() if line.strip()
#                         ]
#                 except UnicodeDecodeError:
#                     with open(bag_txt_path, "r", encoding="gbk") as f:
#                         pkl_lines = [
#                             line.strip() for line in f.readlines() if line.strip()
#                         ]

#                 bags_to_insert.append(
#                     {
#                         "bag_name": bag_name,
#                         "bag_path": bag_txt_path,
#                         "pkl_paths": pkl_lines,
#                     }
#                 )

#             except Exception as e:
#                 errors.append(f"第{line_num}行: 预处理失败: {str(e)}")
#                 error_count += 1

#         # 批量检查已存在的bags
#         if bags_to_insert:
#             bag_paths = [bag["bag_path"] for bag in bags_to_insert]
#             existing_bags_query = """
#             SELECT bag_path, id FROM bag
#             WHERE bag_path IN ({}) AND bag_set_id = %s
#             """.format(",".join(["%s"] * len(bag_paths)))

#             existing_bags_result = execute_query(
#                 existing_bags_query, bag_paths + [bag_set_id], fetch_all=True
#             )

#             existing_bag_paths = set()
#             if existing_bags_result["success"] and existing_bags_result["data"]:
#                 existing_bag_paths = {row[0] for row in existing_bags_result["data"]}

#         # 批量插入bags
#         new_bags = [
#             bag for bag in bags_to_insert if bag["bag_path"] not in existing_bag_paths
#         ]

#         if new_bags:
#             # 使用批量插入
#             bag_insert_values = []
#             for bag in new_bags:
#                 bag_insert_values.extend([bag["bag_name"], bag["bag_path"], bag_set_id])

#             batch_bag_insert_query = """
#             INSERT INTO bag (bag_name, bag_path, bag_set_id) VALUES {}
#             """.format(",".join(["(%s, %s, %s)"] * len(new_bags)))

#             bag_batch_result = execute_query(batch_bag_insert_query, bag_insert_values)

#             if not bag_batch_result["success"]:
#                 raise HTTPException(
#                     status_code=500,
#                     detail=f"批量插入bag失败: {bag_batch_result['error']}",
#                 )

#         # 获取所有bag的ID映射
#         all_bag_paths = [bag["bag_path"] for bag in bags_to_insert]
#         bag_id_query = """
#         SELECT bag_path, id FROM bag
#         WHERE bag_path IN ({}) AND bag_set_id = %s
#         """.format(",".join(["%s"] * len(all_bag_paths)))

#         bag_id_result = execute_query(
#             bag_id_query, all_bag_paths + [bag_set_id], fetch_all=True
#         )

#         bag_path_to_id = {}
#         if bag_id_result["success"] and bag_id_result["data"]:
#             bag_path_to_id = {row[0]: row[1] for row in bag_id_result["data"]}

#         # 准备批量插入pkl数据
#         pkl_batch_data = []
#         for bag in bags_to_insert:
#             bag_id = bag_path_to_id.get(bag["bag_path"])
#             if bag_id:
#                 for pkl_path in bag["pkl_paths"]:
#                     if pkl_path:
#                         pkl_name = os.path.basename(pkl_path)
#                         pkl_batch_data.append((bag_id, pkl_path, pkl_name))

#         # 批量插入pkl数据（使用INSERT IGNORE避免重复）
#         if pkl_batch_data:
#             # 分批处理，避免SQL太长
#             batch_size = 1000
#             for i in range(0, len(pkl_batch_data), batch_size):
#                 batch = pkl_batch_data[i : i + batch_size]

#                 pkl_insert_query = """
#                 INSERT IGNORE INTO bag_pkl_relation (bag_id, pkl_path, pkl_name)
#                 VALUES {}
#                 """.format(",".join(["(%s, %s, %s)"] * len(batch)))

#                 pkl_values = [item for sublist in batch for item in sublist]
#                 pkl_result = execute_query(pkl_insert_query, pkl_values)

#                 if not pkl_result["success"]:
#                     print(f"批次{i // batch_size + 1}插入失败: {pkl_result['error']}")

#         success_count = len(
#             [bag for bag in bags_to_insert if bag["bag_path"] in bag_path_to_id]
#         )

#         return {
#             "success": True,
#             "message": f"上传完成，成功处理{success_count}个bag，失败{error_count}个",
#             "details": {
#                 "success_count": success_count,
#                 "error_count": error_count,
#                 "errors": errors[:10],
#             },
#         }

#     except HTTPException:
#         raise
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")
