import uuid
import asyncio  # 添加 asyncio 导入
import grpc.aio  # 使用 grpc.aio 替换 grpc
# ... 其他导入保持不变 ...
from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
import os
import hashlib
import json
import tempfile
import requests
from fastapi.encoders import jsonable_encoder
from tqdm import tqdm
import shutil
from typing import List
from database.db_operations import execute_query, batch_save_evaluation_results
# 确保导入 inference_result_pb2
from proto import inference_server_pb2, evaluation_server_pb2, inference_result_pb2
from proto import inference_server_pb2_grpc, evaluation_server_pb2_grpc
from google.protobuf.json_format import MessageToDict
import logging
import os
import logging.handlers
from datetime import datetime

log_dir = os.path.join(os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__)))), 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(
    log_dir, f'inference_process_{datetime.now().strftime("%Y%m%d")}.log')

# 配置日志格式和处理器
logger = logging.getLogger('inference_process')
logger.setLevel(logging.INFO)
# 文件处理器
file_handler = logging.handlers.RotatingFileHandler(
    log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
)
# 控制台处理器
console_handler = logging.StreamHandler()
# 格式化器
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)
# 添加处理器到日志记录器
logger.addHandler(file_handler)
logger.addHandler(console_handler)
router = APIRouter()

TASK_STATUS = {}


@router.post("/api/evaluation_tasks")
async def create_evaluation_task(data: dict):
    """创建新的评测任务 (异步处理)"""
    if 'inference_config_id' not in data or 'evaluation_set_ids' not in data:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    config_id = data['inference_config_id']
    eval_set_ids = data['evaluation_set_ids']

    if not isinstance(eval_set_ids, list) or len(eval_set_ids) == 0:
        raise HTTPException(status_code=400, detail="评测集ID列表无效")

    # 生成唯一任务ID
    task_id = str(uuid.uuid4())

    # 初始化任务状态
    TASK_STATUS[task_id] = {
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "config_id": config_id,
        "eval_set_ids": eval_set_ids,
        "message": "任务已创建，等待处理",
        "progress": 0,
        "inferenceProgress": 0,
        "evaluationProgress": 0,
        "results": []
    }

    # 启动异步任务处理
    asyncio.create_task(process_evaluation_task(
        task_id, config_id, eval_set_ids))

    # 立即返回任务ID
    return JSONResponse(
        content={
            "success": True,
            "taskId": task_id,
            "message": "评测任务已提交，正在后台处理"
        }
    )


@router.get("/api/task_status/{task_id}")
async def get_task_status(task_id: str):
    """获取评测任务的状态"""
    if task_id not in TASK_STATUS:
        raise HTTPException(status_code=404, detail="任务不存在")

    return JSONResponse(content=TASK_STATUS[task_id])


@router.post("/api/cancel_task/{task_id}")
async def cancel_task(task_id: str):
    """取消评测任务"""
    if task_id not in TASK_STATUS:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 仅当任务正在运行时才能取消
    if TASK_STATUS[task_id]["status"] == "running":
        TASK_STATUS[task_id]["status"] = "failed"
        TASK_STATUS[task_id]["message"] = "任务已被用户取消"

        # 注意：这里只是改变任务状态，实际取消任务的逻辑需要在处理函数中实现

    return JSONResponse(content={"success": True, "message": "任务已取消"})


async def process_evaluation_task(task_id: str, config_id: int, eval_set_ids: List[int]):
    """异步处理评测任务并更新任务状态"""
    try:
        # 更新任务状态为运行中
        TASK_STATUS[task_id]["status"] = "running"
        TASK_STATUS[task_id]["message"] = "正在验证评测配置..."

        # 验证配置ID是否存在
        query = "SELECT * FROM inference_config WHERE id = %s"
        inference_config_result = execute_query(
            query, (config_id,), fetch_one=True, return_dict=True)

        if not inference_config_result['success'] or not inference_config_result['data']:
            TASK_STATUS[task_id]["status"] = "failed"
            TASK_STATUS[task_id]["message"] = "推理配置不存在"
            return

        # 获取推理配置信息
        config_data = inference_config_result['data']

        TASK_STATUS[task_id]["message"] = "正在获取评测集..."
        TASK_STATUS[task_id]["progress"] = 2

        # 查询评测集
        cases_query = """
        SELECT ecp.* 
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
        WHERE escp.evaluation_set_id = %s
        """
        evaluation_set_result = execute_query(
            cases_query, eval_set_ids, fetch_all=True, return_dict=True)

        if not evaluation_set_result['success']:
            TASK_STATUS[task_id]["status"] = "failed"
            TASK_STATUS[task_id][
                "message"] = f"查询评测集信息失败: {evaluation_set_result.get('error')}"
            return

        if not evaluation_set_result['data']:
            TASK_STATUS[task_id]["status"] = "failed"
            TASK_STATUS[task_id]["message"] = "未找到任何指定的评测集"
            return

        eval_sets = evaluation_set_result['data']

        # 初始化结果数据
        results_data = []
        inference_tasks = []
        eval_sets_needing_inference = []

        TASK_STATUS[task_id]["message"] = "正在检查已有结果..."
        TASK_STATUS[task_id]["progress"] = 5

        # 检查已有的推理和评测结果
        all_eval_set_ids = [es['id'] for es in eval_sets]
        existing_inference_map = {}
        existing_evaluation_map = {}

        if all_eval_set_ids:
            batch_check_inference_query = """
            SELECT id, pkl_id FROM inference_result
            WHERE inference_config_id = %s AND pkl_id IN ({})
            """.format(','.join(['%s'] * len(all_eval_set_ids)))

            # 将元组展平为单个参数列表
            query_params = [config_id] + all_eval_set_ids

            existing_inference_results = execute_query(
                batch_check_inference_query,
                query_params,
                fetch_all=True,
                return_dict=True
            )

            if existing_inference_results['success'] and existing_inference_results['data']:
                for row in existing_inference_results['data']:
                    existing_inference_map[row['pkl_id']] = row['id']

                inference_ids_to_check = list(existing_inference_map.values())
                if inference_ids_to_check:
                    batch_check_eval_query = """
                    SELECT id, inference_result_id FROM evaluation_result
                    WHERE inference_result_id IN ({})
                    """.format(','.join(['%s'] * len(inference_ids_to_check)))
                    # 将元组展平为单个参数列表
                    query_params = inference_ids_to_check
                    existing_eval_results = execute_query(
                        batch_check_eval_query,
                        query_params,
                        fetch_all=True,
                        return_dict=True
                    )
                    if existing_eval_results['success'] and existing_eval_results['data']:
                        for row in existing_eval_results['data']:
                            existing_evaluation_map[row['inference_result_id']] = row['id']

        # 连接推理服务器的辅助函数
        async def _run_inference(stub, request, timeout):
            return await stub.Infer(request, timeout=timeout)

        try:
            # 更新状态
            TASK_STATUS[task_id]["message"] = "正在连接推理服务..."
            TASK_STATUS[task_id]["inferenceProgress"] = 5
            TASK_STATUS[task_id]["progress"] = 8

            # 连接推理服务器
            server_address = 'localhost:50051'
            channel_options = [
                ('grpc.max_receive_message_length', 32 * 1024 * 1024)  # 32 MB
            ]

            async with grpc.aio.insecure_channel(server_address, options=channel_options) as channel:
                logger.info("尝试连接到推理服务器...")
                stub = inference_server_pb2_grpc.InferenceServiceStub(channel)

                TASK_STATUS[task_id]["message"] = "正在加载推理模型..."
                TASK_STATUS[task_id]["inferenceProgress"] = 10
                TASK_STATUS[task_id]["progress"] = 10

                # 加载模型
                json_file_path = os.path.join(
                    '/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/database/json',
                    config_data['json_md5'] + '.json')
                pth_file_path = os.path.join(
                    '/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/database/pth',
                    config_data['pth_md5'] + '.pth')

                config = inference_server_pb2.InferenceConfig(
                    json_config_file=json_file_path,
                    pth_file=pth_file_path,
                    nearest_negative_anchor_num=config_data.get(
                        'nearest_negative_anchor_num', 1),
                    other_negative_anchor_num=config_data.get(
                        'other_negative_anchor_num', 0)
                )

                load_request = inference_server_pb2.LoadModelRequest(
                    config=config)
                try:
                    logger.info("加载模型...")
                    load_response = await stub.LoadModel(load_request, timeout=120)
                    if not load_response.success:
                        TASK_STATUS[task_id]["status"] = "failed"
                        TASK_STATUS[task_id]["message"] = f"模型加载失败: {load_response.message}"
                        return
                    logger.info("模型加载成功。")
                except grpc.aio.AioRpcError as e:
                    TASK_STATUS[task_id]["status"] = "failed"
                    TASK_STATUS[task_id]["message"] = f"模型加载失败或连接推理服务器超时: {e.details()}"
                    return

                # 更新状态
                TASK_STATUS[task_id]["message"] = "正在准备推理请求..."
                TASK_STATUS[task_id]["inferenceProgress"] = 15
                TASK_STATUS[task_id]["progress"] = 15

                # 准备推理请求
                inference_requests_map = {}
                tasks_to_run = []
                eval_sets_needing_inference_tracking = []

                for eval_set in eval_sets:
                    eval_set_id = eval_set['id']
                    pkl_path = os.path.join(
                        eval_set['pkl_dir'], eval_set['pkl_name'])

                    # 检查是否已有推理和评测结果，不再重复处理
                    inference_result_id = existing_inference_map.get(
                        eval_set_id)
                    if inference_result_id:
                        evaluation_result_id = existing_evaluation_map.get(
                            inference_result_id)

                        if evaluation_result_id:
                            results_data.append({
                                "eval_set_id": eval_set_id,
                                "result_id": inference_result_id,
                                "success": True,
                                "skipped_inference": True,
                                "evaluation": {
                                    "success": True,
                                    "skipped_evaluation": True,
                                    "evaluation_result_id": evaluation_result_id,
                                }
                            })
                            continue
                        else:
                            results_data.append({
                                "eval_set_id": eval_set_id,
                                "result_id": inference_result_id,
                                "success": True,
                                "skipped_inference": True
                            })
                            continue

                    # 准备推理请求
                    request_id = f"task_{config_id}_{eval_set_id}"
                    infer_request = inference_server_pb2.InferenceRequest(
                        pickle_file_path=pkl_path,
                        request_id=request_id
                    )
                    task = asyncio.create_task(_run_inference(
                        stub, infer_request, 120))
                    tasks_to_run.append(task)
                    inference_requests_map[request_id] = eval_set
                    eval_sets_needing_inference_tracking.append(eval_set)

                # 更新状态
                TASK_STATUS[task_id]["message"] = "正在提交推理请求..."
                TASK_STATUS[task_id]["inferenceProgress"] = 20
                TASK_STATUS[task_id]["progress"] = 20

                # 处理推理请求
                pending_request_ids = set()
                if tasks_to_run:
                    total_tasks = len(tasks_to_run)
                    TASK_STATUS[task_id]["message"] = f"正在发送 {total_tasks} 个推理请求..."
                    logger.info(f"开始并发发送 {total_tasks} 个推理请求...")

                    # 发送所有推理请求
                    inference_acceptance_responses = await asyncio.gather(*tasks_to_run, return_exceptions=True)
                    logger.info("所有推理请求发送完成。处理接受状态...")

                    # 更新状态
                    TASK_STATUS[task_id]["message"] = "推理请求已发送，处理响应..."
                    TASK_STATUS[task_id]["inferenceProgress"] = 30
                    TASK_STATUS[task_id]["progress"] = 25

                    # 处理推理请求的响应
                    success_count = 0
                    for eval_set, response in zip(eval_sets_needing_inference_tracking, inference_acceptance_responses):
                        eval_set_id = eval_set['id']
                        request_id = f"task_{config_id}_{eval_set_id}"

                        # 处理异常响应
                        if isinstance(response, Exception):
                            error_detail = f"未知错误: {response}"
                            if isinstance(response, grpc.aio.AioRpcError):
                                error_detail = f"发送推理请求失败: {response.code()} - {response.details()}"
                            elif isinstance(response, asyncio.TimeoutError):
                                error_detail = "发送推理请求超时"

                            logger.info(
                                f"发送推理请求失败 for eval_set {eval_set_id}: {error_detail}")
                            results_data.append({
                                "eval_set_id": eval_set_id,
                                "error": error_detail,
                                "success": False
                            })
                            if request_id in inference_requests_map:
                                del inference_requests_map[request_id]

                        # 处理被拒绝的请求
                        elif not response.accepted:
                            error_detail = f"推理服务器拒绝了请求 (request_id: {response.request_id})"
                            logger.info(f"推理请求被拒绝 for eval_set {eval_set_id}")
                            results_data.append({
                                "eval_set_id": eval_set_id,
                                "error": error_detail,
                                "success": False
                            })
                            if request_id in inference_requests_map:
                                del inference_requests_map[request_id]

                        # 处理接受的请求
                        else:
                            success_count += 1
                            pending_request_ids.add(response.request_id)
                            results_data.append({
                                "eval_set_id": eval_set_id,
                                "request_id": response.request_id,
                                "success": True
                            })

                    # 更新成功率
                    if total_tasks > 0:
                        success_rate = int((success_count / total_tasks) * 100)
                        TASK_STATUS[task_id]["message"] = f"推理请求提交成功率: {success_rate}%"
                else:
                    logger.info("没有需要发送的新推理请求。")

                # 更新状态
                TASK_STATUS[task_id]["message"] = "正在获取推理结果..."
                TASK_STATUS[task_id]["inferenceProgress"] = 50
                TASK_STATUS[task_id]["progress"] = 30

                # 获取推理结果
                if pending_request_ids:
                    num_pending = len(pending_request_ids)
                    logger.info(
                        f"开始通过 StreamResults 获取 {num_pending} 个推理结果...")
                    TASK_STATUS[task_id]["message"] = f"正在获取 {num_pending} 个推理结果..."

                    stream_request = inference_server_pb2.StreamResultsRequest(
                        request_ids=list(pending_request_ids),
                        timeout_ms=300 * 1000
                    )
                    processed_request_ids = set()

                    try:
                        # 创建一个列表来收集所有结果
                        collected_results = []

                        # 设置处理计数
                        processed_count = 0

                        # 收集推理结果
                        async for result_proto in stub.StreamResults(stream_request):
                            request_id = result_proto.request_id
                            if request_id not in inference_requests_map:
                                logger.info(
                                    f"警告: 收到未知 request_id 的结果: {request_id}")
                                continue

                            eval_set = inference_requests_map[request_id]
                            eval_set_id = eval_set['id']
                            processed_request_ids.add(request_id)
                            processed_count += 1

                            # 更新进度
                            progress_pct = int(
                                (processed_count / num_pending) * 70) + 30
                            inf_progress_pct = int(
                                (processed_count / num_pending) * 50) + 50
                            TASK_STATUS[task_id]["inferenceProgress"] = min(
                                100, inf_progress_pct)
                            TASK_STATUS[task_id]["progress"] = min(
                                60, progress_pct)
                            if processed_count % 10 == 0 or processed_count == num_pending:
                                TASK_STATUS[task_id]["message"] = f"已处理 {processed_count}/{num_pending} 个推理结果..."

                            # 将结果和元信息一起存储
                            collected_results.append({
                                "result_proto": result_proto,
                                "request_id": request_id,
                                "eval_set": eval_set,
                                "eval_set_id": eval_set_id
                            })

                        # 更新状态
                        TASK_STATUS[task_id]["message"] = "所有推理结果已获取，正在保存到数据库..."
                        TASK_STATUS[task_id]["inferenceProgress"] = 100
                        TASK_STATUS[task_id]["progress"] = 60

                        # 批量保存结果到数据库
                        logger.info(
                            f"所有推理结果已收集完成，共 {len(collected_results)} 个结果。开始保存到数据库...")

                        # 批量处理
                        batch_size = 250
                        batch_data = []
                        result_map = {}  # 用于跟踪每个eval_set_id的result_id

                        for result_item in collected_results:
                            result_proto = result_item["result_proto"]
                            eval_set = result_item["eval_set"]
                            eval_set_id = result_item["eval_set_id"]

                            result_binary = result_proto.SerializeToString()
                            batch_data.append(
                                (config_id, result_binary, eval_set['id']))

                            if len(batch_data) >= batch_size:
                                # 批量插入数据库
                                insert_query = """
                                INSERT INTO inference_result 
                                (inference_config_id, result_binary, pkl_id) 
                                VALUES (%s, %s, %s)
                                """
                                result = execute_query(
                                    insert_query, batch_data, execute_many=True)

                                if result['success']:
                                    get_inference_result_id_query = """
                                    SELECT id, pkl_id FROM inference_result 
                                    WHERE inference_config_id = %s AND pkl_id IN ({})
                                    """.format(','.join(['%s'] * len(batch_data)))
                                    param = [config_id] + [data[2]
                                                           for data in batch_data]
                                    results = execute_query(
                                        get_inference_result_id_query, param, fetch_all=True, return_dict=True)

                                    if results['success'] and results['data']:
                                        for result in results['data']:
                                            pkl_id = result['pkl_id']
                                            result_map[pkl_id] = result['id']

                                            # 添加或更新结果列表
                                            result_entry = next(
                                                (r for r in results_data if r.get("eval_set_id") == pkl_id), None)
                                            if result_entry:
                                                result_entry.update({
                                                    "result_id": result['id'],
                                                    "success": True
                                                })
                                            else:
                                                results_data.append({
                                                    "eval_set_id": pkl_id,
                                                    "result_id": result['id'],
                                                    "success": True
                                                })
                                else:
                                    for data in batch_data:
                                        eval_set_id = data[2]
                                        # 添加或更新结果列表
                                        result_entry = next((r for r in results_data if r.get(
                                            "eval_set_id") == eval_set_id), None)
                                        if result_entry:
                                            result_entry.update({
                                                "error": "保存结果失败",
                                                "success": False
                                            })
                                        else:
                                            results_data.append({
                                                "eval_set_id": eval_set_id,
                                                "error": "保存结果失败",
                                                "success": False
                                            })

                                logger.info(f"批量保存了 {len(batch_data)} 条推理结果")
                                batch_data = []

                        # 处理剩余批次
                        if batch_data:
                            insert_query = """
                            INSERT INTO inference_result 
                            (inference_config_id, result_binary, pkl_id) 
                            VALUES (%s, %s, %s)
                            """
                            result = execute_query(
                                insert_query, batch_data, execute_many=True)

                            if result['success']:
                                get_inference_result_id_query = """
                                SELECT id, pkl_id FROM inference_result 
                                WHERE inference_config_id = %s AND pkl_id IN ({})
                                """.format(','.join(['%s'] * len(batch_data)))
                                param = [config_id] + [data[2]
                                                       for data in batch_data]
                                results = execute_query(
                                    get_inference_result_id_query, param, fetch_all=True, return_dict=True)
                                if results['success'] and results['data']:
                                    for result in results['data']:
                                        results_data.append({
                                            "eval_set_id": result['pkl_id'],
                                            "result_id": result['id'],
                                            "success": True
                                        })
                                else:
                                    for data in batch_data:
                                        results_data.append({
                                            "eval_set_id": data[2],
                                            "error": "获取结果ID失败",
                                            "success": False
                                        })
                            else:
                                for data in batch_data:
                                    results_data.append({
                                        "eval_set_id": data[2],
                                        "error": "保存结果失败",
                                        "success": False
                                    })
                            logger.info(f"保存了剩余的 {len(batch_data)} 条推理结果")

                    except Exception as e:
                        logger.error(f"获取推理结果时出错: {str(e)}")
                        TASK_STATUS[task_id]["message"] = f"获取推理结果时出错: {str(e)}"

                        # 处理未完成的请求
                        missing_results_ids = pending_request_ids - processed_request_ids
                        if missing_results_ids:
                            logger.info(
                                f"警告: {len(missing_results_ids)} 个推理任务未收到结果")
                            for req_id in missing_results_ids:
                                result_entry = next(
                                    (r for r in results_data if r.get("request_id") == req_id), None)
                                if result_entry and result_entry.get("success") is None:
                                    result_entry.update({
                                        "error": "未收到推理结果",
                                        "success": False
                                    })

                # 更新状态
                TASK_STATUS[task_id]["message"] = "推理阶段完成，准备评测阶段..."
                TASK_STATUS[task_id]["evaluationProgress"] = 5
                TASK_STATUS[task_id]["progress"] = 65

                # 连接评测服务器
                logger.info("连接评测服务器...")
                evaluation_server_address = 'localhost:50052'
                evaluation_channel = grpc.aio.insecure_channel(
                    evaluation_server_address)

                try:
                    await asyncio.wait_for(evaluation_channel.channel_ready(), timeout=5)
                    logger.info("已连接到评测服务器")
                    evaluation_stub = evaluation_server_pb2_grpc.EvaluationServiceStub(
                        evaluation_channel)

                    # 更新状态
                    TASK_STATUS[task_id]["message"] = "已连接到评测服务器，准备评测任务..."
                    TASK_STATUS[task_id]["evaluationProgress"] = 10
                    TASK_STATUS[task_id]["progress"] = 70

                    # 查找成功完成推理的结果
                    successful_results = [r for r in results_data if r.get(
                        "success") is True and "result_id" in r]

                    if not successful_results:
                        logger.info("没有成功的推理结果可以进行评测")
                        TASK_STATUS[task_id]["message"] = "没有可评测的推理结果"
                        TASK_STATUS[task_id]["status"] = "failed"
                        return

                    logger.info(f"开始创建评测任务，共 {len(successful_results)} 个...")
                    TASK_STATUS[task_id]["message"] = f"正在准备 {len(successful_results)} 个评测任务..."

                    # 获取推理结果的二进制数据
                    inference_result_ids = [r["result_id"] for r in successful_results
                                            if r.get("success") is True and not r.get("evaluation", {}).get("success")]

                    binary_map = {}  # 存储推理结果二进制数据

                    if inference_result_ids:
                        # 批量查询推理结果
                        placeholders = ','.join(
                            ['%s'] * len(inference_result_ids))
                        select_query = f"SELECT id, result_binary FROM inference_result WHERE id IN ({placeholders})"
                        binary_results = execute_query(
                            select_query, inference_result_ids, fetch_all=True, return_dict=True)

                        if binary_results['success'] and binary_results['data']:
                            binary_map = {row['id']: row['result_binary']
                                          for row in binary_results['data']}

                    # 创建评测任务
                    evaluation_tasks = []
                    total_eval_tasks = len(successful_results)
                    completed_eval_tasks = 0

                    for new_result in successful_results:
                        # 跳过已评测的结果
                        if new_result.get("evaluation", {}).get("success"):
                            completed_eval_tasks += 1
                            continue

                        try:
                            # 获取对应的pkl路径
                            eval_set_id = new_result["eval_set_id"]
                            current_eval_set = next(
                                (es for es in eval_sets if es['id'] == eval_set_id), None)

                            if not current_eval_set:
                                logger.info(
                                    f"错误: 无法在 eval_sets 中找到 ID 为 {eval_set_id} 的数据")
                                new_result["evaluation"] = {
                                    "success": False,
                                    "error": "内部错误：无法找到评测集元数据"
                                }
                                completed_eval_tasks += 1
                                continue

                            pkl_path = os.path.join(
                                current_eval_set['pkl_dir'], current_eval_set['pkl_name'])
                            inference_result_id = new_result["result_id"]

                            # 获取推理结果二进制数据
                            result_binary = binary_map.get(inference_result_id)
                            if not result_binary:
                                error_msg = f"无法获取推理结果数据 for ID {inference_result_id}"
                                logger.info(f"错误: {error_msg}")
                                new_result["evaluation"] = {
                                    "success": False,
                                    "error": error_msg
                                }
                                completed_eval_tasks += 1
                                continue

                            # 解析二进制数据
                            inference_output = inference_result_pb2.InferenceOutput()
                            inference_output.ParseFromString(result_binary)

                            # 准备评测请求
                            request_id = f"eval_{config_id}_{eval_set_id}"
                            eval_request = evaluation_server_pb2.EvaluationRequest(
                                request_id=request_id,
                                pickle_file_path=pkl_path,
                                inference_result=inference_output
                            )

                            # 创建评测任务
                            evaluation_task = asyncio.create_task(
                                process_evaluation(
                                    evaluation_stub, eval_request, new_result)
                            )
                            evaluation_tasks.append(evaluation_task)

                        except Exception as e:
                            logger.info(
                                f"为 eval_set {new_result.get('eval_set_id')} 准备评测任务时出错: {str(e)}")
                            new_result["evaluation"] = {
                                "success": False,
                                "error": f"准备评测过程发生异常: {str(e)}"
                            }
                            completed_eval_tasks += 1

                    # 更新状态
                    active_tasks = len(evaluation_tasks)
                    TASK_STATUS[task_id]["message"] = f"开始执行 {active_tasks} 个评测任务..."
                    TASK_STATUS[task_id]["evaluationProgress"] = 20
                    TASK_STATUS[task_id]["progress"] = 75

                    # 执行评测任务
                    if evaluation_tasks:
                        logger.info(f"等待 {len(evaluation_tasks)} 个评测任务完成...")

                        # 使用 as_completed 按完成顺序处理结果
                        for i, completed_task in enumerate(asyncio.as_completed(evaluation_tasks)):
                            try:
                                updated_result = await completed_task
                                completed_eval_tasks += 1

                                # 更新进度
                                eval_progress = int(
                                    (completed_eval_tasks / total_eval_tasks) * 80) + 20
                                total_progress = int(
                                    (completed_eval_tasks / total_eval_tasks) * 20) + 75
                                TASK_STATUS[task_id]["evaluationProgress"] = min(
                                    100, eval_progress)
                                TASK_STATUS[task_id]["progress"] = min(
                                    95, total_progress)

                                if i % 10 == 0 or completed_eval_tasks == total_eval_tasks:
                                    TASK_STATUS[task_id][
                                        "message"] = f"已完成 {completed_eval_tasks}/{total_eval_tasks} 个评测任务..."

                            except Exception as e:
                                logger.info(f"评测任务异常: {str(e)}")
                                completed_eval_tasks += 1

                        # 更新状态
                        TASK_STATUS[task_id]["message"] = "所有评测任务已完成，保存结果中..."
                        TASK_STATUS[task_id]["evaluationProgress"] = 100
                        TASK_STATUS[task_id]["progress"] = 95

                        # 收集需要保存的评测结果
                        results_to_save = []
                        for result_item in successful_results:
                            if (result_item.get("evaluation", {}).get("success") and
                                "result_data" in result_item.get("evaluation", {}) and
                                    not result_item.get("evaluation", {}).get("skipped_evaluation", False)):
                                results_to_save.append({
                                    "inference_result_id": result_item["result_id"],
                                    "evaluation_data": result_item["evaluation"]["result_data"]
                                })

                        # 批量保存评测结果
                        if results_to_save:
                            logger.info(
                                f"开始批量保存 {len(results_to_save)} 条评测结果...")

                            # 批量保存
                            batch_size = 2000
                            total_saved = 0

                            for i in range(0, len(results_to_save), batch_size):
                                batch = results_to_save[i:i+batch_size]
                                save_result = batch_save_evaluation_results(
                                    batch)

                                if save_result["success"]:
                                    total_saved += save_result["count"]
                                else:
                                    logger.error(
                                        f"批量保存评测结果失败: {save_result.get('error')}")

                            logger.info(f"成功保存了 {total_saved} 条评测结果")

                    # 任务完成
                    TASK_STATUS[task_id]["message"] = "所有处理已完成!"
                    TASK_STATUS[task_id]["progress"] = 100
                    TASK_STATUS[task_id]["status"] = "completed"

                    # 保存任务结果
                    TASK_STATUS[task_id]["results"] = results_data
                    TASK_STATUS[task_id]["completed_at"] = datetime.now(
                    ).isoformat()
                    # TASK_STATUS[task_id]["total_time"] = (TASK_STATUS[task_id]["completed_at"] -
                    #                                       TASK_STATUS[task_id]["created_at"]).total_seconds()

                except asyncio.TimeoutError:
                    TASK_STATUS[task_id]["status"] = "failed"
                    TASK_STATUS[task_id]["message"] = "连接评测服务器超时"
                    logger.error("连接评测服务器超时")
                    return

        except Exception as e:
            TASK_STATUS[task_id]["status"] = "failed"
            TASK_STATUS[task_id]["message"] = f"处理任务时发生错误: {str(e)}"
            logger.error(f"处理评测任务时发生错误: {str(e)}")
            import traceback
            error_detail = traceback.format_exc()
            logger.error(f"处理评测任务时发生错误: {error_detail}")

    except Exception as e:
        import traceback
        error_detail = traceback.format_exc()
        logger.error(f"评测任务处理异常: {error_detail}")

        # 更新状态为失败
        TASK_STATUS[task_id]["status"] = "failed"
        TASK_STATUS[task_id]["message"] = f"处理评测任务时发生错误: {str(e)}"
        TASK_STATUS[task_id]["error"] = error_detail


@router.post("/api/evaluation_tasks_old")
async def create_evaluation_task(data: dict):
    """创建新的评测任务 (使用 asyncio 并发)"""
    if 'inference_config_id' not in data or 'evaluation_set_ids' not in data:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    config_id = data['inference_config_id']
    eval_set_ids = data['evaluation_set_ids']

    if not isinstance(eval_set_ids, list) or len(eval_set_ids) == 0:
        raise HTTPException(status_code=400, detail="评测集ID列表无效")

    # 验证配置ID是否存在
    query = "SELECT * FROM inference_config WHERE id = %s"
    inference_config_result = execute_query(
        query, (config_id,), fetch_one=True, return_dict=True)  # 添加 return_dict=True

    if not inference_config_result['success'] or not inference_config_result['data']:
        raise HTTPException(status_code=404, detail="推理配置不存在")

    # 获取推理配置信息
    config_data = inference_config_result['data']

    cases_query = """
    SELECT ecp.* 
    FROM evaluation_case_pool ecp
    JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
    WHERE escp.evaluation_set_id = %s
    """
    evaluation_set_result = execute_query(
        cases_query, eval_set_ids, fetch_all=True, return_dict=True)

    if not evaluation_set_result['success']:
        raise HTTPException(
            status_code=500, detail=f"查询评测集信息失败: {evaluation_set_result.get('error')}")

    if not evaluation_set_result['data']:

        raise HTTPException(status_code=404, detail="未找到任何指定的评测集")

    eval_sets = evaluation_set_result['data']

    results_data = []  # 存储每个评测集的最终结果状态
    inference_tasks = []
    eval_sets_needing_inference = []

    all_eval_set_ids = [es['id'] for es in eval_sets]
    existing_inference_map = {}
    existing_evaluation_map = {}

    if all_eval_set_ids:
        batch_check_inference_query = """
        SELECT id, pkl_id FROM inference_result
        WHERE inference_config_id = %s AND pkl_id IN ({})
        """.format(','.join(['%s'] * len(all_eval_set_ids)))

        # 将元组展平为单个参数列表
        query_params = [config_id] + all_eval_set_ids

        existing_inference_results = execute_query(
            batch_check_inference_query,
            query_params,
            fetch_all=True,
            return_dict=True
        )

        if existing_inference_results['success'] and existing_inference_results['data']:
            for row in existing_inference_results['data']:
                existing_inference_map[row['pkl_id']] = row['id']

            inference_ids_to_check = list(existing_inference_map.values())
            if inference_ids_to_check:
                batch_check_eval_query = """
                SELECT id, inference_result_id FROM evaluation_result
                WHERE inference_result_id IN ({})
                """.format(','.join(['%s'] * len(inference_ids_to_check)))
                # 将元组展平为单个参数列表
                query_params = inference_ids_to_check
                existing_eval_results = execute_query(
                    batch_check_eval_query,
                    query_params,
                    fetch_all=True,
                    return_dict=True
                )
                if existing_eval_results['success'] and existing_eval_results['data']:
                    for row in existing_eval_results['data']:
                        existing_evaluation_map[row['inference_result_id']] = row['id']

    async def _run_inference(stub, request, timeout):
        return await stub.Infer(request, timeout=timeout)

    try:
        server_address = 'localhost:50051'
        channel_options = [
            ('grpc.max_receive_message_length', 32 * 1024 * 1024)  # 32 MB
        ]
        async with grpc.aio.insecure_channel(server_address, options=channel_options) as channel:
            logger.info("尝试连接到推理服务器...")
            stub = inference_server_pb2_grpc.InferenceServiceStub(channel)
            logger.info("推理服务存根已创建。")
            json_file_path = os.path.join(
                '/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/database/json', config_data['json_md5']+'.json')
            pth_file_path = os.path.join(
                '/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/database/pth', config_data['pth_md5']+'.pth')

            config = inference_server_pb2.InferenceConfig(
                json_config_file=json_file_path,
                pth_file=pth_file_path,
                nearest_negative_anchor_num=config_data.get(
                    'nearest_negative_anchor_num', 1),
                other_negative_anchor_num=config_data.get(
                    'other_negative_anchor_num', 0)
            )
            load_request = inference_server_pb2.LoadModelRequest(config=config)
            try:
                logger.info("加载模型...")
                load_response = await stub.LoadModel(load_request, timeout=120)
                if not load_response.success:
                    raise HTTPException(
                        status_code=500, detail=f"模型加载失败: {load_response.message}")
                logger.info("模型加载成功。")
            except grpc.aio.AioRpcError as e:
                raise HTTPException(
                    status_code=503, detail=f"模型加载失败或连接推理服务器超时: {e.details()}")

            logger.info("处理评测集...")
            inference_requests_map = {}
            tasks_to_run = []
            eval_sets_needing_inference_tracking = []

            for eval_set in eval_sets:
                eval_set_id = eval_set['id']
                pkl_path = os.path.join(
                    eval_set['pkl_dir'], eval_set['pkl_name'])

                inference_result_id = existing_inference_map.get(eval_set_id)
                if inference_result_id:
                    evaluation_result_id = existing_evaluation_map.get(
                        inference_result_id)

                    if evaluation_result_id:
                        results_data.append({
                            "eval_set_id": eval_set_id,
                            "result_id": inference_result_id,
                            "success": True,
                            "skipped_inference": True,
                            "evaluation": {
                                "success": True,
                                "skipped_evaluation": True,
                                "evaluation_result_id": evaluation_result_id,
                            }
                        })
                        continue
                    else:
                        results_data.append({
                            "eval_set_id": eval_set_id,
                            "result_id": inference_result_id,
                            "success": True,
                            "skipped_inference": True
                        })
                        continue

                # logger.info(f"需要为 eval_set {eval_set_id} 执行推理")
                request_id = f"task_{config_id}_{eval_set_id}"
                infer_request = inference_server_pb2.InferenceRequest(
                    pickle_file_path=pkl_path,
                    request_id=request_id
                )
                task = asyncio.create_task(_run_inference(
                    stub, infer_request, 120))
                tasks_to_run.append(task)
                inference_requests_map[request_id] = eval_set
                eval_sets_needing_inference_tracking.append(eval_set)

            pending_request_ids = set()
            if tasks_to_run:
                logger.info(f"开始并发发送 {len(tasks_to_run)} 个推理请求...")
                inference_acceptance_responses = await asyncio.gather(*tasks_to_run, return_exceptions=True)
                logger.info("所有推理请求发送完成。处理接受状态...")

                for eval_set, response in tqdm(zip(eval_sets_needing_inference_tracking, inference_acceptance_responses)):
                    eval_set_id = eval_set['id']
                    request_id = f"task_{config_id}_{eval_set_id}"

                    if isinstance(response, Exception):
                        error_detail = f"未知错误: {response}"
                        if isinstance(response, grpc.aio.AioRpcError):
                            error_detail = f"发送推理请求失败: {response.code()} - {response.details()}"
                        elif isinstance(response, asyncio.TimeoutError):
                            error_detail = "发送推理请求超时"
                        logger.info(
                            f"发送推理请求失败 for eval_set {eval_set_id}: {error_detail}")
                        results_data.append({
                            "eval_set_id": eval_set_id,
                            "error": error_detail,
                            "success": False
                        })
                        if request_id in inference_requests_map:
                            del inference_requests_map[request_id]
                    elif not response.accepted:
                        error_detail = f"推理服务器拒绝了请求 (request_id: {response.request_id})"
                        logger.info(f"推理请求被拒绝 for eval_set {eval_set_id}")
                        results_data.append({
                            "eval_set_id": eval_set_id,
                            "error": error_detail,
                            "success": False
                        })
                        if request_id in inference_requests_map:
                            del inference_requests_map[request_id]
                    else:
                        # logger.info(
                        #     f"推理请求已接受 for eval_set {eval_set_id} (request_id: {response.request_id})")
                        pending_request_ids.add(response.request_id)
                        results_data.append({
                            "eval_set_id": eval_set_id,
                            "request_id": response.request_id,
                            "success": True
                        })

            else:
                logger.info("没有需要发送的新推理请求。")

            if pending_request_ids:
                logger.info(
                    f"开始通过 StreamResults 获取 {len(pending_request_ids)} 个推理结果...")
                stream_request = inference_server_pb2.StreamResultsRequest(
                    request_ids=list(pending_request_ids),
                    timeout_ms=300 * 1000
                )
                processed_request_ids = set()
                evaluation_tasks = []  # 存储所有评测任务
                temp_files_to_clean = []  # 临时文件列表





                try:
                    # 创建一个列表来收集所有结果
                    collected_results = []

                    # 仅收集结果，不执行任何操作
                    async for result_proto in stub.StreamResults(stream_request):
                        request_id = result_proto.request_id
                        if request_id not in inference_requests_map:
                            logger.info(
                                f"警告: 收到未知 request_id 的结果: {request_id}")
                            continue

                        eval_set = inference_requests_map[request_id]
                        eval_set_id = eval_set['id']
                        processed_request_ids.add(request_id)

                        # 将结果和元信息一起存储，等待后续处理
                        collected_results.append({
                            "result_proto": result_proto,
                            "request_id": request_id,
                            "eval_set": eval_set,
                            "eval_set_id": eval_set_id
                        })

                    # 所有结果收集完成后，执行数据库插入操作
                    logger.info(
                        f"所有推理结果已收集完成，共 {len(collected_results)} 个结果。开始保存到数据库...")

                    # 批量处理所有收集的结果
                    batch_size = 250
                    batch_data = []

                    for result_item in collected_results:
                        result_proto = result_item["result_proto"]
                        eval_set = result_item["eval_set"]
                        eval_set_id = result_item["eval_set_id"]

                        result_binary = result_proto.SerializeToString()
                        batch_data.append(
                            (config_id, result_binary, eval_set['id']))

                        if len(batch_data) >= batch_size:
                            # 批量插入数据库
                            insert_query = """
                            INSERT INTO inference_result 
                            (inference_config_id, result_binary, pkl_id) 
                            VALUES (%s, %s, %s)
                            """
                            result = execute_query(
                                insert_query, batch_data, execute_many=True)

                            if result['success']:
                                get_inference_result_id_query = """
                                SELECT id, pkl_id FROM inference_result 
                                WHERE inference_config_id = %s AND pkl_id IN ({})
                                """.format(','.join(['%s'] * len(batch_data)))
                                param = [config_id] + [data[2]
                                                       for data in batch_data]
                                results = execute_query(
                                    get_inference_result_id_query, param, fetch_all=True, return_dict=True)
                                if results['success'] and results['data']:
                                    for result in results['data']:
                                        results_data.append({
                                            "eval_set_id": result['pkl_id'],
                                            "result_id": result['id'],
                                            "success": True
                                        })
                                else:
                                    for data in batch_data:
                                        results_data.append({
                                            "eval_set_id": data[2],
                                            "error": "获取结果ID失败",
                                            "success": False
                                        })
                            else:
                                for data in batch_data:
                                    results_data.append({
                                        "eval_set_id": data[2],
                                        "error": "保存结果失败",
                                        "success": False
                                    })
                            logger.info(f"批量保存了 {len(batch_data)} 条推理结果")
                            batch_data = []

                    # 保存剩余未满批次的数据
                    if batch_data:
                        insert_query = """
                        INSERT INTO inference_result 
                        (inference_config_id, result_binary, pkl_id) 
                        VALUES (%s, %s, %s)
                        """
                        result = execute_query(
                            insert_query, batch_data, execute_many=True)

                        if result['success']:
                            get_inference_result_id_query = """
                            SELECT id, pkl_id FROM inference_result 
                            WHERE inference_config_id = %s AND pkl_id IN ({})
                            """.format(','.join(['%s'] * len(batch_data)))
                            param = [config_id] + [data[2]
                                                   for data in batch_data]
                            results = execute_query(
                                get_inference_result_id_query, param, fetch_all=True, return_dict=True)
                            if results['success'] and results['data']:
                                for result in results['data']:
                                    results_data.append({
                                        "eval_set_id": result['pkl_id'],
                                        "result_id": result['id'],
                                        "success": True
                                    })
                            else:
                                for data in batch_data:
                                    results_data.append({
                                        "eval_set_id": data[2],
                                        "error": "获取结果ID失败",
                                        "success": False
                                    })
                        else:
                            for data in batch_data:
                                results_data.append({
                                    "eval_set_id": data[2],
                                    "error": "保存结果失败",
                                    "success": False
                                })
                        logger.info(f"保存了剩余的 {len(batch_data)} 条推理结果")

                    # 数据库插入完成后，创建并启动所有评测任务


                except grpc.aio.AioRpcError as e:
                    logger.info(
                        f"StreamResults RPC 失败: {e.code()} - {e.details()}")
                    for req_id in pending_request_ids - processed_request_ids:
                        result_entry = next(
                            (r for r in results_data if r.get("request_id") == req_id), None)
                        if result_entry:
                            result_entry.update({
                                "error": f"StreamResults RPC 失败: {e.details()}",
                                "success": False
                            })
                except asyncio.TimeoutError:
                    logger.info(f"StreamResults 超时")
                    for req_id in pending_request_ids - processed_request_ids:
                        result_entry = next(
                            (r for r in results_data if r.get("request_id") == req_id), None)
                        if result_entry:
                            result_entry.update({
                                "error": "获取推理结果超时",
                                "success": False
                            })
                except Exception as e:
                    logger.info(f"处理 StreamResults 时发生意外错误: {str(e)}")
                    for req_id in pending_request_ids - processed_request_ids:
                        result_entry = next(
                            (r for r in results_data if r.get("request_id") == req_id), None)
                        if result_entry:
                            result_entry.update({
                                "error": f"处理StreamResults时异常: {str(e)}",
                                "success": False
                            })
                finally:
                    # 清理临时文件
                    for f_path in temp_files_to_clean:
                        try:
                            if os.path.exists(f_path):
                                os.remove(f_path)
                                logger.info(f"已删除临时文件: {f_path}")
                        except Exception as e:
                            logger.info(f"警告: 删除临时文件失败 {f_path}: {str(e)}")

                missing_results_ids = pending_request_ids - processed_request_ids
                if missing_results_ids:
                    logger.info(f"警告: {len(missing_results_ids)} 个推理任务未收到结果")
                    for req_id in missing_results_ids:
                        result_entry = next(
                            (r for r in results_data if r.get("request_id") == req_id), None)
                        if result_entry and result_entry.get("success") is None:
                            result_entry.update({
                                "error": "未收到推理结果",
                                "success": False
                            })

                # 提前连接评测服务器
            logger.info("连接评测服务器...")
            evaluation_server_address = 'localhost:50052'
            evaluation_channel = grpc.aio.insecure_channel(
                evaluation_server_address)
            try:
                await asyncio.wait_for(evaluation_channel.channel_ready(), timeout=5)
                logger.info("已连接到评测服务器")
                evaluation_stub = evaluation_server_pb2_grpc.EvaluationServiceStub(
                    evaluation_channel)
            except asyncio.TimeoutError:
                raise HTTPException(
                    status_code=503, detail=f"连接评测服务器超时: {evaluation_server_address}")
            evaluation_input = []
            evaluation_tasks = []
            successful_results = [r for r in results_data if r.get(
                "success") is True and "result_id" in r]

            logger.info(f"开始创建评测任务，共 {len(successful_results)} 个...")
            inference_result_ids = [r["result_id"] for r in successful_results
                                    if r.get("success") is True and not r.get("evaluation", {}).get("success")]
            if inference_result_ids:
                # 构建 IN 查询
                placeholders = ','.join(['%s'] * len(inference_result_ids))
                select_query = f"SELECT id, result_binary FROM inference_result WHERE id IN ({placeholders})"
                binary_results = execute_query(
                    select_query, inference_result_ids, fetch_all=True, return_dict=True)

                if binary_results['success'] and binary_results['data']:
                    # 创建结果ID到二进制数据的映射
                    binary_map = {row['id']: row['result_binary']
                                  for row in binary_results['data']}
                else:
                    logger.error(
                        f"无法批量获取推理结果: {binary_results.get('error', '未知错误')}")
                    binary_map = {}
            else:
                binary_map = {}

            for new_result in successful_results:
                if new_result.get("evaluation", {}).get("success"):
                    logger.info(
                        f"跳过评测 for eval_set {new_result['eval_set_id']} (result_id: {new_result['result_id']})")
                    continue

                try:
                    # 获取对应的pkl路径
                    eval_set_id = new_result["eval_set_id"]
                    current_eval_set = next(
                        (es for es in eval_sets if es['id'] == eval_set_id), None)
                    if not current_eval_set:
                        logger.info(
                            f"错误: 无法在 eval_sets 中找到 ID 为 {eval_set_id} 的数据")
                        new_result["evaluation"] = {
                            "success": False, "error": "内部错误：无法找到评测集元数据"}
                        continue

                    pkl_path = os.path.join(
                        current_eval_set['pkl_dir'], current_eval_set['pkl_name'])
                    inference_result_id = new_result["result_id"]

                    # logger.info(
                    #     f"准备评测 result ID {inference_result_id} (eval_set {eval_set_id})")

                    result_binary = binary_map.get(inference_result_id)
                    if not result_binary:
                        error_msg = f"无法获取推理结果数据 for ID {inference_result_id}"
                        logger.info(f"错误: {error_msg}")
                        new_result["evaluation"] = {
                            "success": False, "error": error_msg}
                        continue

                    inference_output = inference_result_pb2.InferenceOutput()
                    inference_output.ParseFromString(result_binary)

                    request_id = f"eval_{config_id}_{eval_set_id}"
                    eval_request = evaluation_server_pb2.EvaluationRequest(
                        request_id=request_id,
                        pickle_file_path=pkl_path,
                        inference_result=inference_output
                    )

                    # 创建异步任务，只包含 GRPC 调用部分
                    evaluation_task = asyncio.create_task(
                        process_evaluation(
                            evaluation_stub, eval_request, new_result)
                    )
                    evaluation_tasks.append(evaluation_task)
                except Exception as e:
                    logger.info(
                        f"为 eval_set {new_result.get('eval_set_id')} 准备评测任务时出错: {str(e)}")
                    new_result["evaluation"] = {
                        "success": False,
                        "error": f"准备评测过程发生异常: {str(e)}"
                    }

            # 等待所有评测任务完成
            if evaluation_tasks:
                logger.info(f"等待 {len(evaluation_tasks)} 个评测任务完成...")
                # 使用 as_completed 可以按完成顺序处理结果
                for completed_task in asyncio.as_completed(evaluation_tasks):
                    try:
                        updated_result = await completed_task
                        # logger.info(f"评测完成: eval_set {updated_result['eval_set_id']} - "
                        #       f"成功: {updated_result.get('evaluation', {}).get('success', False)}")
                    except Exception as e:
                        logger.info(f"评测任务异常: {str(e)}")
                logger.info("所有评测任务已完成，开始批量保存评测结果到数据库...")

                # 收集所有需要保存的结果
                results_to_save = []
                for result_item in successful_results:
                    if (result_item.get("evaluation", {}).get("success") and
                        "result_data" in result_item.get("evaluation", {}) and
                            not result_item.get("evaluation", {}).get("skipped_evaluation", False)):
                        results_to_save.append({
                            "inference_result_id": result_item["result_id"],
                            "evaluation_data": result_item["evaluation"]["result_data"]
                        })

                # 批量保存评测结果
                if results_to_save:
                    logger.info(f"开始批量保存 {len(results_to_save)} 条评测结果...")

                    # 可以使用批处理提高性能
                    batch_size = 2000
                    for i in range(0, len(results_to_save), batch_size):
                        batch = results_to_save[i:i+batch_size]
                        save_result = batch_save_evaluation_results(
                            batch)

                        if save_result["success"]:
                            # 处理成功情况
                            logger.info(f"批量保存了 {save_result['count']} 条评测结果")
                        else:
                            # 处理失败情况
                            logger.info(
                                f"批量保存评测结果失败: {save_result.get('error', '未知错误')}")
                            for result_item in batch:
                                result_item["evaluation"] = {
                                    "success": False,
                                    "error": save_result.get('error', '未知错误')
                                }

                    logger.info("所有评测结果已保存到数据库")
            return JSONResponse(
                content={
                    "success": True,
                    "message": "评测任务已创建",
                    "results": results_data
                }
            )

    except grpc.aio.AioRpcError as e:
        raise HTTPException(
            status_code=503, detail=f"gRPC连接或服务调用错误: {e.code()} - {e.details()}")
    except HTTPException as e:
        raise e
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"创建评测任务时发生内部服务器错误: {str(e)}")


async def process_evaluation(evaluation_stub, eval_request, result):
    """异步处理单个评测任务，仅执行评测 grpc 请求"""
    try:
        # 提取预处理数据
        # eval_request = prepared_data["eval_request"]

        try:
            # 调用评测服务
            evaluation_result_proto = await evaluation_stub.Evaluate(
                eval_request, timeout=60)

            # 将proto消息转换为Python字典
            evaluation_result_dict = MessageToDict(
                evaluation_result_proto,
                preserving_proto_field_name=True,
            )

            result["evaluation"] = {
                "success": True,
                "result_data": evaluation_result_dict,  # 保存完整结果数据供后续批量保存
                "data": {
                    "pathformer_accuracy_40": {
                        "ade": evaluation_result_dict.get("pathformer_accuracy_40_ade", {}).get("top_1"),
                        "fde": evaluation_result_dict.get("pathformer_accuracy_40_fde", {}).get("top_1")
                    },
                    "pathformer_accuracy_200": {
                        "ade": evaluation_result_dict.get("pathformer_accuracy_200_ade", {}).get("top_1"),
                        "fde": evaluation_result_dict.get("pathformer_accuracy_200_fde", {}).get("top_1")
                    },
                    "pathformer_accuracy4s": {
                        "ade": evaluation_result_dict.get("pathformer_accuracy4s_ade", {}).get("top_1"),
                        "fde": evaluation_result_dict.get("pathformer_accuracy4s_fde", {}).get("top_1")
                    },
                    "static_collision": evaluation_result_dict.get("static_4s_collision", {}).get("top_1", False)
                }
            }
        except grpc.RpcError as e:
            result["evaluation"] = {
                "success": False,
                "error": f"评测服务RPC错误: {e.code()} - {e.details()}"
            }

    except Exception as e:
        result["evaluation"] = {
            "success": False,
            "error": f"评测过程发生异常: {str(e)}"
        }

    return result
