from fastapi import APIRouter, Query, HTTPException
from typing import Dict, Any, List, Optional
from database.db_operations import execute_query, get_evaluation_results_by_inference_ids
import json
import numpy as np

router = APIRouter()

# ...现有代码...


# 修改 get_case_evaluation_metrics 函数，使用批量查询

@router.get("/api/case_evaluation_metrics")
async def get_case_evaluation_metrics(
    case_id: int = Query(...),
    inference_config_id: int = Query(...),
    top_k: Optional[str] = Query("1")  # 默认为top1
):
    """获取单个评测案例在特定推理配置下的评测指标"""
    try:
        try:
            k = int(top_k)
            if k not in [1, 3, 6]:
                k = 1  # 如果不是1, 3, 6则默认为1
        except ValueError:
            k = 1  # 如果转换失败，默认为1
        # 获取推理结果ID
        result_id_query = """
        SELECT ir.id 
        FROM inference_result ir
        WHERE ir.inference_config_id = %s AND ir.pkl_id = %s
        """
        params = (inference_config_id, case_id)

        result_id_result = execute_query(
            result_id_query, params, fetch_one=True)

        if not result_id_result["success"] or not result_id_result["data"]:
            return {"success": False, "error": "没有找到匹配的推理结果"}

        result_id = result_id_result["data"][0]

        # 使用批量查询函数，但只传递单个ID
        eval_result = get_evaluation_results_by_inference_ids([result_id], k)

        if not eval_result["success"] or result_id not in eval_result["data"]:
            return {"success": False, "error": "没有找到评测结果"}

        # 获取对应的数据
        data = eval_result["data"][result_id]

        # 构建评测指标
        metrics = {
            "ade_40": data.get('pathformer_accuracy_40_ade', 0),
            "ade_200": data.get('pathformer_accuracy_200_ade', 0),
            "fde_40": data.get('pathformer_accuracy_40_fde', 0),
            "fde_200": data.get('pathformer_accuracy_200_fde', 0),
            "ade_4s": data.get('pathformer_accuracy4s_ade', 0),
            "fde_4s": data.get('pathformer_accuracy4s_fde', 0),
            "static_collision": 100 if data.get('static_collision', 0) > 0 else 0,
        }
        if not metrics['ade_40']:
            metrics['ade_40'] = 0
        if not metrics['ade_200']:
            metrics['ade_200'] = 0
        if not metrics['fde_40']:
            metrics['fde_40'] = 0
        if not metrics['fde_200']:
            metrics['fde_200'] = 0
        if not metrics['ade_4s']:
            metrics['ade_4s'] = 0
        if not metrics['fde_4s']:
            metrics['fde_4s'] = 0
        if not metrics['static_collision']:
            metrics['static_collision'] = 0

        return {
            "success": True,
            "metrics": metrics
        }

    except Exception as e:
        return {"success": False, "error": f"获取案例评测指标时发生错误: {str(e)}"}


# 修改 get_evaluation_metrics 函数，使用批量查询

@router.get("/api/evaluation_metrics")
async def get_evaluation_metrics(
    evaluation_set_id: int = Query(...),
    inference_config_id: int = Query(...),
    top_k: Optional[str] = Query("1")  # 默认为top1
):
    """获取特定评测集在特定推理配置下的评测指标"""
    try:
        # 将top_k转换为整数
        try:
            k = int(top_k)
            if k not in [1, 3, 6]:
                k = 1  # 如果不是1, 3, 6则默认为1
        except ValueError:
            k = 1  # 如果转换失败，默认为1
        # 1. 获取评测集中的所有案例
        cases_query = """
        SELECT ecp.* 
        FROM evaluation_case_pool ecp
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id
        WHERE escp.evaluation_set_id = %s
        """
        cases_result = execute_query(
            cases_query, (evaluation_set_id,), fetch_all=True, return_dict=True)

        if not cases_result["success"]:
            return {"success": False, "error": "获取评测案例失败"}

        cases = cases_result["data"]

        if not cases:
            return {"success": False, "error": "该评测集中没有案例"}

        # 2. 获取这些案例在指定推理配置下的推理结果ID
        pkl_ids = [case['id'] for case in cases]

        placeholders = ', '.join(['%s'] * len(pkl_ids))
        result_ids_query = f"""
        SELECT ir.id 
        FROM inference_result ir
        WHERE ir.inference_config_id = %s 
          AND ir.pkl_id IN ({placeholders})
        """
        params = (inference_config_id, *pkl_ids)

        result_ids_result = execute_query(
            result_ids_query, params, fetch_all=True)

        if not result_ids_result["success"]:
            return {"success": False, "error": "获取推理结果ID失败"}

        inference_result_ids = [r[0] for r in result_ids_result["data"]]

        if not inference_result_ids:
            return {
                "success": False,
                "error": "没有找到匹配的推理结果，请先为这些评测案例生成推理结果"
            }

        # 3. 使用批量查询函数一次获取所有评测结果
        eval_results = get_evaluation_results_by_inference_ids(
            inference_result_ids, k)

        if not eval_results["success"]:
            return {
                "success": False,
                "error": f"获取评测结果失败: {eval_results.get('error', '未知错误')}"
            }

        if not eval_results["data"]:
            return {
                "success": False,
                "error": "没有找到评测结果，请先运行评测"
            }

        # 获取所有评测数据
        evaluation_data = list(eval_results["data"].values())

        if not evaluation_data:
            return {
                "success": False,
                "error": "没有找到评测结果，请先运行评测"
            }

        # 4. 计算平均指标
        ade_40_values = [r['pathformer_accuracy_40_ade']
                         for r in evaluation_data if r.get('pathformer_accuracy_40_ade') is not None]
        ade_200_values = [r['pathformer_accuracy_200_ade']
                          for r in evaluation_data if r.get('pathformer_accuracy_200_ade') is not None]
        fde_40_values = [r['pathformer_accuracy_40_fde']
                         for r in evaluation_data if r.get('pathformer_accuracy_40_fde') is not None]
        fde_200_values = [r['pathformer_accuracy_200_fde']
                          for r in evaluation_data if r.get('pathformer_accuracy_200_fde') is not None]
        ade_4s_values = [r['pathformer_accuracy4s_ade']
                         for r in evaluation_data if r.get('pathformer_accuracy4s_ade') is not None]
        fde_4s_values = [r['pathformer_accuracy4s_fde']
                         for r in evaluation_data if r.get('pathformer_accuracy4s_fde') is not None]

        collision_count = len(
            [r for r in evaluation_data if r.get('static_collision', 0) > 0])

        valid_collision_count = len(
            [r for r in evaluation_data if r.get('static_collision_count', 0) > 0])
        static_collision_rate = round(
            collision_count / valid_collision_count * 100, 4) if valid_collision_count > 0 else 0

        avg_metrics = {
            "ade_40": np.mean(ade_40_values) if ade_40_values else 0,
            "ade_200": np.mean(ade_200_values) if ade_200_values else 0,
            "fde_40": np.mean(fde_40_values) if fde_40_values else 0,
            "fde_200": np.mean(fde_200_values) if fde_200_values else 0,
            "ade_4s": np.mean(ade_4s_values) if ade_4s_values else 0,
            "fde_4s": np.mean(fde_4s_values) if fde_4s_values else 0,
            "static_collision": static_collision_rate,
            "case_count": len(evaluation_data)
        }

        return {
            "success": True,
            "metrics": avg_metrics
        }

    except Exception as e:
        return {"success": False, "error": f"获取评测指标时发生错误: {str(e)}"}
