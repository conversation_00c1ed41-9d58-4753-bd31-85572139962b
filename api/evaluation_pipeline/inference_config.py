# api/inference_config.py

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
import os
import hashlib
from typing import Dict, Any, List, Optional
import pickle
import json
import tempfile
import shutil
from datetime import datetime
from typing import List
from database.db_operations import execute_query
import grpc
from proto import inference_server_pb2
from proto import inference_server_pb2_grpc
from proto import inference_result_pb2
router = APIRouter()

# 文件存储目录
JSON_DIR = "/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/database/json"
PTH_DIR = "/mnt/users/ruoxu.yang/code-space/prod/evaluation_platform/database/pth"

# 确保目录存在
os.makedirs(JSON_DIR, exist_ok=True)
os.makedirs(PTH_DIR, exist_ok=True)


@router.post("/api/inference_config")
async def create_inference_config(
    json_file_path: str = Form(...),
    pth_file_path: str = Form(...)
):
    """创建新的推理配置，使用服务器上已存在的文件路径"""
    # 验证文件类型和存在性
    if not json_file_path.endswith('.json'):
        raise HTTPException(status_code=400, detail="JSON文件格式不正确")

    if not pth_file_path.endswith('.pth'):
        raise HTTPException(status_code=400, detail="PTH文件格式不正确")
    # print(json_file_path, pth_file_path)
    # 验证文件是否存在
    if not os.path.exists(json_file_path):
        raise HTTPException(
            status_code=404, detail=f"JSON文件不存在: {json_file_path}")

    if not os.path.exists(pth_file_path):
        raise HTTPException(
            status_code=404, detail=f"PTH文件不存在: {pth_file_path}")

    try:
        # 处理JSON文件
        with open(json_file_path, "rb") as f:
            json_content = f.read()

        json_md5 = hashlib.md5(json_content).hexdigest()
        json_dest_path = os.path.join(JSON_DIR, f"{json_md5}.json")

        # 验证JSON内容
        try:
            json_data = json.loads(json_content)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="JSON格式无效")

        # 拷贝JSON文件到目标位置
        shutil.copy2(json_file_path, json_dest_path)

        # 处理PTH文件
        with open(pth_file_path, "rb") as f:
            pth_md5 = hashlib.md5(f.read()).hexdigest()

        pth_dest_path = os.path.join(PTH_DIR, f"{pth_md5}.pth")

        # 拷贝PTH文件到目标位置
        shutil.copy2(pth_file_path, pth_dest_path)

        # 插入数据库
        current_time = datetime.now().isoformat()
        json_filename = os.path.basename(json_file_path)
        pth_filename = os.path.basename(pth_file_path)

        query = """
        INSERT INTO inference_config 
        (json_name, json_content, json_md5, pth_name, pth_dir, pth_md5, pth_upload_time)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            json_filename,
            json_content.decode('utf-8'),
            json_md5,
            pth_filename,
            pth_dest_path,
            pth_md5,
            current_time
        )

        result = execute_query(query, params, get_last_id=True)
        if not result['success']:
            # 清理文件
            if os.path.exists(json_dest_path):
                os.remove(json_dest_path)
            if os.path.exists(pth_dest_path):
                os.remove(pth_dest_path)
            raise HTTPException(status_code=500, detail=result['error'])

        return {
            "success": True,
            "id": result['last_id'],
            "message": "推理配置已创建"
        }
    except Exception as e:
        # 出现异常时清理
        raise HTTPException(status_code=500, detail=f"上传文件失败: {str(e)}")


@router.get("/api/inference_config")
async def get_inference_configs():
    """获取所有推理配置"""
    query = """
    SELECT id, json_name, pth_name, DATE_FORMAT(pth_upload_time, '%Y-%m-%d %H:%i:%s') as pth_upload_time
    FROM inference_config
    ORDER BY pth_upload_time DESC
    """
    result = execute_query(query, fetch_all=True)

    if not result['success']:
        raise HTTPException(status_code=500, detail=result['error'])

    return result['data']


@router.get("/api/inference_config/{config_id}")
async def get_inference_config_by_id(config_id: int):
    """获取指定ID的推理配置详情"""
    try:
        query = """
        SELECT id, json_name, pth_name
        FROM inference_config
        WHERE id = %s
        """
        result = execute_query(query, (config_id,), fetch_one=True)

        if not result['success']:
            raise HTTPException(status_code=500, detail=result['error'])

        if not result['data']:
            return {
                "success": False,
                "error": f"未找到ID为{config_id}的推理配置"
            }

        return {
            "success": True,
            "id": result['data'][0],
            "json_name": result['data'][1],
            "pth_name": result['data'][2],
            # "pth_upload_time": result['data'][3]
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@router.get("/api/inference-configs")
async def get_inference_configs(pkl_name: str):
    """获取指定 pickle 文件可用的推理配置列表"""
    try:

        # 查询有结果的推理配置
        query = """
        SELECT DISTINCT c.id, c.json_name, c.pth_name, c.pth_upload_time  
        FROM inference_config c
        JOIN inference_result r ON c.id = r.inference_config_id
        WHERE r.pkl_name = %s
        ORDER BY c.pth_upload_time DESC
        """
        configs = execute_query(query, (pkl_name,), fetch_all=True)
        json_configs = []
        for config in configs['data']:
            json_configs.append({
                "id": config[0],
                "json_name": config[1],
                "pth_name": config[2],
                "pth_upload_time": config[3]
            })
        return {
            "success": True,
            "configs": json_configs
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@router.get("/api/inference-result")
async def get_inference_result(config_id: int, pkl_id: int):
    """获取指定配置和文件的推理结果"""
    try:

        # 查询推理结果
        query = """
        SELECT result_binary 
        FROM inference_result 
        WHERE inference_config_id = %s AND pkl_id = %s
        LIMIT 1
        """
        result = execute_query(query, (config_id, pkl_id), fetch_one=True)
        if not result:
            return {
                "success": False,
                "error": "未找到指定配置的推理结果"
            }
        # result['data'] 是 inference_result_pb2
        # 反序列化二进制数据
        inference_data = inference_result_pb2.InferenceOutput()
        inference_data.ParseFromString(result['data'][0])

        # 从推理结果中提取 AnchorFreePathformerOutput 数据并转换为前端需要的轨迹格式
        trajectories = extract_trajectories_from_inference(inference_data)

        return {
            "success": True,
            "trajectories": trajectories
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def extract_trajectories_from_inference(inference_data: Any) -> List[Dict[str, Any]]:
    """从推理结果中提取轨迹数据"""
    trajectories = []

    try:
        # 解析 AnchorFreePathformerOutput
        if hasattr(inference_data, 'anchor_free_pathformer') and \
           hasattr(inference_data.anchor_free_pathformer, 'paths'):

            # 遍历所有轨迹
            for i, path in enumerate(inference_data.anchor_free_pathformer.paths):
                traj_points = []

                # 对于每个轨迹，提取所有点
                for point in path.trajectory.points:
                    traj_points.append([point.x, point.y, 0.05])  # z轴稍微抬高

                # 计算颜色 - 根据概率变化从绿到红
                prob = path.probability
                hue = 120  # 120是绿色，0是红色
                color = f"hsl({hue}, 100%, 50%)"

                # 添加到轨迹列表
                trajectories.append({
                    "id": f"inference_path_{i}",
                    "points": traj_points,
                    "probability": float(prob),
                    "color": color,
                    "thickness": 2,
                    "type": "solid"
                })
            # 概率最大的轨迹变成黄色
            if len(trajectories) > 0:
                max_prob_traj = max(
                    trajectories, key=lambda x: x['probability'])
                max_prob_traj['color'] = "hsl(60, 100%, 50%)"
                max_prob_traj['thickness'] = 4
    except Exception as e:
        print(f"提取轨迹数据出错: {str(e)}")

    return trajectories
