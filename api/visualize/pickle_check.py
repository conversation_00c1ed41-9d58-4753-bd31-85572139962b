import numpy as np
from shapely.geometry import Polygon, Point
from api.visualize.pickle_visualize import load_pickle


def find_close_obstacles(pkl_file, path_expand=1.0):
    data = load_pickle(pkl_file)

    mask_obj = data["mask_obj"]
    ego_size = data["feature_obj"][0, 0, 8:10]
    ego_width = ego_size[0]

    path = data["pdp_path"][0]["raw_points"]
    path = np.array(path)
    N, P = path.shape

    # 计算轨迹点的切线向量和法线向量
    tangents = np.zeros((N, P))
    for i in range(1, (N - 1)):
        tangents[i] = (path[i + 1] - path[i - 1]) / 2.0
    tangents[0] = path[1] - path[0]
    tangents[(N - 1)] = path[(N - 1)] - path[(N - 2)]

    if not tangents.any():
        return []
    # 归一化切线向量
    norm_tangents = tangents / np.linalg.norm(tangents, axis=1, keepdims=True)
    # 计算法线向量（顺时针旋转90度）
    normals = np.zeros_like(norm_tangents)
    normals[:, 0] = -norm_tangents[:, 1]
    normals[:, 1] = norm_tangents[:, 0]
    try:
        # 计算左右偏移点，形成轨迹带
        left_path = path + ((ego_width + path_expand) / 2) * normals
        right_path = path - ((ego_width + path_expand) / 2) * normals
        # 构建自车轨迹带的多边形（从左路径到右路径反转）
        polygon_points = np.vstack((left_path, right_path[::-1]))
        ego_polygon = Polygon(polygon_points)
    except Exception as e:
        print(len(left_path))
        print(len(right_path))
        print(pkl_file)
        print(f"创建多边形时出错: {e}")
        return []

    # 初始化相近障碍物列表
    close_obstacles = []

    # 遍历障碍物（索引从1到99，0是自车）
    for i in range(1, 100):
        if not np.any(mask_obj[i] == 1):
            break
        elif mask_obj[i, 5] == 1:
            center = data["feature_obj"][i, 5, 2:4]  # 终点坐标
            size_obs = data["feature_obj"][i, 5, 8:10]
            heading_obs = data["feature_obj"][i, 5, 10]  # 航向角
        elif mask_obj[i, 0] == 1:
            center = data["feature_obj"][i, 0, 2:4]  # 终点坐标
            size_obs = data["feature_obj"][i, 0, 8:10]
            heading_obs = data["feature_obj"][i, 0, 10]  # 航向角
        else:
            first_half = mask_obj[i, :5]
            index_1 = np.where(first_half == 1)[0]
            if len(index_1) > 0:
                index_1 = index_1.min()
            else:
                index_1 = 10

            second_half = mask_obj[i, 5:]
            index_2 = np.where(second_half == 1)[0]
            if len(index_2) > 0:
                index_2 = index_2.min()
            else:
                index_2 = 10

            if index_1 * 0.5 > index_2 * 0.2:
                index = index_2 + 5
            else:
                index = index_1

            center = data["feature_obj"][i, index, 2:4]  # 终点坐标
            size_obs = data["feature_obj"][i, index, 8:10]
            heading_obs = data["feature_obj"][i, index, 10]

        W = size_obs[0]
        L = size_obs[1]
        u = np.array([np.cos(heading_obs), np.sin(heading_obs)])  # 方向向量
        v = np.array([-np.sin(heading_obs), np.cos(heading_obs)])  # 法线向量
        # 计算四个角点
        corner1 = center + (L / 2) * u + (W / 2) * v
        corner2 = center + (L / 2) * u - (W / 2) * v
        corner3 = center - (L / 2) * u - (W / 2) * v
        corner4 = center - (L / 2) * u + (W / 2) * v
        obs_polygon = Polygon([corner1, corner2, corner3, corner4])

        # 计算IOU
        intersection_area = ego_polygon.intersection(obs_polygon).area

        union_area = ego_polygon.area + obs_polygon.area - intersection_area
        iou = intersection_area / union_area if union_area > 0 else 0
        if iou > 0:
            close_obstacles.append(i)

    return close_obstacles
