import numpy as np

import matplotlib.pyplot as plt
def get_onehot_index(x) -> int:
    for i, v in enumerate(x):
        if v > 0.5:return i

    raise ValueError(f'x={x}')
    return -1
def draw_lane_turn_sign(
    sp,
    lane_turn_sign: np.ndarray,#(time_length, max_lane_turn_nums, lane_turn_sign_dim)
    lane_turn_sign_mask: np.ndarray,#(time_length, max_lane_turn_nums)
):
    # enum LaneTurnType:uint8_t {
    #     LaneTurnType_Unknown      = 0,
    #     LaneTurnType_NTurn        = 1,    // 直行
    #     LaneTurnType_LTurn        = 2,    // 左转
    #     LaneTurnType_RTurn        = 3,    // 右转
    #     LaneTurnType_UTurn        = 4,    // 掉头
    #     LaneTurnType_LNTurn       = 5,    // 左转-直行
    #     LaneTurnType_RNTurn       = 6,    // 右转-直行
    #     LaneTurnType_LUTurn       = 7,    // 左转-掉头
    #     LaneTurnType_RUTurn       = 8,    // 右转-掉头
    #     LaneTurnType_LUNTurn      = 9,    // 左转-掉头-直行
    #     LaneTurnType_RUNTurn      = 10,   // 右转-掉头-直行
    #     LaneTurnType_LRUNTurn     = 11,   // 左转-右转-掉头-直行
    #     LaneTurnType_LRTurn       = 12,   // 左转-右转
    #     LaneTurnType_LRNTurn      = 13,   // 左转-右转-直行
    #     LaneTurnType_VariableTurn = 14,   // 可变车道
    #     LaneTurnType_NUTurn       = 15,   // 直行-掉头
    #     LaneTurnType_LRUTurn      = 16,   // 左转-右转-掉头
    # };
    LaneTurnType_Unknown      = 0#,
    LaneTurnType_NTurn        = 1#,    // 直行
    LaneTurnType_LTurn        = 2#,    // 左转
    LaneTurnType_RTurn        = 3#,    // 右转
    LaneTurnType_UTurn        = 4#,    // 掉头
    LaneTurnType_LNTurn       = 5#,    // 左转-直行
    LaneTurnType_RNTurn       = 6#,    // 右转-直行
    LaneTurnType_LUTurn       = 7#,    // 左转-掉头
    LaneTurnType_RUTurn       = 8#,    // 右转-掉头
    LaneTurnType_LUNTurn      = 9#,    // 左转-掉头-直行
    LaneTurnType_RUNTurn      = 10#,   // 右转-掉头-直行
    LaneTurnType_LRUNTurn     = 11#,   // 左转-右转-掉头-直行
    LaneTurnType_LRTurn       = 12#,   // 左转-右转
    LaneTurnType_LRNTurn      = 13#,   // 左转-右转-直行
    LaneTurnType_VariableTurn = 14#,   // 可变车道
    LaneTurnType_NUTurn       = 15#,   // 直行-掉头
    LaneTurnType_LRUTurn      = 16#,   // 左转-右转-掉头

    straight_set = set(
        [
            LaneTurnType_NTurn, 
            LaneTurnType_LNTurn, 
            LaneTurnType_RNTurn, 
            LaneTurnType_LUNTurn, 
            LaneTurnType_RUNTurn,
            LaneTurnType_LRUNTurn,
            LaneTurnType_LRNTurn,
            LaneTurnType_NUTurn,
        ]
    )
    turn_left_set = set(
        [
            LaneTurnType_LTurn,
            LaneTurnType_LNTurn,
            LaneTurnType_LUTurn,
            LaneTurnType_LUNTurn,
            LaneTurnType_LRUNTurn,
            LaneTurnType_LRTurn,
            LaneTurnType_LRNTurn,
            LaneTurnType_LRUTurn,
        ]
    )
    turn_right_set = set(
        [
            LaneTurnType_RTurn,
            LaneTurnType_RNTurn,
            LaneTurnType_RUTurn,
            LaneTurnType_RUNTurn,
            LaneTurnType_LRUNTurn,
            LaneTurnType_LRTurn,
            LaneTurnType_LRNTurn,
            LaneTurnType_LRUTurn,
        ]
    )
    uturn_set = set(
        [
            LaneTurnType_UTurn,
            LaneTurnType_LUTurn,
            LaneTurnType_RUTurn,
            LaneTurnType_LUNTurn,
            LaneTurnType_RUNTurn,
            LaneTurnType_LRUNTurn,
            LaneTurnType_NUTurn,
            LaneTurnType_LRUTurn,
        ]
    )

    # 画导航信息

    line_length = 1.8
    # arrow_length = 1.0
    arrow_length = 0.5
    uturn_dx = arrow_length + 0.15
    uturn_dy = 1.8

    def draw_left(
        base_x, base_y,           
    ):
        sp.plot([base_x, base_x], [base_y, base_y + line_length], color='black', linewidth=5)   
        sp.arrow(base_x, base_y + line_length,
            dx=-arrow_length, dy=0, 
            head_width=0.2, head_length=0.3, linewidth=5, fc='black', ec='black')
        
    def draw_right(
        base_x, base_y,            
    ):
        sp.plot([base_x, base_x], [base_y, base_y + line_length], color='black', linewidth=5)   
        sp.arrow(base_x, base_y + line_length,
            dx=arrow_length, dy=0, 
            head_width=0.2, head_length=0.3, linewidth=5, fc='black', ec='black')

    def draw_straight(
        base_x, base_y,
    ):
        sp.arrow(base_x, base_y,
            dx=0, dy=line_length,  
            head_width=0.2, head_length=0.3, linewidth=5, fc='black', ec='black')

    def draw_uturn(
        base_x, base_y,
    ):
        uturn_point_x = [base_x, base_x, base_x - uturn_dx]
        uturn_point_y = [base_y, base_y + uturn_dy, base_y + uturn_dy]
        sp.plot(uturn_point_x, uturn_point_y, color='black', linewidth=5)
        sp.arrow(uturn_point_x[-1], uturn_point_y[-1],
            dx=0, dy=-uturn_dy*0.5,  
            head_width=0.2, head_length=0.3, linewidth=5, fc='black', ec='black')

    def draw_unknown(
        base_x, base_y,
    ):
        sp.plot([base_x, base_x], [base_y, base_y + line_length], color='r', linewidth=5)   
    def draw_variable(
        base_x, base_y,
    ):
        sp.plot([base_x, base_x], [base_y, base_y + line_length], color='cyan', linewidth=5)   

    def draw_lane_turn_sign(
        type_int: int,
        base_x: float,
        base_y: float,
    ):
        if type_int in straight_set:
            draw_straight(base_x=base_x, base_y=base_y)
        if type_int in turn_left_set:
            draw_left(base_x=base_x, base_y=base_y)
        if type_int in turn_right_set:
            draw_right(base_x=base_x, base_y=base_y)
        if type_int in uturn_set:
            draw_uturn(base_x=base_x, base_y=base_y)

        if type_int == LaneTurnType_Unknown:
            draw_unknown(base_x=base_x, base_y=base_y)
        if type_int == LaneTurnType_VariableTurn:
            draw_variable(base_x=base_x, base_y=base_y)
        



    time_length, max_lane_turn_nums, lane_turn_sign_dim = lane_turn_sign.shape

    current_base_x = 0.0
    current_base_y = 0.0
    base_offset_x = 2.0
    base_offset_y = 2.5
    for time_index in range(time_length):
        for lane_index in range(max_lane_turn_nums):
            if lane_turn_sign_mask[time_index, lane_index] < 0.5:continue
            type_int = get_onehot_index(lane_turn_sign[time_index, lane_index])
            draw_lane_turn_sign(
                type_int=type_int,
                base_x=current_base_x + lane_index*base_offset_x,
                base_y=current_base_y + time_index*base_offset_y,
            )

    sp.set_xlim(-base_offset_x*0.7, base_offset_x*max_lane_turn_nums)
    sp.set_ylim(-base_offset_y*0.7, base_offset_y*time_length)
