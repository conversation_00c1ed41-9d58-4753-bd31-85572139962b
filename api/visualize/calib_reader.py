import yaml
import os
import numpy as np

class CalibReader:
    def __init__(self, vin='LFZ63AX50SD000527',bagdir=None,calib_name='driving_offline_calibration_param.yaml'):
        #self.csv_list= csv_list
        #self.calib_path =  os.path.join(os.path.dirname(bagdir),"driving_offline_calibration_param.yaml")
        self.vin = vin
        self.calib_path=os.path.join(r'/extraStore/groupdatahw01/Predictionalgorithm/37929/data/calib',self.vin,"driving_offline_calibration_param.yaml")
    def get_calib(self, scale, offset_x, offset_y):
        if not os.path.exists(self.calib_path):
            try:
                #vin= self.calib_path.split('/')[5].split('_record')[0]
                self.calib_path=os.path.join(r'/extraStore/groupdatahw01/Predictionalgorithm/37929/data/calib',self.vin,"driving_offline_calibration_param.yaml")
                if not os.path.exists(self.calib_path):
                    return None
            except:
                return None
            #return None
        with open(self.calib_path) as f:
            calib = yaml.load(f, Loader=yaml.FullLoader)
        calib_dict = dict()
        for key in calib.keys():
            if key not in ["front_wide"]:
                continue
            calib_dict[key] = dict()
            rot=np.array(calib[key]['rotation'],dtype=np.float32).reshape([3,3])
            x=calib[key]['translation']['x']
            y=calib[key]['translation']['y']
            z=calib[key]['translation']['z'] 
            rt=np.zeros((4,4),dtype=np.float32)
            rt[:3,:3]=rot
            rt[0,-1]=x
            rt[1,-1]=y
            rt[2,-1]=z
            rt[-1,-1]=1.0
            e=np.linalg.inv(rt).tolist()
            calib[key]['ego2cam']=e
            calib_dict[key]['ego2cam']=e
            calib_dict[key]['ego2img'], calib_dict[key]['K_intrin']= self.get_ego2img(calib[key],scale, offset_x=offset_x, offset_y=offset_y)
            calib_dict[key]["dist_coeff"] = [
                calib[key]['intrinsic']["k1"],
                calib[key]['intrinsic']["k2"],
                calib[key]['intrinsic']["p1"],
                calib[key]['intrinsic']["p2"],
                calib[key]['intrinsic']["k3"],
                calib[key]['intrinsic']["k4"],
                calib[key]['intrinsic']["k5"],
                calib[key]['intrinsic']["k6"],
            ]
        return calib_dict
    
    def get_ego2img(self, calib,scale, offset_x=0, offset_y=0):
        intr=calib['intrinsic']
        intinsics=np.array([intr['fx'],0,intr['cx']-offset_x,0,intr['fy'],intr['cy']-offset_y,0,0,1],dtype=np.float32).reshape([3,3])
        scale_factor = np.eye(3, dtype=np.float32)
        scale_factor[0, 0] *= scale[0]
        scale_factor[1, 1] *= scale[1]
        cam2img = np.dot(scale_factor, intinsics)
        cam2img_pad = np.eye(4, dtype=np.float32)
        cam2img_pad[:3, :3] = cam2img
        lidar2cam = calib['ego2cam']
        lidar2img = np.dot(cam2img_pad, lidar2cam)
        return lidar2img, cam2img_pad


class CalibReaderDict:
    def __init__(self, ):
        #self.csv_list= csv_list
        #self.calib_path =  os.path.join(os.path.dirname(bagdir),"driving_offline_calibration_param.yaml")
        root=r'/extraStore/groupdatahw01/Predictionalgorithm/37929/data/calib'
        vins=os.listdir(root)
        self.calib_reader_dict = {}
        for vin in vins:
            if vin not in self.calib_reader_dict:
                self.calib_reader_dict[vin] = CalibReader(vin=vin)
    def get_calib(self, vin, scale, offset_x, offset_y):
        if vin not in self.calib_reader_dict:
            calib_reader = self.calib_reader_dict['LFZ63AX50SD000527']
            print('...vikn not found, use default', vin)
            return calib_reader.get_calib(scale, offset_x, offset_y)
        
        calib_reader = self.calib_reader_dict[vin]
        return calib_reader.get_calib(scale, offset_x, offset_y)