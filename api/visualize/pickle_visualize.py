import gzip
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Body
from fastapi.responses import JSONResponse
import os
import pickle
import json
import tempfile
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import asyncio
import concurrent.futures
import time
import numpy as np

router = APIRouter()

maneuver_dict = {
    0: "Null",
    1: "Car",
    2: "TurnLeft",
    3: "TurnRight",
    4: "SlightLeft",
    5: "SlightRight",
    6: "TurnHardLeft",
    7: "TurnHardRight",
    8: "UTurn",
    9: "Continue",
    10: "Way",
    11: "EntryRing",
    12: "LeaveRing",
    13: "SAPA",
    14: "TollGate",
    15: "Destination",
    16: "Tunnel",
    17: "EntryLeftRing",
    18: "LeaveLeftRing",
    19: "UTurnRight",
    20: "SpecialContinue",
    21: "EntryRingLeft",
    22: "EntryRingRight",
    23: "EntryRingContinue",
    24: "EntryRingUTurn",
    25: "EntryLeftRingLeft",
    26: "EntryLeftRingRight",
    27: "EntryLeftRingContinue",
    28: "EntryLeftRingUTurn",
    29: "CrossWalk",
    30: "OverPass",
    31: "Underground",
    32: "Square",
    33: "Park",
    34: "Staircase",
    35: "Lift",
    36: "Cableway",
    37: "SkyChannel",
    38: "Channel",
    39: "WalkRoad",
    40: "BoatLine",
    41: "SightseeingLine",
    42: "Skidway",
    43: "Ladder",
    44: "Slope",
    45: "Bridge",
    46: "Ferry",
    47: "Subway",
    48: "EnterBuilding",
    49: "LeaveBuilding",
    50: "ByElevator",
    51: "ByStair",
    52: "ByEscalator",
    53: "LowTrafficCross",
    54: "LowCross",
    55: "HousingEstateInner",
    56: "Cnt",
    64: "WayChargeStation",
    65: "MergeLeft",
    66: "MergeRight",
}
lane_action_dict = {
    0: "Null",
    1: "Ahead",
    2: "Left",
    3: "AheadLeft",
    4: "Right",
    5: "AheadRight",
    6: "LeftUTurn",
    7: "LeftRight",
    8: "AheadLeftRight",
    9: "RightUTurn",
    10: "AheadLeftUTurn",
    11: "AheadRightUTurn",
    12: "LeftLeftUTurn",
    13: "RightRightUTurn",
    14: "LeftInAheadReserve",
    15: "LeftLeftUTurnReserve",
    16: "ReservedReserve",
    17: "AheadLeftLeftUTurn",
    18: "RightLeftUTurn",
    19: "LeftRightLeftUTurn",
    20: "AheadRightLeftUTurn",
    21: "LeftRightUTurn",
    22: "Bus",
    23: "Empty",
    24: "Variable",
    25: "Dedicated",
    26: "Tidal",
}


def is_gzip_file(file_path):
    with open(file_path, "rb") as f:
        magic = f.read(2)
    return magic == b"\x1f\x8b"


def load_pickle(file_path):
    if is_gzip_file(file_path):
        with gzip.open(file_path, "rb") as f:
            return pickle.load(f)
    else:
        with open(file_path, "rb") as f:
            return pickle.load(f)


class PickleVisualizationRequest(BaseModel):
    pickle_path: str
    config: Optional[Dict[str, Any]] = None


async def load_pickle_async(file_path):
    """异步加载pickle文件，避免阻塞事件循环"""

    def _load_pickle_sync(file_path):
        # print(f"开始加载文件: {file_path}")
        # start_time = time.time()

        try:
            if is_gzip_file(file_path):
                with gzip.open(file_path, "rb") as f:
                    data = pickle.load(f)
            else:
                with open(file_path, "rb") as f:
                    data = pickle.load(f)

            # load_time = time.time() - start_time
            # print(f"文件加载完成，耗时: {load_time:.2f}秒")
            return data

        except Exception as e:
            print(f"文件加载失败: {str(e)}")
            raise

    # 在线程池中执行同步I/O操作，避免阻塞主线程
    loop = asyncio.get_event_loop()
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        return await loop.run_in_executor(executor, _load_pickle_sync, file_path)


async def extract_geometry_from_pickle_async(
    pickle_path: str, config: Optional[Dict[str, Any]] = None
):
    """异步版本的几何数据提取"""
    # 异步加载pickle文件
    data = await load_pickle_async(pickle_path)
    if isinstance(data, list):
        # data[0][0]: pkl_path
        # data[0][1]: original_dict
        data = data[0][1]
    result = {
        "polylines": [],
        "polygons": [],
        "arrows": [],
        "objects": [],
        "texts": [],
        "future_obj_infos": [],
        "recommend_lanes": [],
        "future_obj_pred": [],
        "metadata": {
            "filename": os.path.basename(pickle_path),
            "timestamp": datetime.now().isoformat(),
        },
        # "tbt": {}
        "centerlines_for_drivable": [],
        
    }
    if "centerlines_for_drivable" in data:
        line_dict=data["centerlines_for_drivable"]
        points_list=line_dict["points_list"]
        points_id_list=line_dict["points_id_list"]
        points_recommend_label=line_dict["points_recommend_label"]
        for points,id,recommend_label in zip(points_list,points_id_list,points_recommend_label):
            result["centerlines_for_drivable"].append(
                {
                    "id": f"{id}",
                    "points": points.tolist(),
                    "points_recommend_label": int(recommend_label),
                }
            )
    has_pred = False
    if "prediction_traj_winner" in data:
        has_pred = True
        prediction_traj_winner = data["prediction_traj_winner"]  # 99,12,2
        # if isinstance(data["prediction_traj_winner"] ,torch.Tensor):
        #     prediction_traj_winner = prediction_traj_winner.cpu().numpy()
        # for idx, pred_traj in enumerate(prediction_traj_winner):
        #     pred_traj=np.concatenate([pred_traj,np.full((pred_traj.shape[0],1),2)],axis=1)
        #     timestamps = np.arange(0, pred_traj.shape[0]) * 0.5
        #     result["future_obj_pred"].append(
        #         {
        #             "id": f"{idx+1}",
        #             "points": pred_traj.tolist(),
        #             "timestamps": timestamps.tolist(),
        #         }
        #     )
    # TBT数据处理
    if data.get("tbt"):
        tbt = data["tbt"]
        dist = tbt["dist"]
        maneuver_text = maneuver_dict[tbt["maneuver_id"]]
        lane_action_text = "|"
        for lane_action in tbt["front_lane"]:
            lane_action_text += lane_action_dict[lane_action[1]] + "|"
        result["tbt"] = {
            "dist": dist,
            "maneuver": maneuver_text,
            "lane_action": lane_action_text,
        }
    if "ego_traffic_light" in data:
        # [1,0,0,0] unknown, [0,1,0,0] red, [0,0,1,0] green, [0,0,0,1] yellow
        ego_traffic_light = data["ego_traffic_light"][2:]
        max_index = np.argmax(ego_traffic_light[:4])
        sign_map = {0: "Unknown", 1: "Red", 2: "Green", 3: "Yellow"}
        sign = sign_map.get(max_index, "Unknown")
        # 获取16进制颜色 0->black 1->red 2->green 3->yellow
        color_map = {0: "#000000", 1: "#FF0000", 2: "#00FF00", 3: "#FFFF00"}
        color = color_map.get(max_index, "Black")

        result["traffic_lights"] = {"color": color, "sign": sign}

    # 获取数据数组
    ployline_lane_type = data.get("polyline_lane_type", None)
    polyline_arrow = data["ployline_arrow"]
    polyline_arrow_mask = data["ployline_arrow_mask"]
    polygon_subgraph = data["ploygon_subgraph"]
    polygon_subgraph_mask = data["ploygon_subgraph_mask"]
    feature_obj = data["feature_obj"]
    feature_obj_mask = data["mask_obj"]
    ego_path_point = data["path_point"]
    ego_path_mask = data["path_mask"]
    navigation_path = data["navigation_path"]
    navigation_path_mask = data["navigation_mask"]

    if "Seach" in data:
        future_obj = data["Seach"]
        future_obj_mask = data["Sweach"]
        # future_obj = np.array(future_obj)
        # future_obj_mask = np.array(future_obj_mask)
        valid = np.any(future_obj_mask, axis=1)
        valid_obj_indices = np.where(valid)[0]
        # 提取有效轨迹和对应的掩码
        valid_future_obj = future_obj[valid_obj_indices]  # 形状 (有效轨迹数, 12, 2)
        valid_future_obj_mask = future_obj_mask[
            valid_obj_indices
        ]  # 形状 (有效轨迹数, 12)
        padding_points = feature_obj[
            valid_obj_indices, 5, 2:4
        ]  # (有效轨迹数，2) 使用当前时刻位置padding初始时刻
        for idx, (traj, padding_point, mask) in enumerate(
            zip(valid_future_obj, padding_points, valid_future_obj_mask)
        ):
            valid_points = traj[mask.astype(bool)]
            valid_indices = np.where(mask)[0]
            filter_t4 = np.where((valid_indices + 1) * 0.5 <= 4)[0]
            valid_points = valid_points[filter_t4]
            # add z value 2 to valid_points
            valid_points = np.concatenate(
                [valid_points, np.full((valid_points.shape[0], 1), 2)], axis=1
            )
            timestamps = (valid_indices[filter_t4] + 1) * 0.5
            # caculate heading
            heading = np.arctan2(
                valid_points[1:, 1] - valid_points[:-1, 1],
                valid_points[1:, 0] - valid_points[:-1, 0],
            )
            heading = np.concatenate(
                [
                    [
                        np.arctan2(
                            valid_points[0, 1] - padding_point[1],
                            valid_points[0, 0] - padding_point[0],
                        )
                    ],
                    heading,
                ],
                axis=0,
            ).tolist()
            result["future_obj_infos"].append(
                {
                    "id": f"{valid_obj_indices[idx]}",
                    "points": valid_points.tolist(),
                    "timestamps": timestamps.tolist(),
                    "rotation": heading,
                    # "color": "#5D8BF4",
                }
            )
    # 处理navigation路径
    navigation_point = []
    navi_color = "#FFB6C1"
    for point, mask in zip(navigation_path, navigation_path_mask):
        if mask:
            x, y = point[:2]
            navigation_point.append([x, y, 2])

    if len(navigation_point) > 1:
        result["polylines"].append(
            {
                "id": "navigation_point",
                "points": navigation_point,
                "color": navi_color,
                "thickness": 0.05,
                "type": "solid",
            }
        )
    if "recommend_lane_xy" in data:
        # recommend_lane_xy (10,25,2) 10 lines 25 points 2 dimensions
        # recommend_lane_xy_mask (10,25) valid mask
        # extract all valid line points and convert to format like arrows and  add to results
        recommend_lane_xy = data["recommend_lane_xy"]
        recommend_lane_xy_mask = data["recommend_lane_xy_mask"]
        for i, (lane_data, lane_mask) in enumerate(
            zip(recommend_lane_xy, recommend_lane_xy_mask)
        ):
            # if valid points <2 continue
            if np.sum(lane_mask) < 2:
                continue
            valid_lane_points = lane_data[lane_mask.astype(bool)]
            # two points construct one arrow line
            for j in range(len(valid_lane_points) // 2):
                begin_x = valid_lane_points[j * 2][0]
                begin_y = valid_lane_points[j * 2][1]
                end_x = valid_lane_points[j * 2 + 1][0]
                end_y = valid_lane_points[j * 2 + 1][1]
                result["recommend_lanes"].append(
                    {
                        "id": f"recommend_lane_{i}_{j}",
                        "start": [begin_x, begin_y, 0],
                        "end": [end_x, end_y, 0],
                        "color": "#01E8F8",
                        "headSize": 0.3,
                    }
                )
    # 处理polyline数据
    for i, (line, mask) in enumerate(zip(polyline_arrow, polyline_arrow_mask)):
        if not mask:
            continue

        begin_x, begin_y, end_x, end_y = line[:4]
        map_type_onehot = line[4:10]

        line_type = "solid"
        line_color = "#CCCCCC"

        if map_type_onehot[0] > 0.5:  # unknown
            line_color = "#555555"
        elif map_type_onehot[1] > 0.5:  # solid
            line_color = "#E6E6E6"
        elif map_type_onehot[2] > 0.5:  # dashed
            line_type = "dashed"
            line_color = "#E6E6E6"
        elif map_type_onehot[3] > 0.5:  # solid_dashed
            line_type = "solid_dashed"
            line_color = "#E6E6E6"
        elif map_type_onehot[4] > 0.5:  # dashed_solid
            line_type = "dashed_solid"
            line_color = "#E6E6E6"
        elif map_type_onehot[5] > 0.5:  # sideline
            line_color = "#8F5300"
        elif ployline_lane_type is not None:
            if ployline_lane_type[1] > 0.5 or ployline_lane_type[4] > 0.5:
                line_color = "#FFD700"  # 金色

        # 处理复合线型
        if line_type == "solid_dashed":
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_dashed",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "dashed",
                }
            )
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_solid",
                    "points": [
                        [begin_x + 0.1, begin_y + 0.1, 0],
                        [end_x + 0.1, end_y + 0.1, 0],
                    ],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "solid",
                }
            )
        elif line_type == "dashed_solid":
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_solid",
                    "points": [
                        [begin_x + 0.1, begin_y + 0.1, 0],
                        [end_x + 0.1, end_y + 0.1, 0],
                    ],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "solid",
                }
            )
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_dashed",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "dashed",
                }
            )
        else:
            result["polylines"].append(
                {
                    "id": f"polyline_{i}",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": line_type,
                }
            )

        result["arrows"].append(
            {
                "id": f"arrow_{i}",
                "start": [begin_x, begin_y, 0],
                "end": [end_x, end_y, 0],
                "color": line_color,
                "headSize": 0.1,
            }
        )

    # 处理polygon数据
    for i, (polygon_data, mask_row) in enumerate(
        zip(polygon_subgraph, polygon_subgraph_mask)
    ):
        if not any(mask_row):
            continue

        vertices = []
        polygon_type = "unknown"
        polygon_color = "#888888"
        opacity = 1.0

        for j, (point, mask) in enumerate(zip(polygon_data, mask_row)):
            if not mask:
                continue

            begin_x, begin_y, end_x, end_y = point[:4]
            polygon_onehot = point[4:7]

            if len(vertices) == 0:
                if polygon_onehot[0] > 0.5:  # crosswalk
                    polygon_type = "crosswalk"
                    polygon_color = "#E6E6E6"
                    opacity = 0.5
                elif polygon_onehot[1] > 0.5:  # stopline
                    polygon_type = "stopline"
                    polygon_color = "#FF6B6B"
                elif polygon_onehot[2] > 0.5:  # static_element
                    polygon_type = "static_element"
                    polygon_color = "#D6B914"

            vertices.append([begin_x, begin_y, 0])
            vertices.append([end_x, end_y, 0])

        if len(vertices) >= 3:
            result["polygons"].append(
                {
                    "id": f"polygon_{i}",
                    "vertices": vertices,
                    "color": polygon_color,
                    "opacity": opacity,
                    "type": polygon_type,
                }
            )

    # 处理feature_obj数据
    ego_heading = feature_obj[0][0][10]
    result["metadata"]["ego_heading"] = ego_heading - np.pi * 0.5
    mask_obj_algo_flag = False
    if "infer_pred_mask" in data:
        mask_obj_algo = data["infer_pred_mask"]
        mask_obj_algo_flag = True
    for i, (obj_group, mask_row) in enumerate(zip(feature_obj, feature_obj_mask)):
        mask_row = mask_row.astype(bool)
        if not np.any(mask_row):
            continue
        # find valid object time
        if mask_row[5]:
            valid_idx = 5
        else:
            valid_idx = np.argmax(mask_row)
        obj = obj_group[valid_idx]
        begin_x, begin_y = obj[0], obj[1]
        end_x, end_y = obj[2], obj[3]
        end_vel_x, end_vel_y = obj[4], obj[5]
        begin_t, end_t = obj[6], obj[7]
        end_width, end_length = obj[8], obj[9]
        end_heading = obj[10]
        obj_onehot = obj[11:15] if len(obj) >= 15 else []
        mean_t = end_t

        # if mean_t == 0:
        alpha = 1.0
        # else:
        #     continue

        vel = np.sqrt(end_vel_x**2 + end_vel_y**2)
        vel = round(vel, 2)

        obj_type = "unknown"
        obj_color = "#FF8800"

        if len(obj_onehot) > 0:
            max_index = max(range(len(obj_onehot)), key=lambda idx: obj_onehot[idx])
            obj_colors = ["#FF6B6B", "#98D8C8", "#5D8BF4", "#FFBE7D"]
            obj_types = ["unknown", "pedestrian", "vehicle", "cyclist"]

            if max_index < len(obj_colors):
                obj_color = obj_colors[max_index]
                obj_type = obj_types[max_index]

        if i == 0:
            obj_color = "#FF4757"

        result["polylines"].append(
            {
                "id": f"trajectory_{i}_{valid_idx}",
                "points": [[begin_x, begin_y, 2], [end_x, end_y, 2]],
                "color": obj_color,
                "thickness": 0.1,
            }
        )

        result["objects"].append(
            {
                "id": f"object_{i}_{valid_idx}",
                "position": [end_x, end_y, 2],
                "dimensions": {
                    "width": max(0.2, end_width),
                    "depth": 1.5,
                    "height": max(0.2, end_length),
                },
                "rotation": [0, 0, end_heading + np.pi * 0.5],
                "color": obj_color,
                "type": obj_type,
                "opacity": alpha,
            }
        )

        result["texts"].append(
            {
                "id": f"vel_{i}_{valid_idx}",
                "position": [end_x, end_y, 3],
                "content": f"{float(vel)} m/s",
                "fontSize": 30,
                "color": "#FFFFFF",
            }
        )
        if has_pred and i > 0:
            if (mask_obj_algo_flag and mask_obj_algo[i, 5] > 0.5) or (
                not mask_obj_algo_flag and valid_idx == 5
            ):
                pred_traj = prediction_traj_winner[i - 1]
                # pred_traj=np.vstack(
                #     [[end_x, end_y],pred_traj]
                # )
                pred_traj = np.concatenate(
                    [pred_traj, np.full((pred_traj.shape[0], 1), 2)], axis=1
                )
                timestamps = np.arange(0, pred_traj.shape[0]) * 0.5
                result["future_obj_pred"].append(
                    {
                        "id": f"{i}",
                        "points": pred_traj.tolist(),
                        "timestamps": timestamps.tolist(),
                    }
                )
        # for j, (obj, mask) in enumerate(zip(obj_group, mask_row)):
        #     if not mask:
        #         continue
        #     if j == 0:
        #         continue
        #     begin_x, begin_y = obj[0], obj[1]
        #     end_x, end_y = obj[2], obj[3]
        #     end_vel_x, end_vel_y = obj[4], obj[5]
        #     begin_t, end_t = obj[6], obj[7]
        #     end_width, end_length = obj[8], obj[9]
        #     end_heading = obj[10]
        #     obj_onehot = obj[11:15] if len(obj) >= 15 else []
        #     mean_t = end_t

        #     if mean_t == 0:
        #         alpha = 1.0
        #     else:
        #         continue

        #     vel = np.sqrt(end_vel_x**2 + end_vel_y**2)
        #     vel = round(vel, 2)

        #     obj_type = "unknown"
        #     obj_color = "#FF8800"

        #     if len(obj_onehot) > 0:
        #         max_index = max(range(len(obj_onehot)), key=lambda idx: obj_onehot[idx])
        #         obj_colors = ["#FF6B6B", "#98D8C8", "#5D8BF4", "#FFBE7D"]
        #         obj_types = ["vehicle", "pedestrian", "cyclist", "other"]

        #         if max_index < len(obj_colors):
        #             obj_color = obj_colors[max_index]
        #             obj_type = obj_types[max_index]

        #     if i == 0:
        #         obj_color = "#FF4757"

        #     result["polylines"].append(
        #         {
        #             "id": f"trajectory_{i}_{j}",
        #             "points": [[begin_x, begin_y, 2], [end_x, end_y, 2]],
        #             "color": obj_color,
        #             "thickness": 0.1,
        #         }
        #     )

        #     result["objects"].append(
        #         {
        #             "id": f"object_{i}_{j}",
        #             "position": [end_x, end_y, 2],
        #             "dimensions": {
        #                 "width": max(0.2, end_width),
        #                 "depth": 1.5,
        #                 "height": max(0.2, end_length),
        #             },
        #             "rotation": [0, 0, end_heading + np.pi * 0.5],
        #             "color": obj_color,
        #             "type": obj_type,
        #             "opacity": alpha,
        #         }
        #     )

        #     result["texts"].append(
        #         {
        #             "id": f"vel_{i}_{j}",
        #             "position": [end_x, end_y, 3],
        #             "content": f"{float(vel)} m/s",
        #             "fontSize": 30,
        #             "color": "#FFFFFF",
        #         }
        #     )

    # 处理ego路径
    ego_path_points = []
    for point, mask in zip(ego_path_point, ego_path_mask):
        if mask:
            x, y = point[:2]
            ego_path_points.append([x, y, 2])

    if len(ego_path_points) > 1:
        result["polylines"].append(
            {
                "id": "ego_path",
                "points": ego_path_points,
                "color": "#FF0000",
                "thickness": 0.1,
                "type": "solid",
            }
        )

    # 处理road island
    road_island_points_list = data.get("road_island_points_list", [])
    for i, island in enumerate(road_island_points_list):
        vertices = []
        for point in island:
            x, y = point[:2]
            vertices.append([x, y, 0])
        result["polygons"].append(
            {
                "id": f"road_island_{i}",
                "vertices": vertices,
                "color": "#00FF00",
                "opacity": 0.5,
                "type": "road_island",
            }
        )

    return result

async def extract_geometry_from_pickle_rotate_async(
    pickle_path: str, config: Optional[Dict[str, Any]] = None
):
    """异步版本的几何数据提取"""
    # 异步加载pickle文件
    data = await load_pickle_async(pickle_path)
    if isinstance(data, list):
        # data[0][0]: pkl_path
        # data[0][1]: original_dict
        data = data[0][1]
    result = {
        "polylines": [],
        "polygons": [],
        "arrows": [],
        "objects": [],
        "texts": [],
        "future_obj_infos": [],
        "future_obj_pred": [],
        "metadata": {
            "filename": os.path.basename(pickle_path),
            "timestamp": datetime.now().isoformat(),
        },
        # "tbt": {}
        "centerlines_for_drivable": [],
        
    }
    if "centerlines_for_drivable" in data:
        line_dict=data["centerlines_for_drivable"]
        points_list=line_dict["points_list"]
        points_id_list=line_dict["points_id_list"]
        points_recommend_label=line_dict["points_recommend_label"]
        for points,id,recommend_label in zip(points_list,points_id_list,points_recommend_label):
            result["centerlines_for_drivable"].append(
                {
                    "id": f"{id}",
                    "points": points.tolist(),
                    "points_recommend_label": int(recommend_label),
                }
            )
  
    # TBT数据处理
    if data.get("tbt"):
        tbt = data["tbt"]
        dist = tbt["dist"]
        maneuver_text = maneuver_dict[tbt["maneuver_id"]]
        lane_action_text = "|"
        for lane_action in tbt["front_lane"]:
            lane_action_text += lane_action_dict[lane_action[1]] + "|"
        result["tbt"] = {
            "dist": dist,
            "maneuver": maneuver_text,
            "lane_action": lane_action_text,
        }
    if "ego_traffic_light" in data:
        # [1,0,0,0] unknown, [0,1,0,0] red, [0,0,1,0] green, [0,0,0,1] yellow
        ego_traffic_light = data["ego_traffic_light"][2:]
        max_index = np.argmax(ego_traffic_light[:4])
        sign_map = {0: "Unknown", 1: "Red", 2: "Green", 3: "Yellow"}
        sign = sign_map.get(max_index, "Unknown")
        # 获取16进制颜色 0->black 1->red 2->green 3->yellow
        color_map = {0: "#000000", 1: "#FF0000", 2: "#00FF00", 3: "#FFFF00"}
        color = color_map.get(max_index, "Black")

        result["traffic_lights"] = {"color": color, "sign": sign}

    # 获取数据数组
    ployline_lane_type = data.get("polyline_lane_type", None)
    polyline_arrow = data["ployline_arrow"]
    polyline_arrow_mask = data["ployline_arrow_mask"]
    polygon_subgraph = data["ploygon_subgraph"]
    polygon_subgraph_mask = data["ploygon_subgraph_mask"]
    feature_obj = data["feature_obj"]
    feature_obj_mask = data["mask_obj"]
    ego_path_point = data["path_point"]
    ego_path_mask = data["path_mask"]
    navigation_path = data["navigation_path"]
    navigation_path_mask = data["navigation_mask"]

    if "Seach" in data:
        future_obj = data["Seach"]
        future_obj_mask = data["Sweach"]
        # future_obj = np.array(future_obj)
        # future_obj_mask = np.array(future_obj_mask)
        valid = np.any(future_obj_mask, axis=1)
        valid_obj_indices = np.where(valid)[0]
        # 提取有效轨迹和对应的掩码
        valid_future_obj = future_obj[valid_obj_indices]  # 形状 (有效轨迹数, 12, 2)
        valid_future_obj_mask = future_obj_mask[
            valid_obj_indices
        ]  # 形状 (有效轨迹数, 12)
        padding_points = feature_obj[
            valid_obj_indices, 5, 2:4
        ]  # (有效轨迹数，2) 使用当前时刻位置padding初始时刻
        for idx, (traj, padding_point, mask) in enumerate(
            zip(valid_future_obj, padding_points, valid_future_obj_mask)
        ):
            valid_points = traj[mask.astype(bool)]
            valid_indices = np.where(mask)[0]
            filter_t4 = np.where((valid_indices + 1) * 0.5 <= 4)[0]
            valid_points = valid_points[filter_t4]
            # add z value 2 to valid_points
            valid_points = np.concatenate(
                [valid_points, np.full((valid_points.shape[0], 1), 2)], axis=1
            )
            timestamps = (valid_indices[filter_t4] + 1) * 0.5
            # caculate heading
            heading = np.arctan2(
                valid_points[1:, 1] - valid_points[:-1, 1],
                valid_points[1:, 0] - valid_points[:-1, 0],
            )
            heading = np.concatenate(
                [
                    [
                        np.arctan2(
                            valid_points[0, 1] - padding_point[1],
                            valid_points[0, 0] - padding_point[0],
                        )
                    ],
                    heading,
                ],
                axis=0,
            ).tolist()
            result["future_obj_infos"].append(
                {
                    "id": f"{valid_obj_indices[idx]}",
                    "points": valid_points.tolist(),
                    "timestamps": timestamps.tolist(),
                    "rotation": heading,
                    # "color": "#5D8BF4",
                }
            )
    # 处理navigation路径
    navigation_point = []
    navi_color = "#FFB6C1"
    for point, mask in zip(navigation_path, navigation_path_mask):
        if mask:
            x, y = point[:2]
            navigation_point.append([x, y, 2])

    if len(navigation_point) > 1:
        result["polylines"].append(
            {
                "id": "navigation_point",
                "points": navigation_point,
                "color": navi_color,
                "thickness": 0.05,
                "type": "solid",
            }
        )
    
    # 处理polyline数据
    for i, (line, mask) in enumerate(zip(polyline_arrow, polyline_arrow_mask)):
        if not mask:
            continue

        begin_x, begin_y, end_x, end_y = line[:4]
        map_type_onehot = line[4:10]

        line_type = "solid"
        line_color = "#CCCCCC"

        if map_type_onehot[0] > 0.5:  # unknown
            line_color = "#555555"
        elif map_type_onehot[1] > 0.5:  # solid
            line_color = "#E6E6E6"
        elif map_type_onehot[2] > 0.5:  # dashed
            line_type = "dashed"
            line_color = "#E6E6E6"
        elif map_type_onehot[3] > 0.5:  # solid_dashed
            line_type = "solid_dashed"
            line_color = "#E6E6E6"
        elif map_type_onehot[4] > 0.5:  # dashed_solid
            line_type = "dashed_solid"
            line_color = "#E6E6E6"
        elif map_type_onehot[5] > 0.5:  # sideline
            line_color = "#8F5300"
        elif ployline_lane_type is not None:
            if ployline_lane_type[1] > 0.5 or ployline_lane_type[4] > 0.5:
                line_color = "#FFD700"  # 金色

        # 处理复合线型
        if line_type == "solid_dashed":
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_dashed",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "dashed",
                }
            )
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_solid",
                    "points": [
                        [begin_x + 0.1, begin_y + 0.1, 0],
                        [end_x + 0.1, end_y + 0.1, 0],
                    ],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "solid",
                }
            )
        elif line_type == "dashed_solid":
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_solid",
                    "points": [
                        [begin_x + 0.1, begin_y + 0.1, 0],
                        [end_x + 0.1, end_y + 0.1, 0],
                    ],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "solid",
                }
            )
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_dashed",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "dashed",
                }
            )
        else:
            result["polylines"].append(
                {
                    "id": f"polyline_{i}",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": line_type,
                }
            )

        result["arrows"].append(
            {
                "id": f"arrow_{i}",
                "start": [begin_x, begin_y, 0],
                "end": [end_x, end_y, 0],
                "color": line_color,
                "headSize": 0.1,
            }
        )

    # 处理polygon数据
    for i, (polygon_data, mask_row) in enumerate(
        zip(polygon_subgraph, polygon_subgraph_mask)
    ):
        if not any(mask_row):
            continue

        vertices = []
        polygon_type = "unknown"
        polygon_color = "#888888"
        opacity = 1.0

        for j, (point, mask) in enumerate(zip(polygon_data, mask_row)):
            if not mask:
                continue

            begin_x, begin_y, end_x, end_y = point[:4]
            polygon_onehot = point[4:7]

            if len(vertices) == 0:
                if polygon_onehot[0] > 0.5:  # crosswalk
                    polygon_type = "crosswalk"
                    polygon_color = "#E6E6E6"
                    opacity = 0.5
                elif polygon_onehot[1] > 0.5:  # stopline
                    polygon_type = "stopline"
                    polygon_color = "#FF6B6B"
                elif polygon_onehot[2] > 0.5:  # static_element
                    polygon_type = "static_element"
                    polygon_color = "#D6B914"

            vertices.append([begin_x, begin_y, 0])
            vertices.append([end_x, end_y, 0])

        if len(vertices) >= 3:
            result["polygons"].append(
                {
                    "id": f"polygon_{i}",
                    "vertices": vertices,
                    "color": polygon_color,
                    "opacity": opacity,
                    "type": polygon_type,
                }
            )

    # 处理feature_obj数据
    ego_heading = feature_obj[0][5][10]
    result["metadata"]["ego_heading"] = ego_heading - np.pi * 0.5
    mask_obj_algo_flag = False
    if "infer_pred_mask" in data:
        mask_obj_algo = data["infer_pred_mask"]
        mask_obj_algo_flag = True
    for i, (obj_group, mask_row) in enumerate(zip(feature_obj, feature_obj_mask)):
        mask_row = mask_row.astype(bool)
        if not np.any(mask_row):
            continue
        # find valid object time
        if mask_row[5]:
            valid_idx = 5
        else:
            valid_idx = np.argmax(mask_row)
        obj = obj_group[valid_idx]
        begin_x, begin_y = obj[0], obj[1]
        end_x, end_y = obj[2], obj[3]
        end_vel_x, end_vel_y = obj[4], obj[5]
        begin_t, end_t = obj[6], obj[7]
        end_width, end_length = obj[8], obj[9]
        end_heading = obj[10]
        obj_onehot = obj[11:15] if len(obj) >= 15 else []
        mean_t = end_t

        # if mean_t == 0:
        alpha = 1.0
        # else:
        #     continue

        vel = np.sqrt(end_vel_x**2 + end_vel_y**2)
        vel = round(vel, 2)

        obj_type = "unknown"
        obj_color = "#FF8800"

        if len(obj_onehot) > 0:
            max_index = max(range(len(obj_onehot)), key=lambda idx: obj_onehot[idx])
            obj_colors = ["#FF6B6B", "#98D8C8", "#5D8BF4", "#FFBE7D"]
            obj_types = ["unknown", "pedestrian", "vehicle", "cyclist"]

            if max_index < len(obj_colors):
                obj_color = obj_colors[max_index]
                obj_type = obj_types[max_index]

        if i == 0:
            obj_color = "#FF4757"

        result["polylines"].append(
            {
                "id": f"trajectory_{i}_{valid_idx}",
                "points": [[begin_x, begin_y, 2], [end_x, end_y, 2]],
                "color": obj_color,
                "thickness": 0.1,
            }
        )

        result["objects"].append(
            {
                "id": f"object_{i}_{valid_idx}",
                "position": [end_x, end_y, 2],
                "dimensions": {
                    "width": max(0.2, end_width),
                    "depth": 1.5,
                    "height": max(0.2, end_length),
                },
                "rotation": [0, 0, end_heading + np.pi * 0.5],
                "color": obj_color,
                "type": obj_type,
                "opacity": alpha,
            }
        )

        result["texts"].append(
            {
                "id": f"vel_{i}_{valid_idx}",
                "position": [end_x, end_y, 3],
                "content": f"{float(vel)} m/s",
                "fontSize": 30,
                "color": "#FFFFFF",
            }
        )
       
        # for j, (obj, mask) in enumerate(zip(obj_group, mask_row)):
        #     if not mask:
        #         continue
        #     if j == 0:
        #         continue
        #     begin_x, begin_y = obj[0], obj[1]
        #     end_x, end_y = obj[2], obj[3]
        #     end_vel_x, end_vel_y = obj[4], obj[5]
        #     begin_t, end_t = obj[6], obj[7]
        #     end_width, end_length = obj[8], obj[9]
        #     end_heading = obj[10]
        #     obj_onehot = obj[11:15] if len(obj) >= 15 else []
        #     mean_t = end_t

        #     if mean_t == 0:
        #         alpha = 1.0
        #     else:
        #         continue

        #     vel = np.sqrt(end_vel_x**2 + end_vel_y**2)
        #     vel = round(vel, 2)

        #     obj_type = "unknown"
        #     obj_color = "#FF8800"

        #     if len(obj_onehot) > 0:
        #         max_index = max(range(len(obj_onehot)), key=lambda idx: obj_onehot[idx])
        #         obj_colors = ["#FF6B6B", "#98D8C8", "#5D8BF4", "#FFBE7D"]
        #         obj_types = ["vehicle", "pedestrian", "cyclist", "other"]

        #         if max_index < len(obj_colors):
        #             obj_color = obj_colors[max_index]
        #             obj_type = obj_types[max_index]

        #     if i == 0:
        #         obj_color = "#FF4757"

        #     result["polylines"].append(
        #         {
        #             "id": f"trajectory_{i}_{j}",
        #             "points": [[begin_x, begin_y, 2], [end_x, end_y, 2]],
        #             "color": obj_color,
        #             "thickness": 0.1,
        #         }
        #     )

        #     result["objects"].append(
        #         {
        #             "id": f"object_{i}_{j}",
        #             "position": [end_x, end_y, 2],
        #             "dimensions": {
        #                 "width": max(0.2, end_width),
        #                 "depth": 1.5,
        #                 "height": max(0.2, end_length),
        #             },
        #             "rotation": [0, 0, end_heading + np.pi * 0.5],
        #             "color": obj_color,
        #             "type": obj_type,
        #             "opacity": alpha,
        #         }
        #     )

        #     result["texts"].append(
        #         {
        #             "id": f"vel_{i}_{j}",
        #             "position": [end_x, end_y, 3],
        #             "content": f"{float(vel)} m/s",
        #             "fontSize": 30,
        #             "color": "#FFFFFF",
        #         }
        #     )

    # 处理ego路径
    ego_path_points = []
    for point, mask in zip(ego_path_point, ego_path_mask):
        if mask:
            x, y = point[:2]
            ego_path_points.append([x, y, 2])

    if len(ego_path_points) > 1:
        result["polylines"].append(
            {
                "id": "ego_path",
                "points": ego_path_points,
                "color": "#FF0000",
                "thickness": 0.1,
                "type": "solid",
            }
        )

    # 处理road island
    road_island_points_list = data.get("road_island_points_list", [])
    for i, island in enumerate(road_island_points_list):
        vertices = []
        for point in island:
            x, y = point[:2]
            vertices.append([x, y, 0])
        result["polygons"].append(
            {
                "id": f"road_island_{i}",
                "vertices": vertices,
                "color": "#00FF00",
                "opacity": 0.5,
                "type": "road_island",
            }
        )

    return result
@router.post("/api/visualize-pickle")
async def visualize_pickle(request: PickleVisualizationRequest):
    """
    接收pickle文件路径，解析为Three.js可视化所需的数据格式

    返回包含以下字段的JSON:
    - polylines: 线段数据
    - polygons: 多边形数据
    - arrows: 箭头数据
    - texts: 文本标注信息
    - objects: 3D对象数据
    - metadata: 元数据信息
    """
    # request.pickle_path = '/extra-data/Prediction/46462/code-space/leapprediction/test.pkl'
    # request.config = {}
    # 检查文件是否存在
    # print(request.pickle_path)
    if not os.path.exists(request.pickle_path):
        raise HTTPException(status_code=404, detail="Pickle文件不存在")

    try:
        # 使用提取函数获取转换后的数据
        visualization_data = await extract_geometry_from_pickle_async(
            request.pickle_path, config=request.config
        )

        return JSONResponse(content=visualization_data)

    except Exception as e:
        print(e)
        raise HTTPException(status_code=500, detail=f"解析Pickle文件失败: {str(e)}")

@router.post("/api/visualize-pickle-rotate")
async def visualize_pickle_rotate(request: PickleVisualizationRequest):
    """
    接收pickle文件路径，解析为Three.js可视化所需的数据格式

    返回包含以下字段的JSON:
    - polylines: 线段数据
    - polygons: 多边形数据
    - arrows: 箭头数据
    - texts: 文本标注信息
    - objects: 3D对象数据
    - metadata: 元数据信息
    """
    # request.pickle_path = '/extra-data/Prediction/46462/code-space/leapprediction/test.pkl'
    # request.config = {}
    # 检查文件是否存在
    # print(request.pickle_path)
    if not os.path.exists(request.pickle_path):
        raise HTTPException(status_code=404, detail="Pickle文件不存在")

    try:
        # 使用提取函数获取转换后的数据
        visualization_data = await extract_geometry_from_pickle_rotate_async(
            request.pickle_path, config=request.config
        )

        return JSONResponse(content=visualization_data)

    except Exception as e:
        print(e)
        raise HTTPException(status_code=500, detail=f"解析Pickle文件失败: {str(e)}")
# 辅助函数 - 将来实现真正的pickle解析逻辑时会用到


def extract_geometry_from_pickle(
    pickle_path: str, config: Optional[Dict[str, Any]] = None
):
    """
    从pickle文件中提取几何数据

    参数:
    - pickle_path: pickle文件路径
    - config: 可选配置参数

    返回:
    - 包含几何数据的字典
    """
    # TODO: 实现真正的提取逻辑
    # 此处应打开pickle文件，解析内容，提取几何数据

    # 示例实现
    data = load_pickle(pickle_path)
    result = {
        "polylines": [],
        "polygons": [],
        "arrows": [],
        "objects": [],
        "texts": [],
        "future_obj_infos": [],
        "recommend_lanes": [],
        "future_obj_pred": [],
        "metadata": {
            "filename": os.path.basename(pickle_path),
            "timestamp": datetime.now().isoformat(),
        },
        # "tbt": {}
        "centerlines_for_drivable": [],
        
    }
    if "centerlines_for_drivable" in data:
        line_dict=data["centerlines_for_drivable"]
        points_list=line_dict["points_list"]
        points_id_list=line_dict["points_id_list"]
        points_recommend_label=line_dict["points_recommend_label"]
        for points,id,recommend_label in zip(points_list,points_id_list,points_recommend_label):
            result["centerlines_for_drivable"].append(
                {
                    "id": f"{id}",
                    "points": points,
                    "points_recommend_label": recommend_label,
                }
            )
    has_pred = False
    if "prediction_traj_winner" in data:
        has_pred = True
        prediction_traj_winner = data["prediction_traj_winner"]  # 99,12,2
    if data.get("tbt"):
        tbt = data["tbt"]
        dist = tbt["dist"]
        maneuver_text = maneuver_dict[tbt["maneuver_id"]]
        lane_action_text = "|"
        for lane_action in tbt["front_lane"]:
            lane_action_text += lane_action_dict[lane_action[1]] + "|"
        result["tbt"] = {
            "dist": dist,
            "maneuver": maneuver_text,
            "lane_action": lane_action_text,
        }
    # (end_x, end_y,begin_x, begin_y, *map_type) = 4+6 = 10
    # ployline__unknown_onehot = (1.0, 0.0, 0.0, 0.0, 0.0, 0.0)
    # ployline__solid_onehot = (0.0, 1.0, 0.0, 0.0, 0.0, 0.0)
    # ployline__dashed_onehot = (0.0, 0.0, 1.0, 0.0, 0.0, 0.0)
    # ployline__solid_dashed_onehot = (0.0, 0.0, 0.0, 1.0, 0.0, 0.0)
    # ployline__dashed_solid_onehot = (0.0, 0.0, 0.0, 0.0, 1.0, 0.0)
    # ployline__sideline_onehot = (0.0, 0.0, 0.0, 0.0, 0.0, 1.0)

    polyline_arrow = data["ployline_arrow"]  # (700, 10)
    polyline_arrow_mask = data["ployline_arrow_mask"]  # (700,)

    # (begin_x, begin_y, end_x, end_y, *ploygon_onehot) = 4+3 = 7
    # ploygon__crosswalk_onehot = (1.0, 0.0, 0.0)
    # ploygon__stopline_onehot = (0.0, 1.0, 0.0)
    # ploygon__static_element_onehot = (0.0, 0.0, 1.0)
    # each polygon has 4 arrows
    polygon_subgraph = data["ploygon_subgraph"]  # (40,4,7)
    polygon_subgraph_mask = data["ploygon_subgraph_mask"]  # (40,4)

    # [
    #         begin_position.x, begin_position.y,
    #         end_position.x, end_position.y,
    #         end_velocity.x, end_velocity.y,
    #         begin_t, end_t,
    #         end_size.width, end_size.length,
    #         end_info.heading, *onehot
    #  ]
    feature_obj = data["feature_obj"]  # (40,10,15)
    feature_obj_mask = data["mask_obj"]  # (40,10)

    ego_path_point = data["path_point"]  # (200,2)
    ego_path_mask = data["path_mask"]  # (200,)

    navigation_path = data["navigation_path"]  # (50,2)
    navigation_path_mask = data["navigation_mask"]  # (50,)

    navigation_point = []
    # 浅粉色
    navi_color = "#FFB6C1"
    for point, mask in zip(navigation_path, navigation_path_mask):
        if mask:
            x, y = point[:2]
            navigation_point.append([x, y, 2])

            # 如果有足够的点，才添加路径
    if len(navigation_point) > 1:
        result["polylines"].append(
            {
                "id": "navigation_point",
                "points": navigation_point,
                "color": navi_color,
                "thickness": 0.05,
                "type": "solid",
            }
        )

    # 实际解析逻辑将在这里实现
    for i, (line, mask) in enumerate(zip(polyline_arrow, polyline_arrow_mask)):
        if not mask:  # 忽略被掩码的数据
            continue

        # 解析数据
        begin_x, begin_y, end_x, end_y = line[:4]
        map_type_onehot = line[4:10]

        # 确定线的类型和颜色
        # 确定线的类型和颜色
        line_type = "solid"
        line_color = "#CCCCCC"  # 默认浅灰色，更柔和

        # 根据onehot向量确定线型
        if map_type_onehot[0] > 0.5:  # unknown
            line_color = "#555555"  # 深灰色
        elif map_type_onehot[1] > 0.5:  # solid
            line_color = "#E6E6E6"  # 亮灰色，更清晰
        elif map_type_onehot[2] > 0.5:  # dashed
            line_type = "dashed"
            line_color = "#E6E6E6"  # 亮灰色
        elif map_type_onehot[3] > 0.5:  # solid_dashed
            line_type = "solid_dashed"
            line_color = "#E6E6E6"  # 亮灰色
        elif map_type_onehot[4] > 0.5:  # dashed_solid
            line_type = "dashed_solid"
            line_color = "#E6E6E6"  # 亮灰色
        elif map_type_onehot[5] > 0.5:  # sideline
            line_color = "#FFD700"  # 金色，更明显但不刺眼

        # 添加到结果

        if line_type == "solid_dashed":
            # 添加虚线的起点和终点
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_dashed",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "dashed",
                }
            )
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_dashed",
                    "points": [
                        [begin_x + 0.1, begin_y + 0.1, 0],
                        [end_x + 0.1, end_y + 0.1, 0],
                    ],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "solid",
                }
            )
        elif line_type == "dashed_solid":
            # 添加虚线的起点和终点
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_solid",
                    "points": [
                        [begin_x + 0.1, begin_y + 0.1, 0],
                        [end_x + 0.1, end_y + 0.1, 0],
                    ],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "solid",
                }
            )
            result["polylines"].append(
                {
                    "id": f"polyline_{i}_dashed",
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": "dashed",
                }
            )
        else:
            result["polylines"].append(
                {
                    "id": f"polyline_{i}",
                    # 注意：y坐标作为Three.js中的z坐标
                    "points": [[begin_x, begin_y, 0], [end_x, end_y, 0]],
                    "color": line_color,
                    "thickness": 0.05,
                    "type": line_type,
                }
            )
        # 如果是带箭头的线，添加箭头
        result["arrows"].append(
            {
                "id": f"arrow_{i}",
                "start": [begin_x, begin_y, 0],
                "end": [end_x, end_y, 0],
                "color": line_color,
                "headSize": 0.1,
            }
        )
    if "recommend_lane_xy" in data:
        # recommend_lane_xy (10,25,2) 10 lines 25 points 2 dimensions
        # recommend_lane_xy_mask (10,25) valid mask
        # extract all valid line points and convert to format like arrows and  add to results
        recommend_lane_xy = data["recommend_lane_xy"]
        recommend_lane_xy_mask = data["recommend_lane_xy_mask"]
        for i, (lane_data, lane_mask) in enumerate(
            zip(recommend_lane_xy, recommend_lane_xy_mask)
        ):
            # if valid points <2 continue
            if np.sum(lane_mask) < 2:
                continue
            valid_lane_points = lane_data[lane_mask.astype(bool)]
            # two points construct one arrow line
            for j in range(len(valid_lane_points) // 2):
                begin_x = valid_lane_points[j * 2][0]
                begin_y = valid_lane_points[j * 2][1]
                end_x = valid_lane_points[j * 2 + 1][0]
                end_y = valid_lane_points[j * 2 + 1][1]
                result["recommend_lanes"].append(
                    {
                        "id": f"recommend_lane_{i}_{j}",
                        "start": [begin_x, begin_y, 0],
                        "end": [end_x, end_y, 0],
                        "color": "#FFB108FF",
                        "headSize": 0.3,
                    }
                )

    for i, (polygon_data, mask_row) in enumerate(
        zip(polygon_subgraph, polygon_subgraph_mask)
    ):
        # 检查是否至少有一个有效点
        if not any(mask_row):
            continue

        # 收集多边形的顶点
        vertices = []
        polygon_type = "unknown"
        polygon_color = "#888888"  # 默认灰色
        opcaity = 1.0

        for j, (point, mask) in enumerate(zip(polygon_data, mask_row)):
            if not mask:
                continue

            begin_x, begin_y, end_x, end_y = point[:4]
            polygon_onehot = point[4:7]

            # 第一个有效点决定多边形类型和颜色
            if len(vertices) == 0:
                if polygon_onehot[0] > 0.5:  # crosswalk
                    polygon_type = "crosswalk"
                    polygon_color = "#E6E6E6"  # 浅灰白色
                    opcaity = 0.5
                elif polygon_onehot[1] > 0.5:  # stopline
                    polygon_type = "stopline"
                    polygon_color = "#FF6B6B"  # 柔和的红色
                elif polygon_onehot[2] > 0.5:  # static_element
                    polygon_type = "static_element"
                    # 亮黄色
                    polygon_color = "#FFD700"  # 金色

            # 添加起点和终点作为多边形的顶点
            vertices.append([begin_x, begin_y, 0])
            vertices.append([end_x, end_y, 0])

        # 只有当有足够的顶点时才添加多边形
        if len(vertices) >= 3:
            result["polygons"].append(
                {
                    "id": f"polygon_{i}",
                    "vertices": vertices,
                    "color": polygon_color,
                    "opacity": opcaity,
                    "type": polygon_type,
                }
            )

    # 处理feature_obj数据 - (40,10,15) 的形状
    feature_obj = data["feature_obj"]  # (40,10,15)
    feature_obj_mask = data["mask_obj"]  # (40,10)
    ego_heading = feature_obj[0][0][10]  # 假设第一个对象的heading是ego的heading
    result["metadata"]["ego_heading"] = ego_heading - np.pi * 0.5
    mask_obj_algo_flag = False
    if "infer_pred_mask" in data:
        mask_obj_algo = data["infer_pred_mask"]
        mask_obj_algo_flag = True
    for i, (obj_group, mask_row) in enumerate(zip(feature_obj, feature_obj_mask)):
        # mask_row = mask_row.astype(bool)
        if not np.any(mask_row > 0.5):
            continue
        # find valid object time
        if mask_row[5] > 0.5:
            valid_idx = 5
        else:
            valid_idx = np.argmax(mask_row)
        obj = obj_group[valid_idx]
        begin_x, begin_y = obj[0], obj[1]
        end_x, end_y = obj[2], obj[3]
        end_vel_x, end_vel_y = obj[4], obj[5]
        begin_t, end_t = obj[6], obj[7]
        end_width, end_length = obj[8], obj[9]
        end_heading = obj[10]
        obj_onehot = obj[11:15] if len(obj) >= 15 else []
        mean_t = end_t

        # if mean_t == 0:
        alpha = 1.0
        # else:
        #     continue

        vel = np.sqrt(end_vel_x**2 + end_vel_y**2)
        vel = round(vel, 2)

        obj_type = "unknown"
        obj_color = "#FF8800"

        if len(obj_onehot) > 0:
            max_index = max(range(len(obj_onehot)), key=lambda idx: obj_onehot[idx])
            obj_colors = ["#FF6B6B", "#98D8C8", "#5D8BF4", "#FFBE7D"]
            obj_types = ["vehicle", "pedestrian", "cyclist", "other"]

            if max_index < len(obj_colors):
                obj_color = obj_colors[max_index]
                obj_type = obj_types[max_index]

        if i == 0:
            obj_color = "#FF4757"

        result["polylines"].append(
            {
                "id": f"trajectory_{i}_{valid_idx}",
                "points": [[begin_x, begin_y, 2], [end_x, end_y, 2]],
                "color": obj_color,
                "thickness": 0.1,
            }
        )

        result["objects"].append(
            {
                "id": f"object_{i}_{valid_idx}",
                "position": [end_x, end_y, 2],
                "dimensions": {
                    "width": max(0.2, end_width),
                    "depth": 1.5,
                    "height": max(0.2, end_length),
                },
                "rotation": [0, 0, end_heading + np.pi * 0.5],
                "color": obj_color,
                "type": obj_type,
                "opacity": alpha,
            }
        )

        result["texts"].append(
            {
                "id": f"vel_{i}_{valid_idx}",
                "position": [end_x, end_y, 3],
                "content": f"{float(vel)} m/s",
                "fontSize": 30,
                "color": "#FFFFFF",
            }
        )
        if has_pred and i > 0:
            if (mask_obj_algo_flag and mask_obj_algo[i, 5] > 0.5) or (
                not mask_obj_algo_flag and valid_idx == 5
            ):
                pred_traj = prediction_traj_winner[i - 1]
                pred_traj = np.concatenate(
                    [pred_traj, np.full((pred_traj.shape[0], 1), 2)], axis=1
                )
                timestamps = np.arange(0, pred_traj.shape[0]) * 0.5
                result["future_obj_pred"].append(
                    {
                        "id": f"{i}",
                        "points": pred_traj.tolist(),
                        "timestamps": timestamps.tolist(),
                    }
                )
    if "Seach" in data:
        future_obj = data["Seach"]
        future_obj_mask = data["Sweach"]
        # future_obj = np.array(future_obj)
        # future_obj_mask = np.array(future_obj_mask)
        valid = np.any(future_obj_mask, axis=1)
        valid_obj_indices = np.where(valid)[0]
        # 提取有效轨迹和对应的掩码
        valid_future_obj = future_obj[valid_obj_indices]  # 形状 (有效轨迹数, 12, 2)
        valid_future_obj_mask = future_obj_mask[
            valid_obj_indices
        ]  # 形状 (有效轨迹数, 12)
        padding_points = feature_obj[
            valid_obj_indices, 5, 2:4
        ]  # (有效轨迹数，2) 使用当前时刻位置padding初始时刻
        for idx, (traj, padding_point, mask) in enumerate(
            zip(valid_future_obj, padding_points, valid_future_obj_mask)
        ):
            valid_points = traj[mask.astype(bool)]
            valid_indices = np.where(mask)[0]
            filter_t4 = np.where((valid_indices + 1) * 0.5 <= 4)[0]
            valid_points = valid_points[filter_t4]
            # add z value 2 to valid_points
            valid_points = np.concatenate(
                [valid_points, np.full((valid_points.shape[0], 1), 2)], axis=1
            )
            timestamps = (valid_indices[filter_t4] + 1) * 0.5
            # caculate heading
            heading = np.arctan2(
                valid_points[1:, 1] - valid_points[:-1, 1],
                valid_points[1:, 0] - valid_points[:-1, 0],
            )
            heading = np.concatenate(
                [
                    [
                        np.arctan2(
                            valid_points[0, 1] - padding_point[1],
                            valid_points[0, 0] - padding_point[0],
                        )
                    ],
                    heading,
                ],
                axis=0,
            ).tolist()
            result["future_obj_infos"].append(
                {
                    "id": f"{valid_obj_indices[idx]}",
                    "points": valid_points.tolist(),
                    "timestamps": timestamps.tolist(),
                    "rotation": heading,
                    # "color": "#5D8BF4",
                }
            )
            # 处理ego_path数据，将所有有效点连成一条线
    ego_path_points = []
    for point, mask in zip(ego_path_point, ego_path_mask):
        if mask:
            x, y = point[:2]
            ego_path_points.append([x, y, 2])

    # 如果有足够的点，才添加路径
    if len(ego_path_points) > 1:
        result["polylines"].append(
            {
                "id": "ego_path",
                "points": ego_path_points,
                "color": "#FF0000",
                "thickness": 0.1,
                "type": "solid",
            }
        )

    god_object_list = data.get("god_object_list", [])
    road_island_points_list = data.get("road_island_points_list", [])
    for island in road_island_points_list:
        vertices = []
        for point in island:
            x, y = point[:2]
            vertices.append([x, y, 0])
        result["polygons"].append(
            {
                "id": f"road_island_{i}",
                "vertices": vertices,
                "color": "#00FF00",
                "opacity": 0.5,
                "type": "road_island",
            }
        )

    return result


if __name__ == "__main__":
    # 测试代码
    # test_pickle_path = "/extra-data/Prediction/46462/code-space/leapprediction/test.pkl"
    # pkl_dir="/extraStore/groupdatahw01/Predictionalgorithm/37929/data/redmine_testcase/0806/pkl/LFZ63AX5XSD025306_record_data_2025_08_06_21_37_35/"
    # pkl_name="C10^LFZ63AX5XSD025306_record_data_2025_08_06_21_37_35^1754487510084747274^[]^[3]^1^1^1^0^0^0^0.pkl"
    # dlp1
    # pkl_dir="/extraStore/groupdatahw01/Predictionalgorithm/37929/data/redmine_testcase/0810/pkl/LFZ63AX5XSD025306_record_data_2025_08_10_13_57_31/"
    # pkl_name='C10^LFZ63AX5XSD025306_record_data_2025_08_10_13_57_31^1754805507431153481^[]^[2]^1^1^1^0^0^0^0.pkl'
    pkl_dir = "/extraStore/groupdatahw01/Predictionalgorithm/37929/data/redmine_testcase/dagger_0813/pkl/LFZ63AX5XSD025306_record_data_2025_08_13_07_26_21/"
    pkl_name = "C10^LFZ63AX5XSD025306_record_data_2025_08_13_07_26_21^1755041236438009163^[1,0]^[0]^1^1^1^0^4^0^0.pkl"
    pdp_pkl_dir = "/extraStore/groupdatahw01/Predictionalgorithm/39736/0812_lcc_general/pkl/LFZ63AX57SD000511_record_data_2025_08_10_10_24_41/"
    pdp_pkl_name = "B10^LFZ63AX57SD000511_record_data_2025_08_10_10_24_41^1754793175452713701^[0]^[0]^1^1^1^0^0^0^0.pkl"
    dlp_pkl_path = pkl_dir + pkl_name
    pdp_pkl_path = pdp_pkl_dir + pdp_pkl_name
    debug_dir = "/extraStore/groupdatahw01/Predictionalgorithm/37929/data/redmine_testcase/dagger_0815/pkl/LFZ63AX55SD025228_record_data_2025_08_15_18_11_01/"
    debug_dir += "C10^LFZ63AX55SD025228_record_data_2025_08_15_18_11_01^1755252664541879930^[]^[2]^1^1^1^0^0^0^0.pkl"
    expert_pkl = "/extraStore/groupdatahw01/Predictionalgorithm/50380/generated_data/all/20250808/from_bags/urp/pkl/LFZ63AN51RD017575_record_data_2025_03_08_18_33_42/C10^LFZ63AN51RD017575_record_data_2025_03_08_18_33_42^1741459010682000000^[0,1]^[9,10]^1^1^1^0^0^0^0.pkl"
    predict_pkl = "/extraStore/groupdatahw01/Predictionalgorithm/37929/data/redmine_testcase/expert_0904a/pkl/LFZ63AX57SD023884_record_data_2025_09_04_09_18_50/C10^LFZ63AX57SD023884_record_data_2025_09_04_09_18_50^1756948725845310994^[]^[0]^1^1^1^1^0^0^0.pkl"
    original_pkl = "/extraStore/groupdatahw01/Predictionalgorithm/42060/generated_data/all/20250911_drivable/from_bags/cnap_ee35/pkl/LFZ63AX50SD020664_record_data_2025_08_16_10_40_08//B10^LFZ63AX50SD020664_record_data_2025_08_16_10_40_08^1755312043571040730^[0]^[6]^1^1^1^0^3^3^0^0^-1^0^1^117^172^1^2^117^172^0^1^0^0^0^0^0^0^0^3^467^3571^18^7^2^69^1.pkl"
    bag_pkl="/extraStore/groupdatahw01/Predictionalgorithm/42060/generated_data/all/20250911_drivable/from_bags/cnap_ee35/pkl/LFZ63AZ52SD000199_record_data_2025_04_24_14_47_01/C10^LFZ63AZ52SD000199_record_data_2025_04_24_14_47_01^1745477217542254070^[0]^[1]^1^1^1^0^0^0^0^0^-1^0^0^0^0^1^1^131^146^0^1^0^0^0^0^0^0^0^2^556^2827^0^6^0^16^1.pkl"
    extract_geometry_from_pickle(bag_pkl, config={"some_config": True})
