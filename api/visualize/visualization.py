"""
可视化相关的API端点
"""
import os
import pickle
import numpy as np
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from api.visualize.pickle_visualize import load_pickle

router = APIRouter()


class LoadPklRequest(BaseModel):
    pkl_path: str


class VisualizationResponse(BaseModel):
    success: bool
    paths: Optional[List[Dict[str, Any]]] = None
    trajs: Optional[List[Dict[str, Any]]] = None
    ego_traj_length: Optional[List[float]] = None
    ego_traj_vel: Optional[List[float]] = None
    selected_object_ids: Optional[List[str]] = None
    error: Optional[str] = None


@router.post("/load-pkl")
async def load_pkl_file(request: LoadPklRequest) -> VisualizationResponse:
    """
    直接读取PKL文件并返回可视化数据
    """
    try:
        pkl_path = request.pkl_path.strip()
        
        # 检查文件是否存在
        if not os.path.exists(pkl_path):
            return VisualizationResponse(
                success=False,
                error=f"PKL文件不存在: {pkl_path}"
            )
        
        # 读取PKL文件
        data = load_pickle(pkl_path)
        if isinstance(data, list):
            #data: list
            #data[0][0]: pkl_path
            #data[0][1]: original_dict
            data = data[0][1] 
        
        ego_v = data["ego_input"][0] ** 2 + data["ego_input"][1] ** 2
        ego_v = ego_v**0.5
        ego_s = ego_v * 6
        ego_default_s = 40
        ego_final_s = max(ego_s, ego_default_s)
        ego_final_index = int(ego_final_s / 2)
        pdp_paths = data.get("pdp_path", [])
        pdp_trajs = data.get("pdp_traj", [])
        future_obj = data.get("Seach", [])
        future_obj_mask = data.get("Sweach", [])
        # np.ndarray(200,2)
        ego_gt = data.get("path_point", [])
        ego_gt_mask = data.get("path_mask", [])
        ego_traj = data.get("ego_label", None)[:20, ...]
        # 修改: 在ego_traj前补零点，然后累加每一段的距离
        ego_traj_padded = np.concatenate([np.zeros((1, 2)), ego_traj], axis=0)
        ego_traj_diff = np.diff(ego_traj_padded, axis=0)
        ego_traj_length = np.cumsum(np.linalg.norm(ego_traj_diff, axis=-1))
        ego_traj_vel = data.get("future_traj_velocity", None)
        paths_info=[]
        # 处理GT路径
        gt_path = data.get("gt_path", None)
        if gt_path is not None and len(gt_path) > 0:
            gt_visualization_points = []
            for point in gt_path:
                if len(point) >= 2:
                    visualization_point = [
                        float(point[0]),
                        float(point[1]),
                        float(point[2]) if len(point) > 2 else 0.0,
                    ]
                    gt_visualization_points.append(visualization_point)
            
            if len(gt_visualization_points) > 0:
                gt_path_info = {
                    "index": -1,  # GT路径使用-1作为索引
                    "probability": 1.0,  # GT路径概率设为1.0
                    "points_count": len(gt_visualization_points),
                    "annotation": None,
                    "visualization_points": gt_visualization_points,
                    "middle_point": None,
                    "is_ground_truth": True,  # 标识为GT路径
                }
                paths_info.append(gt_path_info)
        
        # 处理PDP路径
        for i, path in enumerate(pdp_paths):
            prob = float(path.get("prob", 0))
            raw_points = path.get("raw_points", [])
            points_count = len(raw_points)
            
            # 提取可视化所需的点数据
            visualization_points = []
            for point in raw_points:
                if len(point) >= 2:
                    visualization_point = [
                        float(point[0]),
                        float(point[1]),
                        float(point[2]) if len(point) > 2 else 0.0,
                    ]
                    visualization_points.append(visualization_point)
            
            path_info = {
                "index": i,
                "probability": prob,
                "points_count": points_count,
                "annotation": None,
                "visualization_points": visualization_points,
                "middle_point": None,
                "is_ground_truth": False,
            }
            paths_info.append(path_info)
        
        # 提取轨迹信息
        trajs_info = []
        future_trajs = data.get("future_trajs", [])
        
        for i, traj in enumerate(pdp_trajs[:6]):
            prob = float(traj.get("prob", 0))
            vel = traj.get("vel", [])
            acc = traj.get("acc", [])
            s = traj.get("s", [])[2:]

            # 确保轨迹数据为列表格式
            if isinstance(vel, np.ndarray):
                vel = vel.tolist()
            if isinstance(acc, np.ndarray):
                acc = acc.tolist()
            if isinstance(s, np.ndarray):
                s = s.tolist()

            traj_info = {
                "index": i,
                "probability": prob,
                "vel": vel,
                "acc": acc,
                "s": s,
            }
            trajs_info.append(traj_info)
        
        # 提取ego轨迹数据
        ego_traj = data.get("ego_traj", None)
        ego_traj_length = []
        ego_traj_vel = []
        
        if ego_traj is not None and len(ego_traj) > 0:
            # 计算ego轨迹长度
            ego_traj_array = np.array(ego_traj)
            if ego_traj_array.shape[1] >= 2:
                # 在ego_traj前补零点，然后累加每一段的距离
                ego_traj_padded = np.concatenate([np.zeros((1, 2)), ego_traj_array[:, :2]], axis=0)
                ego_traj_diff = np.diff(ego_traj_padded, axis=0)
                ego_traj_length = np.cumsum(np.linalg.norm(ego_traj_diff, axis=-1)).tolist()
        
        # 提取ego轨迹速度
        future_traj_velocity = data.get("future_traj_velocity", None)
        if future_traj_velocity is not None:
            if isinstance(future_traj_velocity, np.ndarray):
                ego_traj_vel = future_traj_velocity.tolist()
            else:
                ego_traj_vel = future_traj_velocity
        
        return VisualizationResponse(
            success=True,
            paths=paths_info,
            trajs=trajs_info,
            ego_traj_length=ego_traj_length,
            ego_traj_vel=ego_traj_vel,
            selected_object_ids=[],
        )
        
    except Exception as e:
        return VisualizationResponse(
            success=False,
            error=f"读取PKL文件失败: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "ok", "message": "Visualization API is running"}
