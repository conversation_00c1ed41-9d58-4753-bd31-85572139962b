# api/evaluation_api.py

from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
import tempfile
import os
import shutil

from database.db_operations import (
    insert_evaluation_set,
    insert_many_evaluation_case_pool,
    get_evaluation_sets_with_pagination,
)
from utils.csv_parser import parse_csv_to_evaluation_case_pool

router = APIRouter()


# 获取评测集列表


# 在 evaluation_sets.py 中修改
@router.get("/api/evaluation_case_pool")
def get_evaluation_sets(
    page: int = 1,
    per_page: int = 12,
    search: Optional[str] = None
):
    # 使用新的分页查询
    results = get_evaluation_sets_with_pagination(page, per_page, search)
    if not results["success"]:
        raise HTTPException(
            status_code=500, detail=results.get("error", "查询失败"))
    return results


@router.post("/api/evaluation_case_pool/upload_csv")
async def upload_csv_file(file: UploadFile = File(...)):
    # 检查文件类型
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="只接受CSV文件")

    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')

    try:
        # 保存上传的文件
        with temp_file as f:
            shutil.copyfileobj(file.file, f)

        # 解析CSV文件
        parse_result = parse_csv_to_evaluation_case_pool(temp_file.name)

        if not parse_result["success"]:
            raise HTTPException(status_code=400, detail=parse_result["error"])

        # 批量插入数据
        data_list = parse_result["data"]
        insert_result = insert_many_evaluation_case_pool(data_list)
        print(insert_result)

        if not insert_result["success"]:
            raise HTTPException(status_code=500, detail=insert_result["error"])

        return {
            "status": "success",
            "message": f"已成功导入 {insert_result['count']} 个评测集",
            "count": insert_result["count"]
        }

    finally:
        # 清理临时文件
        os.unlink(temp_file.name)
