# evaluation_sets.py
from fastapi import APIRouter, Query, Body, Path, HTTPException, UploadFile, File, Form, Depends
import os
import csv
import tempfile
import shutil
import re
from typing import Optional, List
from api.auth.auth import get_current_user
from pydantic import BaseModel
from database.db_operations import (
    create_evaluation_set,
    get_evaluation_sets,
    get_evaluation_set_with_cases,
    delete_evaluation_set,
    add_cases_to_evaluation_case_pool,
    remove_cases_from_evaluation_set,
    execute_query,
    insert_many_evaluation_case_pool
)

router = APIRouter(prefix="/api", tags=["evaluation_sets"])
vin_pattern = r"[A-Z0-9]{17}"
vehicle_type_pattern = r"^[A-Z]\d{2}"  # 修正正则表达式，去掉开头的空格
time_ns_pattern = r"\d{19}"
# 定义数据模型


class EvaluationSetCreate(BaseModel):
    set_name: str
    creator_name: str
    description: Optional[str] = ""
    evaluation_items: Optional[List] = []
    scene_tag: Optional[List[str]] = []  # 新增场景标签字段


class CaseIdsRequest(BaseModel):
    case_ids: List[int]

# 获取评测集列表（支持分页和过滤）


@router.get("/evaluation_sets")
async def list_evaluation_sets(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    name: Optional[str] = None,
    creator: Optional[str] = None
):
    result = get_evaluation_sets(page, per_page, name, creator)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    return result

# 获取单个评测集详情



@router.get("/evaluation_sets/{evaluation_set_id}")
async def get_evaluation_set_detail(
    evaluation_set_id: int = Path(..., ge=1),
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None, description="搜索关键词"),
    fully_annotated_only: Optional[bool] = Query(
        False, description="只显示完全标注的PKL"),
    check_status: Optional[str] = Query(
        'all', description="检查状态筛选: all, checked, unchecked"),
    dirty_filter: Optional[bool] = Query(
        True, description="是否过滤脏数据"),
    annotated_filter: Optional[bool] = Query(
        False, description="是否仅显示已有路径对标注的PKL")
):
    result = get_evaluation_set_with_cases(
        evaluation_set_id, page, per_page, search, fully_annotated_only, check_status, dirty_filter, annotated_filter)
    if not result["success"]:
        raise HTTPException(status_code=404, detail=result["error"])
    return result


def get_evaluation_set_with_cases(evaluation_set_id, page=1, per_page=10, search=None, fully_annotated_only=False, check_status='all', dirty_filter=True, annotated_filter=False):
    """
    获取评测集详情及其包含的案例，支持搜索和筛选已完全标注的PKL
    """
    try:
        # 构建基础查询条件
        where_conditions = ["escp.evaluation_set_id = %s"]
        params = [evaluation_set_id]

        # 添加搜索条件
        if search:
            where_conditions.append(
                "(ecp.pkl_name LIKE %s OR ecp.vin LIKE %s)")
            search_pattern = f"%{search}%"
            params.extend([search_pattern, search_pattern])

        # 添加脏数据过滤
        if dirty_filter:
            where_conditions.append("ecp.dirty_data = FALSE")

        # 添加完全标注筛选条件 - 简化为只要存在标注记录即可
        if fully_annotated_only:
            fully_annotated_subquery = """
            ecp.id IN (
                SELECT DISTINCT pkl_id 
                FROM pdp_path_annotation 
                WHERE evaluation_set_id = %s
            )
            """
            where_conditions.append(fully_annotated_subquery)
            params.append(evaluation_set_id)

        # 添加路径对标注筛选条件
        if annotated_filter:
            path_pair_annotated_subquery = """
            ecp.id IN (
                SELECT DISTINCT pkl_id 
                FROM path_pair_annotation 
                WHERE evaluation_set_id = %s
            )
            """
            where_conditions.append(path_pair_annotated_subquery)
            params.append(evaluation_set_id)

        if check_status == 'checked':
            where_conditions.append("escp.is_checked = TRUE")
        elif check_status == 'unchecked':
            where_conditions.append("(escp.is_checked = FALSE OR escp.is_checked IS NULL)")

        where_clause = " AND ".join(where_conditions)

        # 查询评测集信息
        set_query = "SELECT id, set_name, creator_name, description, created_at FROM evaluation_set WHERE id = %s"
        set_result = execute_query(
            set_query, (evaluation_set_id,), fetch_one=True)

        if not set_result["success"] or not set_result["data"]:
            return {"success": False, "error": "评测集不存在"}

        # 查询案例总数
        count_query = f"""
        SELECT COUNT(DISTINCT ecp.id) 
        FROM evaluation_case_pool ecp 
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id 
        WHERE {where_clause}
        """
        count_result = execute_query(count_query, params, fetch_one=True)
        total_count = count_result["data"][0] if count_result["success"] else 0

        # 查询案例列表（分页）
        offset = (page - 1) * per_page
        cases_query = f"""
        SELECT DISTINCT ecp.id, ecp.pkl_name, ecp.pkl_dir, ecp.vehicle_type, 
               ecp.vin, ecp.time_ns, ecp.key_obs_id, ecp.dirty_data,
               escp.is_checked, escp.checked_at, escp.checked_by
        FROM evaluation_case_pool ecp 
        JOIN evaluation_set_case_pool escp ON ecp.id = escp.evaluation_case_id 
        WHERE {where_clause}
        ORDER BY ecp.id DESC
        LIMIT %s OFFSET %s
        """
        cases_params = params + [per_page, offset]
        cases_result = execute_query(cases_query, cases_params, fetch_all=True)

        cases = []
        if cases_result["success"] and cases_result["data"]:
            for row in cases_result["data"]:
                cases.append({
                    "id": row[0],
                    "pkl_name": row[1],
                    "pkl_dir": row[2],
                    "vehicle_type": row[3],
                    "vin": row[4],
                    "time_ns": row[5],
                    "key_obs_id": row[6],
                    "dirty_data": bool(row[7]),
                    "is_checked": bool(row[8]) if row[8] is not None else False,
                    "checked_at": row[9],
                    "checked_by": row[10]
                })

        return {
            "success": True,
            "evaluation_set": {
                "id": set_result["data"][0],
                "name": set_result["data"][1],
                "creator_name": set_result["data"][2],
                "description": set_result["data"][3],
                "created_at": set_result["data"][4]
            },
            "cases": cases,
            "case_count": total_count,
            "page": page,
            "per_page": per_page
        }

    except Exception as e:
        return {"success": False, "error": str(e)}

@router.post("/evaluation_sets")
async def create_new_evaluation_set(evaluation_set: EvaluationSetCreate):
    result = create_evaluation_set(
        evaluation_set.set_name,
        evaluation_set.creator_name,
        evaluation_set.description,
        evaluation_set.scene_tag  # 传递场景标签
    )
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    result = add_cases_to_evaluation_case_pool(
        result["id"], evaluation_set.evaluation_items)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    return result
# 删除评测集


@router.delete("/evaluation_sets/{evaluation_set_id}")
async def remove_evaluation_set(evaluation_set_id: int = Path(..., ge=1)):
    result = delete_evaluation_set(evaluation_set_id)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    return result

# 向评测集添加案例


@router.post("/evaluation_sets/{evaluation_set_id}/cases")
async def add_cases_to_set(
    case_ids_request: CaseIdsRequest,
    evaluation_set_id: int = Path(..., ge=1)
):
    result = add_cases_to_evaluation_case_pool(
        evaluation_set_id, case_ids_request.case_ids)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    return result

# 从评测集移除案例


@router.delete("/evaluation_sets/{evaluation_set_id}/cases")
async def remove_cases_from_set(
    case_ids_request: CaseIdsRequest,
    evaluation_set_id: int = Path(..., ge=1)
):
    result = remove_cases_from_evaluation_set(
        evaluation_set_id, case_ids_request.case_ids)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])
    return result

# 更新评测集信息


@router.put("/evaluation_sets/{evaluation_set_id}")
async def update_evaluation_set(
    evaluation_set: EvaluationSetCreate,
    evaluation_set_id: int = Path(..., ge=1)
):
    # 这个函数需要在db_operations.py中实现
    # 这里我们模拟一个实现
    from database.db_operations import execute_query

    query = """
    UPDATE evaluation_set 
    SET set_name = %s, creator_name = %s, description = %s 
    WHERE id = %s
    """
    params = (
        evaluation_set.set_name,
        evaluation_set.creator_name,
        evaluation_set.description,
        evaluation_set_id
    )

    result = execute_query(query, params)
    if not result["success"]:
        raise HTTPException(status_code=500, detail=result["error"])

    return {"success": True, "message": "评测集更新成功", "id": evaluation_set_id}


@router.post("/evaluation_set/upload_csv")
async def upload_csv_for_evaluation_set(
    file: UploadFile = File(...),
    set_name: str = Form(...),
    creator_name: str = Form(...),
    description: str = Form("")
):
    """
    通过上传CSV文件创建新的评测集
    
    CSV文件必须包含以下列：pkl_name, pkl_dir
    其他列如 key_obs_id, path_range_start, path_range_end 为可选项
    """
    # 检查文件类型
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="只接受CSV文件")

    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')

    try:
        # 保存上传的文件
        with temp_file as f:
            shutil.copyfileobj(file.file, f)

        # 解析CSV文件
        csv_data = []
        with open(temp_file.name, mode='r', encoding='utf-8') as f:
            csv_reader = csv.DictReader(f)
            # 验证必填字段
            required_fields = ['pkl_name', 'pkl_dir']
            if not all(field in csv_reader.fieldnames for field in required_fields):
                raise HTTPException(status_code=400,
                                    detail=f"CSV文件必须包含以下列：{', '.join(required_fields)}")

            # 读取CSV数据
            for row in csv_reader:
                csv_data.append(row)

        # 创建新的评测集
        set_result = create_evaluation_set(set_name, creator_name, description)
        if not set_result["success"]:
            raise HTTPException(status_code=500, detail=set_result["error"])

        evaluation_set_id = set_result["id"]

        # 分别收集需要查询的条件和需要创建的记录
        existing_records = []
        new_records = []

        search_params = []
        for item in csv_data:
            key_obs_id = item.get('key_obs_id', 0)
            search_params.append((item['pkl_name'], key_obs_id))
        if search_params:
            # 构建批量查询
            placeholders = ", ".join([f"(%s, %s)"] * len(search_params))
            flat_params = [param for pair in search_params for param in pair]

            query = f"""
            SELECT id, pkl_name, key_obs_id 
            FROM evaluation_case_pool 
            WHERE (pkl_name, key_obs_id) IN ({placeholders})
            """

            result = execute_query(query, flat_params, fetch_all=True)

            # 创建查找表，用于快速匹配已存在的记录
            existing_dict = {}
            if result["success"] and result["data"]:
                for record in result["data"]:
                    record_id, pkl_name, key_obs_id = record
                    existing_dict[(pkl_name, key_obs_id)] = record_id
                    existing_records.append(record_id)

        # 对每条CSV记录，检查是否已存在于case_pool中
            for item in csv_data:
                key_obs_id = item.get('key_obs_id', 0)
                key = (item['pkl_name'], key_obs_id)
                # 查询是否存在
                if key not in existing_dict:
                    # 如果不存在，准备创建新记录
                    new_item = {
                        'pkl_name': item['pkl_name'],
                        'pkl_dir': item['pkl_dir'],
                        'key_obs_id': int(item.get('key_obs_id', 0)),
                    }

                    # 提取时间戳
                    time_ns_match = re.search(
                        time_ns_pattern, item['pkl_name'])
                    if time_ns_match:
                        new_item['time_ns'] = int(time_ns_match.group(0))
                    else:
                        new_item['time_ns'] = 0

                    # 提取车辆类型
                    vehicle_type_match = re.search(
                        vehicle_type_pattern, item['pkl_name'])
                    if vehicle_type_match:
                        new_item['vehicle_type'] = vehicle_type_match.group(0)
                    else:
                        new_item['vehicle_type'] = 'B10'  # 默认值

                    # 提取 VIN
                    vin_match = re.search(vin_pattern, item['pkl_name'])
                    if vin_match:
                        new_item['vin'] = vin_match.group(0)
                    else:
                        new_item['vin'] = 'unknown'

                    # 添加路径范围
                    if 'path_range_start' in item and item['path_range_start'].strip():
                        new_item['path_range_start'] = int(
                            item['path_range_start'])
                    else:
                        new_item['path_range_start'] = 0

                    if 'path_range_end' in item and item['path_range_end'].strip():
                        new_item['path_range_end'] = int(
                            item['path_range_end'])
                    else:
                        new_item['path_range_end'] = 10

                    new_records.append(new_item)

        # 创建不存在的记录
        new_ids = []
        if new_records:
            insert_result = insert_many_evaluation_case_pool(new_records)
            if not insert_result["success"]:
                # 如果插入失败，也添加已有记录并返回部分成功
                if existing_records:
                    add_cases_to_evaluation_case_pool(
                        evaluation_set_id, existing_records)

                return {
                    "success": False,
                    "error": insert_result["error"],
                    "message": f"成功关联 {len(existing_records)} 个已有案例，但 {len(new_records)} 个新案例创建失败",
                    "evaluation_set_id": evaluation_set_id
                }

            new_ids = insert_result["ids"]

        # 关联所有案例到评测集
        all_case_ids = existing_records + \
            [id for id in new_ids if id is not None]

        if all_case_ids:
            add_result = add_cases_to_evaluation_case_pool(
                evaluation_set_id, all_case_ids)
            if not add_result["success"]:
                raise HTTPException(
                    status_code=500, detail=add_result["error"])

        # 返回成功结果
        return {
            "success": True,
            "message": f"成功创建评测集，包含 {len(existing_records)} 个已有案例和 {len(new_ids)} 个新案例",
            "evaluation_set_id": evaluation_set_id,
            "total_cases": len(all_case_ids)
        }

    except Exception as e:
        # 如果发生错误但评测集已经创建，返回部分成功信息
        if 'evaluation_set_id' in locals():
            return {
                "success": False,
                "error": str(e),
                "message": "评测集已创建，但添加案例时出错",
                "evaluation_set_id": evaluation_set_id
            }
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # 清理临时文件
        os.unlink(temp_file.name)


@router.post("/evaluation_sets/{evaluation_set_id}/upload_csv")
async def upload_csv_to_existing_evaluation_set(
    evaluation_set_id: int = Path(..., ge=1),
    file: UploadFile = File(...)
):
    """
    向现有评测集上传CSV文件添加案例
    """
    # 检查评测集是否存在
    set_query = "SELECT id, set_name FROM evaluation_set WHERE id = %s"
    set_result = execute_query(set_query, (evaluation_set_id,), fetch_one=True)

    if not set_result["success"] or not set_result["data"]:
        raise HTTPException(status_code=404, detail="评测集不存在")

    # 检查文件类型
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="只接受CSV文件")

    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')

    try:
        # 保存上传的文件
        with temp_file as f:
            shutil.copyfileobj(file.file, f)

        # 解析CSV文件
        csv_data = []
        with open(temp_file.name, mode='r', encoding='utf-8') as f:
            csv_reader = csv.DictReader(f)
            # 验证必填字段
            required_fields = ['pkl_name', 'pkl_dir']
            if not all(field in csv_reader.fieldnames for field in required_fields):
                raise HTTPException(status_code=400,
                                    detail=f"CSV文件必须包含以下列：{', '.join(required_fields)}")

            # 读取CSV数据
            for row in csv_reader:
                csv_data.append(row)

        # 处理CSV数据（与现有逻辑相同）
        existing_records = []
        new_records = []

        search_params = []
        for item in csv_data:
            key_obs_id = item.get('key_obs_id', 0)
            search_params.append((item['pkl_name'], key_obs_id))

        if search_params:
            # 批量查询已存在的记录
            placeholders = ", ".join([f"(%s, %s)"] * len(search_params))
            flat_params = [param for pair in search_params for param in pair]

            query = f"""
            SELECT id, pkl_name, key_obs_id 
            FROM evaluation_case_pool 
            WHERE (pkl_name, key_obs_id) IN ({placeholders})
            """

            result = execute_query(query, flat_params, fetch_all=True)

            existing_dict = {}
            if result["success"] and result["data"]:
                for record in result["data"]:
                    record_id, pkl_name, key_obs_id = record
                    existing_dict[(pkl_name, key_obs_id)] = record_id
                    existing_records.append(record_id)

            # 处理新记录
            for item in csv_data:
                key_obs_id = item.get('key_obs_id', 0)
                key = (item['pkl_name'], key_obs_id)

                if key not in existing_dict:
                    # 创建新记录的逻辑（与现有代码相同）
                    new_item = {
                        'pkl_name': item['pkl_name'],
                        'pkl_dir': item['pkl_dir'],
                        'key_obs_id': int(item.get('key_obs_id', 0)),
                    }

                    # 提取其他字段...（与现有代码相同）
                    time_ns_match = re.search(
                        time_ns_pattern, item['pkl_name'])
                    if time_ns_match:
                        new_item['time_ns'] = int(time_ns_match.group(0))
                    else:
                        new_item['time_ns'] = 0

                    vehicle_type_match = re.search(
                        vehicle_type_pattern, item['pkl_name'])
                    if vehicle_type_match:
                        new_item['vehicle_type'] = vehicle_type_match.group(0)
                    else:
                        new_item['vehicle_type'] = 'B10'

                    vin_match = re.search(vin_pattern, item['pkl_name'])
                    if vin_match:
                        new_item['vin'] = vin_match.group(0)
                    else:
                        new_item['vin'] = 'unknown'

                    if 'path_range_start' in item and item['path_range_start'].strip():
                        new_item['path_range_start'] = int(
                            item['path_range_start'])
                    else:
                        new_item['path_range_start'] = 0

                    if 'path_range_end' in item and item['path_range_end'].strip():
                        new_item['path_range_end'] = int(
                            item['path_range_end'])
                    else:
                        new_item['path_range_end'] = 10

                    new_records.append(new_item)

        # 创建新记录
        new_ids = []
        if new_records:
            insert_result = insert_many_evaluation_case_pool(new_records)
            if not insert_result["success"]:
                raise HTTPException(
                    status_code=500, detail=insert_result["error"])
            new_ids = insert_result["ids"]

        # 关联所有案例到评测集
        all_case_ids = existing_records + \
            [id for id in new_ids if id is not None]

        if all_case_ids:
            add_result = add_cases_to_evaluation_case_pool(
                evaluation_set_id, all_case_ids)
            if not add_result["success"]:
                raise HTTPException(
                    status_code=500, detail=add_result["error"])

        return {
            "success": True,
            "message": f"成功添加 {len(existing_records)} 个已有案例和 {len(new_ids)} 个新案例到评测集",
            "added_cases": len(all_case_ids)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # 清理临时文件
        os.unlink(temp_file.name)


@router.post("/evaluation_sets/{evaluation_set_id}/check-pkl/{pkl_id}")
async def mark_pkl_checked(
    evaluation_set_id: int = Path(..., ge=1),
    pkl_id: int = Path(..., ge=1),
    current_user: Optional[dict] = Depends(get_current_user)
):
    """
    标记PKL文件在指定评测集中为已检查
    """
    try:
        # 检查PKL是否存在于评测集中
        check_query = """
        SELECT 1 FROM evaluation_set_case_pool 
        WHERE evaluation_set_id = %s AND evaluation_case_id = %s
        """
        check_result = execute_query(
            check_query, (evaluation_set_id, pkl_id), fetch_one=True)

        if not check_result["success"] or not check_result["data"]:
            raise HTTPException(status_code=404, detail="PKL不在指定评测集中")

        # 更新evaluation_set_case_pool表的检查状态
        update_query = """
        UPDATE evaluation_set_case_pool 
        SET is_checked = TRUE, 
            checked_at = CURRENT_TIMESTAMP,
            checked_by = %s
        WHERE evaluation_set_id = %s AND evaluation_case_id = %s
        """
        params = (
            current_user.get('employee_id', '') if current_user else 'unknown',
            evaluation_set_id,
            pkl_id
        )

        result = execute_query(update_query, params)

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"标记检查状态失败: {result['error']}")

        return {
            "success": True,
            "message": "PKL已标记为已检查",
            "evaluation_set_id": evaluation_set_id,
            "pkl_id": pkl_id,
            "checked_by": current_user.get('employee_id', '') if current_user else 'unknown'
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/evaluation_sets/{evaluation_set_id}/check-pkl/{pkl_id}")
async def unmark_pkl_checked(
    evaluation_set_id: int = Path(..., ge=1),
    pkl_id: int = Path(..., ge=1)
):
    """
    取消PKL文件在指定评测集中的已检查标记
    """
    try:
        update_query = """
        UPDATE evaluation_set_case_pool 
        SET is_checked = FALSE, 
            checked_at = NULL,
            checked_by = NULL
        WHERE evaluation_set_id = %s AND evaluation_case_id = %s
        """

        result = execute_query(update_query, (evaluation_set_id, pkl_id))

        if not result["success"]:
            raise HTTPException(
                status_code=500, detail=f"取消检查标记失败: {result['error']}")

        return {
            "success": True,
            "message": "PKL检查标记已取消",
            "evaluation_set_id": evaluation_set_id,
            "pkl_id": pkl_id
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/evaluation_sets/{evaluation_set_id}/check-status")
async def get_check_status(evaluation_set_id: int = Path(..., ge=1)):
    """
    获取评测集中PKL的检查状态统计
    """
    try:
        # 查询检查状态统计
        status_query = """
        SELECT 
            COUNT(*) as total_cases,
            SUM(CASE WHEN is_checked = TRUE THEN 1 ELSE 0 END) as checked_cases,
            SUM(CASE WHEN is_checked = FALSE OR is_checked IS NULL THEN 1 ELSE 0 END) as unchecked_cases
        FROM evaluation_set_case_pool 
        WHERE evaluation_set_id = %s
        """

        result = execute_query(
            status_query, (evaluation_set_id,), fetch_one=True)

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        if not result["data"]:
            return {
                "success": True,
                "total_cases": 0,
                "checked_cases": 0,
                "unchecked_cases": 0,
                "check_progress": 0.0
            }

        total, checked, unchecked = result["data"]
        progress = (checked / total * 100) if total > 0 else 0.0

        return {
            "success": True,
            "total_cases": total,
            "checked_cases": checked,
            "unchecked_cases": unchecked,
            "check_progress": round(progress, 2)
        }

    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=str(e))
