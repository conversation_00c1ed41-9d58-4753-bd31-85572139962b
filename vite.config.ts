import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: {
    // hmr: false,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://**************:5006',
        // target: 'http://localhost:4006',
        // target: 'http://localhost:5006',

        changeOrigin: true,
      },
    },
  }
  
})
