-- 路径pair比较标注系统数据库表
-- 创建日期: 2025-07-03

-- 1. 路径pair标注结果表
CREATE TABLE IF NOT EXISTS path_pair_annotation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pkl_id INT NOT NULL COMMENT '关联evaluation_case_pool中的PKL文件ID',
    path_a_index INT NOT NULL COMMENT '路径A的索引',
    path_b_index INT NOT NULL COMMENT '路径B的索引', 
    comparison_result ENUM('A_better', 'B_better', 'indistinguishable') NOT NULL COMMENT '比较结果：A>B, A<B, 不能区分',
    annotator_id VARCHAR(50) NOT NULL COMMENT '标注员ID',
    evaluation_set_id INT NOT NULL COMMENT '关联的评测集ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON>G<PERSON> KEY (pkl_id) REFERENCES evaluation_case_pool(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (evaluation_set_id) REFERENCES evaluation_set(id) ON DELETE CASCADE,
    UNIQUE KEY unique_annotation (pkl_id, path_a_index, path_b_index, annotator_id, evaluation_set_id),
    INDEX idx_pkl_id (pkl_id),
    INDEX idx_annotator_id (annotator_id),
    INDEX idx_evaluation_set_id (evaluation_set_id)
);
CREATE TABLE IF NOT EXISTS path_pair_definition (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pkl_id INT NOT NULL COMMENT '关联evaluation_case_pool中的PKL文件ID',
    evaluation_set_id INT NOT NULL COMMENT '关联的评测集ID',
    pair_index INT NOT NULL COMMENT 'pair在该PKL中的索引（从0开始）',
    path_a_index INT NOT NULL COMMENT '路径A的索引（-1表示GT路径）',
    path_b_index INT NOT NULL COMMENT '路径B的索引',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pkl_id) REFERENCES evaluation_case_pool(id) ON DELETE CASCADE,
    FOREIGN KEY (evaluation_set_id) REFERENCES evaluation_set(id) ON DELETE CASCADE,
    UNIQUE KEY unique_pair (pkl_id, evaluation_set_id, pair_index),
    INDEX idx_pkl_evaluation (pkl_id, evaluation_set_id),
    INDEX idx_path_indices (pkl_id, evaluation_set_id, path_a_index, path_b_index)
);
