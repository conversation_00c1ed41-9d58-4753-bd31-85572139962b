import requests
import json
import os
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"  # 根据你的服务器地址调整
API_ENDPOINT = f"{BASE_URL}/api/annotation/export-pair-annotations"

# 测试用户认证信息（如果需要的话）
TEST_USER_TOKEN = "your_test_token_here"  # 替换为实际的认证token

def test_export_pair_annotations():
    """测试导出路径pair标注结果的接口"""
    
    # 测试数据
    test_cases = [

        {
            "name": "导出JSON格式（不包含冲突）",
            "data": {
                "evaluation_set_id": 69,
                "include_conflicts": False,
                "export_format": "json"
            }
        },
    ]
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    # 如果需要认证，添加认证头
    # if TEST_USER_TOKEN:
    #     headers["Authorization"] = f"Bearer {TEST_USER_TOKEN}"
    
    # 创建下载目录
    # download_dir = "downloaded_exports"
    # os.makedirs(download_dir, exist_ok=True)
    
    for test_case in test_cases:
        print(f"\n{'='*50}")
        print(f"测试: {test_case['name']}")
        print(f"{'='*50}")
        
        try:
            # 发送POST请求
            response = requests.post(
                API_ENDPOINT,
                json=test_case['data'],
                headers=headers,
                stream=True  # 用于下载文件
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                print(response)
                # 从响应头获取文件名
                content_disposition = response.headers.get('Content-Disposition', '')
                filename = None
                
                if 'filename*=UTF-8' in content_disposition:
                    # 处理UTF-8编码的文件名
                    import urllib.parse
                    filename_part = content_disposition.split("filename*=UTF-8''")[1]
                    filename = urllib.parse.unquote(filename_part)
                else:
                    # 默认文件名
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    ext = "json" if test_case['data']['export_format'] == "json" else "csv"
                    filename = f"export_{timestamp}.{ext}"
                
                # 保存文件
                file_path = os.path.join(download_dir, filename)
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                print(f"✅ 文件下载成功: {file_path}")
                print(f"文件大小: {os.path.getsize(file_path)} bytes")
                
                # 如果是JSON文件，尝试验证格式
                if test_case['data']['export_format'] == "json":
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        print(f"JSON格式验证通过，包含 {len(data)} 条记录")
                        
                        # 显示第一条记录的结构（如果存在）
                        if data:
                            print("第一条记录结构:")
                            print(json.dumps(data[0], ensure_ascii=False, indent=2)[:500] + "...")
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON格式验证失败: {e}")
                
                # 如果是CSV文件，尝试验证格式
                elif test_case['data']['export_format'] == "csv":
                    try:
                        import csv
                        with open(file_path, 'r', encoding='utf-8') as f:
                            reader = csv.reader(f)
                            headers = next(reader)
                            row_count = sum(1 for _ in reader)
                        print(f"CSV格式验证通过，包含 {row_count} 条数据记录")
                        print(f"列名: {headers}")
                    except Exception as e:
                        print(f"❌ CSV格式验证失败: {e}")
                        
            else:
                # 处理错误响应
                try:
                    error_data = response.json()
                    print(f"❌ 请求失败: {error_data}")
                except:
                    print(f"❌ 请求失败: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")



if __name__ == "__main__":
    print("开始测试导出路径pair标注结果接口")
    print(f"API端点: {API_ENDPOINT}")
    
    # 测试正常情况
    test_export_pair_annotations()
    
    print(f"\n{'='*50}")
    print("测试完成！")
    print("请检查 'downloaded_exports' 目录中的下载文件")
    print(f"{'='*50}")